/* ../../styles/Chats.css */
.chat-wrapper {
    position: fixed; /* Fixes the entire chat component in place */
    top: 10px; /* Margin from top */
    left: 300px; /* Accounts for sidebar width on desktop */
    right: 10px; /* Margin from right */
    bottom: 10px; /* Margin from bottom */
    display: flex;
    flex-direction: column;
    transition: all 0.4s ease;
    border-radius: 15px;
    overflow: hidden; /* Prevents wrapper from scrolling */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 5; /* Ensures it stays above other content */
}

/* Chat Header */
.chat-header {
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.05);
    flex-shrink: 0; /* Fixed height, no shrinking */
}

/* Chat Title */
.chat-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b82f6;
    letter-spacing: 1px;
}

/* Chat Messages */
.chat-messages {
    flex: 1; /* Takes remaining space between header and input */
    padding: 20px;
    overflow-y: auto; /* Messages scrollable */
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Message Styling */
.message {
    display: flex;
    align-items: flex-end;
    animation: slideIn 0.3s ease;
}

.message-you {
    justify-content: flex-end;
}

.message-friend {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 12px 18px;
    border-radius: 20px;
    position: relative;
    transition: transform 0.2s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.message-bubble:hover {
    transform: scale(1.02);
}

.message-text {
    display: block;
    font-size: 1rem;
    word-wrap: break-word;
}

.message-timestamp {
    display: block;
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 5px;
}

/* Chat Input */
.chat-input-form {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 10px;
    flex-shrink: 0; /* Fixed height, no shrinking */
}

/* Input Field */
.chat-input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 30px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-input:focus {
    outline: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Send Button */
.chat-send-btn {
    padding: 12px 20px;
    border-radius: 50%;
    background: #3b82f6;
    color: #ffffff;
    border: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.chat-send-btn:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.5);
}

/* Animation */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #3b82f6;
}

/* Mobile Adjustments */
@media (max-width: 991px) {
    .chat-wrapper {
        left: 10px; /* No sidebar on mobile, full width */
        top: 70px; /* Accounts for navbar height */
    }
}

/* Messages Component Responsive Styles */
.chat-list-with-panel {
    width: 350px !important;
}

@media (max-width: 1024px) {
    .chat-container {
        flex-direction: column;
        height: 90vh;
    }

    .chat-list {
        width: 100% !important;
        max-height: none;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .chat-panel {
        width: 100% !important;
        display: flex !important;
    }

    .chat-list-with-panel {
        display: none; /* Hide chat list when chat is open on mobile */
    }
}

@media (max-width: 768px) {
    .chat-container {
        height: 95vh;
    }

    .chat-item {
        padding: 10px 15px;
    }

    .chat-messages {
        max-height: 300px;
    }

    .message-bubble {
        max-width: 85%;
    }
}

@media (max-width: 480px) {
    .chat-input-form {
        padding: 15px;
    }

    .chat-input {
        padding: 10px 14px;
        font-size: 0.9rem;
    }

    .chat-send-btn {
        width: 45px;
        height: 45px;
    }

    .chat-title {
        font-size: 1.2rem;
    }

    .user-name {
        font-size: 0.8rem;
    }

    .last-message {
        font-size: 0.7rem;
    }
}