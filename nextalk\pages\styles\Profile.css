/* ../styles/Profile.css */
.skeleton {
    position: relative;
    overflow: hidden;
    background: #ddd;
    border-radius: 8px;
}

.skeleton::after {
    content: "";
    position: absolute;
    top: 0;
    left: -150px;
    height: 100%;
    width: 150px;
    background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.4),
            transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -150px;
    }

    100% {
        left: 100%;
    }
}


.skeleton-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.skeleton-username {
    width: 150px;
    height: 25px;
}

.skeleton-button {
    width: 100px;
    height: 30px;
    border-radius: 5px;
}

.skeleton-icon {
    width: 30px;
    height: 30px;
    border-radius: 5px;
}

.skeleton-text {
    width: 60px;
    height: 20px;
    margin-bottom: 5px;
}

.skeleton-line {
    width: 100%;
    height: 15px;
    margin-top: 10px;
}

.skeleton-line.short {
    width: 70%;
}

/* Profile Page Styles */
.profile-container {
    display: flex;
    justify-content: center;
    padding: 20px;
}

.profile-card:hover {
    box-shadow: 0 6px 20px rgba(255, 77, 77, 0.2);
    /* Subtle red glow on hover */
}

.profile-content {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid gray;
    transition: transform 0.3s ease;
}

.profile-card:hover .profile-avatar {
    transform: scale(1.05);
}

.profile-info {
    flex: 1;
    text-align: left;
}

.profile-username {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 5px;
}

.profile-name {
    font-size: 1.1rem;
    font-weight: 400;
    color: #0f172a;
    margin-bottom: 8px;
}

.profile-bio {
    font-size: 0.9rem;
    color: #4a5568;
    margin-bottom: 10px;
    line-height: 1.4;
}

.profile-stats {
    display: flex;
    gap: 15px;
}

.stat-item {
    font-size: 0.9rem;
    color: #0f172a;
}

.stat-item strong {
    color: #ff4d4d;
}

.ot-but {
    display: none;
}

.edit-button {
    display: block;
    width: 100%;
    padding: 12px;
    background: #ff4d4d;
    color: #f1f5f9;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease, box-shadow 0.3s ease;
}

.edit-button:hover {
    background: #e04343;
    box-shadow: 0 0 15px rgba(255, 77, 77, 0.4);
    /* Glowing effect */
}

.edit-button:active {
    box-shadow: none;
}

/* Edit Profile Page Styles (unchanged) */
.edit-profile-layout {
    display: flex;
    height: 98vh;
    overflow: hidden;
}

.sidebar-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.sidebar-section {
    margin-bottom: 20px;
}

.sidebar-section-title {
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.sidebar-section-content {
    font-size: 0.9rem;
    color: gray;
    line-height: 1.5;
}

.sidebar-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.sidebar-button {
    padding: 10px 15px;
    background: #3a3a3a;
    color: #f1f5f9;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.1s ease;
}

.sidebar-button:hover {
    background: #4a4a4a;
    transform: translateX(5px);
}

.sidebar-button:active {
    transform: translateX(0);
}

.sidebar-button.active {
    background: #3b82f6;
    cursor: default;
}

.sidebar-button.active:hover {
    background: #2563eb;
    transform: none;
}

.edit-profile-card {
    border-radius: 15px;
    padding: 40px;
    width: 100%;
    max-width: 600px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.edit-profile-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 30px;
}

.edit-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 1rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    padding: 10px;
    border: none;
    border-radius: 8px;
    background: #e2e8f0;
    font-size: 1rem;
    transition: background 0.3s ease, transform 0.1s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    background: #d1d5db;
    outline: none;
    transform: scale(1.01);
}

.form-group textarea {
    resize: vertical;
}

.avatar-preview {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-top: 10px;
    border: 2px solid #ff4d4d;
    transition: transform 0.3s ease;
}

.avatar-preview:hover {
    transform: scale(1.1);
}

.form-instructions {
    margin-top: 20px;
    font-size: 0.1rem;
    color: #4a5568;
}

.visibility-link {
    color: #ff4d4d;
    cursor: pointer;
    transition: color 0.3s ease;
}

.visibility-link:hover {
    color: #e04343;
    text-decoration: underline;
}

.form-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 20px;
}

.submit-button,
.cancel-button {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.1s ease;
}

.submit-button {
    background: #3b82f6;
    color: #f1f5f9;
}

.submit-button:hover {
    background: #2563eb;
    transform: translateY(-2px);
}

.submit-button:active {
    transform: translateY(0);
}

.cancel-button {
    background: #3a3a3a;
    color: #f1f5f9;
}

.cancel-button:hover {
    background: #4a4a4a;
    transform: translateY(-2px);
}

.cancel-button:active {
    transform: translateY(0);
}

/* Shared Styles */
.loading,
.error {
    text-align: center;
    font-size: 1.2rem;
    color: #f1f5f9;
    padding: 20px;
}

.error {
    color: #ff4d4d;
}

@media (min-width: 770px) {
    .edit-profile-main {
        flex: 1;
        padding: 40px;
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    .edit-profile-sidebar {
        width: 350px;
        border-right: 1px solid #3a3a3a;
        padding: 20px;
        flex-shrink: 0;
        overflow-y: auto;
    }

    .p-car {
        justify-content: space-between;
        width: 600px;
    }

    .edit-profile-main {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        scroll-behavior: smooth;
    }

    .p-card {
        justify-content: space-around;
        gap: 40px;
    }

    .profile-card {
        background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
        border-radius: 15px;
        padding: 25px;
        width: 700px;
        /* Wider card to accommodate the new layout */
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: box-shadow 0.3s ease;
    }

    .edit-profile-sidebar {
        display: block;
    }
}

/* Responsive Design */
@media (max-width: 768px) {

    html,
    body {
        overflow-x: hidden;
        overflow-y: auto;
    }

    .edit-profile-sidebar {
        display: none;
    }

    .p-car {
        gap: 14px;
    }

    .p-card {
        gap: 20px;
    }
    .edit-profile-main {
        padding: 4px;
    }
    .profile-card {
        padding: 20px;
        max-width: 100%;
        background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
        /* Subtle gradient */
        border-radius: 15px;
        padding: 25px;
        width: 100%;
        /* Wider card to accommodate the new layout */
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: box-shadow 0.3s ease;
    }

    .profile-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .profile-avatar {
        width: 60px;
        height: 60px;
    }

    .ot-but {
        display: block;
    }

    .ot-butt {
        display: none;
    }

    .profile-username {
        font-size: 1.4rem;
    }

    .profile-name {
        font-size: 1rem;
    }

    .profile-stats {
        flex-direction: column;
        gap: 8px;
    }

    .edit-profile-layout {
        flex-direction: column;
    }

    .edit-profile-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #3a3a3a;
    }

   .edit-profile-layout {
    display: flex;
    flex-direction: column; /* Important for stacking on mobile */
    height: auto;
    min-height: 100vh;
    overflow: visible; /* Avoid blocking scroll */
}


    .edit-profile-card {
        padding: 8px;
    }

    .edit-profile-title {
        font-size: 1.5rem;
    }
}

/* Modal Styles */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.7); /* Semi-transparent dark overlay */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    border-radius: 15px;
    padding: 25px;
    width: 100%;
    color: white;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease-out forwards;
    text-align: center;
    background:  rgb(22, 33, 62);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.modal-text {
    font-size: 1rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

.modal-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.modal-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.modal-btn-success {
    background: #3b82f6;
    color: #f1f5f9;
}

.modal-btn-success:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 77, 77, 0.3);
}

.modal-btn-success:active {
    transform: translateY(0);
    box-shadow: none;
}

.modal-btn-cancel {
    background: #3a3a3a;
    color: #f1f5f9;
}

.modal-btn-cancel:hover {
    background: #4a4a4a;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(58, 58, 58, 0.3);
}

.modal-btn-cancel:active {
    transform: translateY(0);
    box-shadow: none;
}

.custom-alert-container {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 9999;
  animation: slideIn 0.5s ease forwards;
}

.custom-alert {
  background-color: #dc3545;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  font-weight: bold;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
  position: relative;
  min-width: 300px;
}

.timer-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 5px;
  background-color: #ffffff;
  animation: countdownBar 4s linear forwards;
  border-radius: 0 0 8px 8px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes countdownBar {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

.no-followers-container {
  animation: fadeInUp 0.6s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-wrapper {
  animation: iconPop 0.5s ease-in-out;
}

@keyframes iconPop {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
