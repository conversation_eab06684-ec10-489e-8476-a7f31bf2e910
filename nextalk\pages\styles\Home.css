/* ../../styles/Home.css */

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a1a, #2c2c2c);
    color: #ffffff;
    font-family: 'Inter', sans-serif;
    font-size: 1.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    animation: pulse 1.5s ease-in-out infinite;
  }
  
  .loading::before {
    content: '';
    width: 12px;
    height: 12px;
    background: #00ff88;
    border-radius: 50%;
    margin-right: 10px;
    animation: bounce 0.6s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }
  
  @keyframes bounce {
    0% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-8px);
    }
  }

  .error {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #2c0b0b, #3d1c1c);
    color: #ff4d4d;
    font-family: 'Inter', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    text-align: center;
    padding: 20px;
    border-radius: 12px;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    animation: shake 0.4s ease-in-out, fadeIn 0.8s ease-out;
  }
  
  .error::before {
    content: '⚠';
    display: inline-block;
    margin-right: 10px;
    font-size: 1.5rem;
    animation: pulseIcon 1.2s ease-in-out infinite;
  }
  
  @keyframes shake {
    0%, 100% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(-5px);
    }
    75% {
      transform: translateX(5px);
    }
  }
  
  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes pulseIcon {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.2);
    }
  }
  
.home-container {
    padding: 20px;
    min-height: calc(100vh - 60px);
    overflow-y: auto;
    perspective: 1000px;
}

.home-title {
    font-size: 1.8rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 40px;
    color: #3b82f6;
    text-transform: uppercase;
    letter-spacing: 2px;
    animation: pulse 2s infinite;
}

.home-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr; /* 3 columns on desktop */
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.notification-section, .user-section {
    padding: 20px;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 25px;
    color: inherit;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    border-radius: 2px;
    animation: slideInLeft 1s ease;
}

.notification-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px; /* Taller on desktop */
    overflow-y: auto;
}

.notification-card {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
    cursor: pointer;
}

.notification-card:hover {
    transform: translateX(10px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    background: rgba(59, 130, 246, 0.1);
}

.notif-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
    border: 2px solid #3b82f6;
    transition: transform 0.3s ease;
}

.notification-card:hover .notif-avatar {
    transform: scale(1.1);
}

.notif-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.notif-message {
    font-size: 1rem;
    font-weight: 500;
}

.notif-time {
    font-size: 0.8rem;
    color: #60a5fa;
    margin-top: 5px;
}

.user-list {
    display: grid;
    grid-template-columns: 1fr; /* Single column within each section */
    gap: 20px;
}

.user-card {
    position: relative;
    width: 100%;
    height: 80px;
    transform-style: preserve-3d;
    transition: transform 0.6s ease;
    cursor: pointer;
}

.user-card.flipped {
    transform: rotateY(180deg);
}

.card-front, .card-back {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.3s ease;
}

.card-front:hover, .card-back:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.card-front {
    z-index: 2;
}

.card-back {
    transform: rotateY(180deg);
    justify-content: center;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 20px;
    object-fit: cover;
    border: 3px solid #3b82f6;
    transition: transform 0.4s ease, border-color 0.3s ease;
}

.user-card:hover .user-avatar {
    transform: scale(1.15) rotate(5deg);
    border-color: #60a5fa;
}

.user-name {
    flex: 1;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.user-bio {
    font-size: 1rem;
    font-style: italic;
    color: #60a5fa;
    text-align: center;
}

.follow-btn, .accept-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 25px;
    font-size: 0.95rem;
    font-weight: 600;
    color: #ffffff;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.follow-btn:hover, .accept-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5);
}

.follow-btn:active, .accept-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);
}

.simulate-buttons {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

.simulate-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 20px;
    background: linear-gradient(45deg, #3b82f6, #60a5fa);
    color: #ffffff;
    font-size: 0.9rem;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.simulate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
}

.simulate-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);
}

.loading, .error {
    text-align: center;
    font-size: 1.5rem;
    padding: 50px;
    color: #3b82f6;
    animation: fadeIn 0.5s ease;
}

.error {
    color: #ef4444;
    font-weight: 500;
}

/* Animations */
@keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
@keyframes slideInLeft { from { width: 0; } to { width: 100%; } }
@keyframes bounceIn { 0% { transform: scale(0.8); opacity: 0; } 60% { transform: scale(1.05); opacity: 1; } 100% { transform: scale(1); } }

/* Responsive Design */
@media (max-width: 768px) {
    .home-grid {
        grid-template-columns: 1fr; /* Single column on mobile */
    }

    .home-title { font-size: 1rem; }
    .section-title { font-size: 1.4rem; }
    
    .notification-list { max-height: 300px; }
    .notification-card { padding: 10px; }
    .notif-avatar { width: 35px; height: 35px; }
    .notif-message { font-size: 0.9rem; }
    .notif-time { font-size: 0.7rem; }
    
    .user-card { height: 70px; }
    .user-avatar { width: 45px; height: 45px; margin-right: 15px; }
    .user-name { font-size: 1rem; }
    .user-bio { font-size: 0.9rem; }
    .follow-btn, .accept-btn { padding: 6px 16px; font-size: 0.85rem; }
    .simulate-btn { padding: 5px 10px; font-size: 0.8rem; }
    .home-container {
    padding: 2px;
    position: relative;
}
}

.notification-card, .user-card { animation: bounceIn 0.5s ease; }


.home-content {
    display: flex;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.image-section {
    flex: 1;
}

.image-list {
    display: flex;
    overflow-x: auto;
    gap: 15px;
    padding: 10px 0;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.image-card {
    flex: 0 0 auto;
    scroll-snap-align: start;
    text-align: center;
    position: relative;
}

.image-wrapper {
    position: relative;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}


.image-item {
    width: 80px;
    height: 80px;
    padding: 2.5px;
    border-radius: 50%;
    object-fit: cover;
    border: 2.5px solid rgb(49, 49, 191);
    transition: border-image 0.3s ease;
}


.image-username {
    display: block;
    font-size: 0.8rem;
    margin-top: 5px;
    transition: color 0.3s ease;
}


.tooltip {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    display: none;
    flex-direction: column;
    text-align: center;
    z-index: 10;
}

.image-wrapper:hover .tooltip {
    display: flex;
}

.suggested-section {
    flex: 0 0 300px;
    border-radius: 15px;
    padding: 15px;
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3), -5px -5px 15px rgba(255, 255, 255, 0.1);
}

.suggested-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
}

.see-all {
    font-size: 0.9rem;
    color: #1e90ff;
    text-decoration: none;
}

.see-all:hover {
    text-decoration: none;
}

.follow-all-btn {
    width: 100%;
    padding: 8px;
    border: none;
    border-radius: 5px;
    background: linear-gradient(45deg, #3b82f6, #60a5fa);
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    margin-bottom: 15px;
    transition: background 0.3s ease, transform 0.3s ease;
}

.follow-all-btn:hover {
    background: linear-gradient(45deg, #033483, #054a9d);
    transform: scale(1.02);
}

.suggested-grid {
    display: grid;
    grid-template-columns: 1fr;
    scroll-behavior: smooth;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}
::-webkit-scrollbar{
    display: none;
}
.suggested-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: default;
    padding: 15px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.suggested-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.suggested-image-wrapper {
    position: relative;
    margin-bottom: 10px;
}

.suggested-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid;
    transition: transform 0.3s ease;
}

.suggested-card:hover .suggested-image {
    transform: scale(1.1);
}

.suggested-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    text-align: center;
}

.suggested-name {
    font-size: 0.9rem;
    font-weight: 500;
}

.suggested-followed-by {
    font-size: 0.75rem;
    color: #b0b0b0;
}

.follow-btn {
    padding: 5px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    color: white;
    font-size: 0.9rem;
    background: linear-gradient(45deg, #3b82f6, #60a5fa);
    transition: transform 0.3s ease, background 0.3s ease;
    margin-top: 5px;
}

.follow-btn:hover {
    transform: scale(1.05);
    background: linear-gradient(45deg, #2563eb, #3b82f6);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  
  /* Start with hidden and slightly moved up */
  opacity: 0;
  transform: translateY(-20px);
  
  /* Animate when appearing */
  animation: fadeSlideIn 0.3s forwards;
  z-index: 1000;
}

@keyframes fadeSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
    background: #1e293b;
    border-radius: 15px;
    padding: 20px;
    max-width: 400px;
    width: 90%;
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

.modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
}

.modal-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.modal-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border-radius: 50%;
    border: 2px solid;
}

.modal-body h3 {
    margin: 10px 0 5px;
    font-size: 1.2rem;
    color: #b0b0b0;
}

.modal-body p {
    font-size: 0.9rem;
    color: #b0b0b0;
    text-align: center;
}

/* Animations */
@keyframes glow {
    from {
        box-shadow: 0 0 10px #00ffcc, 0 0 20px #00ffcc, 0 0 30px #00ffcc;
    }
    to {
        box-shadow: 0 0 20px #00ffcc, 0 0 30px #00ffcc, 0 0 40px #00ffcc;
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .home-content {
        flex-direction: column;
    }

    .image-list {
        display: flex;
        overflow-x: auto;
        gap: 10px;
        -webkit-overflow-scrolling: touch;
    }

    .image-card {
        flex: 0 0 calc(25% - 10px); /* 4 images */
    }

    .image-item {
        width: 80px;
        height: 80px;
    }

    .suggested-section {
        flex: 1;
    }
}

/* Desktop Responsiveness */
@media (min-width: 769px) {
    .image-list {
        display: flex;
        overflow-x: auto;
        gap: 15px;
    }

    .image-card {
        flex: 0 0 calc(14.28% - 15px); /* 7 images */
    }

    .image-item {
        width: 80px;
        height: 80px;
    }
}

/* Hide scrollbar but keep functionality */
.image-list::-webkit-scrollbar {
    display: none;
}

.image-list {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.image-card.session-user {
    position: relative;
    margin-right: 1.2rem;
    padding-right: 1rem;
}

.image-card.session-user::after {
    content: "";
    position: absolute;
    top: 5%;
    right: 0;
    height: 80%;
    width: 2px;
    background: linear-gradient(to bottom, #4f46e5, #9333ea); /* Styled look */
    border-radius: 2px;
}

