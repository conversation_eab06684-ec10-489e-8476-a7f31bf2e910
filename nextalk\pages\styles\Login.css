
.login-container {
    display: flex;
    color: #333;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
}

/* Wrapper with Animation */
.login-wrapper {
    background: #fff;
    border-radius: 20px;
    overflow: hidden;
    width: 100%;
    max-width: 1000px;
    animation: slideIn 0.8s ease-out forwards;
    /* Slide-in animation */
}

/* Slide-In Animation */
@keyframes slideIn {
    0% {
        transform: translateY(100vh);
        /* Starts from bottom of screen */
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        /* Ends at natural position */
        opacity: 1;
    }
}

/* Login Section */
.login-section {
    background: #fff;
    padding: 2rem;
}

.alert {
    border-radius: 10px;
    padding: 12px;
    font-size: 1rem;
    animation: fadeIn 0.5s ease-in;
    margin-bottom: 20px;
}

/* Create Account Section */
.create-section {
    color: #fff;
    position: relative;
    overflow: hidden;
    padding: 2rem;
}

.create-section1 {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.Randomimages1 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.rand {
    text-align: right;
    cursor: pointer;
}


/* Form Inputs */
.form-control {
    border-radius: 10px;
    padding: 12px;
    font-size: 1.1rem;
    border: 2px solid #e0e0e0;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 8px rgba(102, 126, 234, 0.3);
}

/* Checkbox */
.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Login Button */
.login-btn {
    background: linear-gradient(90deg, #667eea, #764ba2);
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-size: 1.2rem;
    font-weight: 600;
    transition: background 0.3s ease, transform 0.2s ease;
}

.login-btn:hover {
    background: linear-gradient(90deg, #764ba2, #667eea);
    transform: scale(1.05);
}

/* Create Account Button */
.create-btn {
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.create-btn:hover {
    background: #fff;
    color: #667eea;
    transform: scale(1.05);
}

/* Form Label */
.form-label {
    color: #333;
    font-family: 'Poppins', sans-serif;
}


/* Responsive Design */
@media (max-width: 767.98px) {
    
    .login-wrapper {
        max-width: 100%;
        animation: slideInMobile 0.6s ease-out forwards;
        /* Faster animation for mobile */
    }

    .login-section,
    .create-section {
        padding: 1.5rem;
        /* Reduced padding */
    }

    .login-btn,
    .create-btn {
        font-size: 1rem;
        /* Smaller buttons */
    }

    .form-control {
        font-size: 1rem;
        /* Smaller inputs */
    }
}

@media (min-width: 767.98px) {
    .login-container {
        background: linear-gradient(135deg, #667eea, #764ba2);
        margin-top: 80px;
    }
    
    .create-section1 {
        margin-top: 120px;
    }
}

@media (max-width: 575.98px) {
    .create-section1 {
        margin-top: 20px;
        height: 100%;
    }

    .login-wrapper {
        border-radius: 1px;
    }

    .login-section,
    .create-section {
        padding: 1rem;
        /* Even less padding */
    }
    .create-section{
        height: 300px;
    }

    .login-btn,
    .create-btn {
        width: 100%;
        /* Full-width buttons */
    }
}

/* Animation for Mobile */
@keyframes slideInMobile {
    0% {
        transform: translateY(50vh);
        /* Shorter distance for smaller screens */
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}
.custom-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-loader-overlay svg {
  height: 150px;
  width: 150px;
}

.loading-text {
  color: #fff;
  margin-top: 20px;
  font-weight: bold;
  font-size: 1.2rem;
}
