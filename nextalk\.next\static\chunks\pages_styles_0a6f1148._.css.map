{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Login.css"], "sourcesContent": ["\r\n.login-container {\r\n    display: flex;\r\n    color: #333;\r\n    justify-content: center;\r\n    align-items: center;\r\n    height: 100%;\r\n    width: 100%;\r\n}\r\n\r\n/* Wrapper with Animation */\r\n.login-wrapper {\r\n    background: #fff;\r\n    border-radius: 20px;\r\n    overflow: hidden;\r\n    width: 100%;\r\n    max-width: 1000px;\r\n    animation: slideIn 0.8s ease-out forwards;\r\n    /* Slide-in animation */\r\n}\r\n\r\n/* Slide-In Animation */\r\n@keyframes slideIn {\r\n    0% {\r\n        transform: translateY(100vh);\r\n        /* Starts from bottom of screen */\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateY(0);\r\n        /* Ends at natural position */\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Login Section */\r\n.login-section {\r\n    background: #fff;\r\n    padding: 2rem;\r\n}\r\n\r\n.alert {\r\n    border-radius: 10px;\r\n    padding: 12px;\r\n    font-size: 1rem;\r\n    animation: fadeIn 0.5s ease-in;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n/* Create Account Section */\r\n.create-section {\r\n    color: #fff;\r\n    position: relative;\r\n    overflow: hidden;\r\n    padding: 2rem;\r\n}\r\n\r\n.create-section1 {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    position: relative;\r\n}\r\n\r\n.Randomimages1 {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.rand {\r\n    text-align: right;\r\n    cursor: pointer;\r\n}\r\n\r\n\r\n/* Form Inputs */\r\n.form-control {\r\n    border-radius: 10px;\r\n    padding: 12px;\r\n    font-size: 1.1rem;\r\n    border: 2px solid #e0e0e0;\r\n    transition: border-color 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.form-control:focus {\r\n    border-color: #667eea;\r\n    box-shadow: 0 0 8px rgba(102, 126, 234, 0.3);\r\n}\r\n\r\n/* Checkbox */\r\n.form-check-input:checked {\r\n    background-color: #667eea;\r\n    border-color: #667eea;\r\n}\r\n\r\n/* Login Button */\r\n.login-btn {\r\n    background: linear-gradient(90deg, #667eea, #764ba2);\r\n    border: none;\r\n    border-radius: 10px;\r\n    padding: 12px;\r\n    font-size: 1.2rem;\r\n    font-weight: 600;\r\n    transition: background 0.3s ease, transform 0.2s ease;\r\n}\r\n\r\n.login-btn:hover {\r\n    background: linear-gradient(90deg, #764ba2, #667eea);\r\n    transform: scale(1.05);\r\n}\r\n\r\n/* Create Account Button */\r\n.create-btn {\r\n    border-radius: 10px;\r\n    padding: 12px 24px;\r\n    font-size: 1.1rem;\r\n    font-weight: 600;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.create-btn:hover {\r\n    background: #fff;\r\n    color: #667eea;\r\n    transform: scale(1.05);\r\n}\r\n\r\n/* Form Label */\r\n.form-label {\r\n    color: #333;\r\n    font-family: 'Poppins', sans-serif;\r\n}\r\n\r\n\r\n/* Responsive Design */\r\n@media (max-width: 767.98px) {\r\n    \r\n    .login-wrapper {\r\n        max-width: 100%;\r\n        animation: slideInMobile 0.6s ease-out forwards;\r\n        /* Faster animation for mobile */\r\n    }\r\n\r\n    .login-section,\r\n    .create-section {\r\n        padding: 1.5rem;\r\n        /* Reduced padding */\r\n    }\r\n\r\n    .login-btn,\r\n    .create-btn {\r\n        font-size: 1rem;\r\n        /* Smaller buttons */\r\n    }\r\n\r\n    .form-control {\r\n        font-size: 1rem;\r\n        /* Smaller inputs */\r\n    }\r\n}\r\n\r\n@media (min-width: 767.98px) {\r\n    .login-container {\r\n        background: linear-gradient(135deg, #667eea, #764ba2);\r\n        margin-top: 80px;\r\n    }\r\n    \r\n    .create-section1 {\r\n        margin-top: 120px;\r\n    }\r\n}\r\n\r\n@media (max-width: 575.98px) {\r\n    .create-section1 {\r\n        margin-top: 20px;\r\n        height: 100%;\r\n    }\r\n\r\n    .login-wrapper {\r\n        border-radius: 1px;\r\n    }\r\n\r\n    .login-section,\r\n    .create-section {\r\n        padding: 1rem;\r\n        /* Even less padding */\r\n    }\r\n    .create-section{\r\n        height: 300px;\r\n    }\r\n\r\n    .login-btn,\r\n    .create-btn {\r\n        width: 100%;\r\n        /* Full-width buttons */\r\n    }\r\n}\r\n\r\n/* Animation for Mobile */\r\n@keyframes slideInMobile {\r\n    0% {\r\n        transform: translateY(50vh);\r\n        /* Shorter distance for smaller screens */\r\n        opacity: 0;\r\n    }\r\n\r\n    100% {\r\n        transform: translateY(0);\r\n        opacity: 1;\r\n    }\r\n}\r\n.custom-loader-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  height: 100vh;\r\n  width: 100vw;\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n}\r\n\r\n.custom-loader-overlay svg {\r\n  height: 150px;\r\n  width: 150px;\r\n}\r\n\r\n.loading-text {\r\n  color: #fff;\r\n  margin-top: 20px;\r\n  font-weight: bold;\r\n  font-size: 1.2rem;\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;AAUA;;;;;;;;;AAWA;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AAQA;;;;;AAOA;;;;;;;;AAQA;;;;;AAMA;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;;AAQA;;;;;;AAOA;;;;;AAOA;EAEI;;;;;EAMA;;;;EAMA;;;;;AAYJ;EACI;;;;;EAKA;;;;;AAKJ;EACI;;;;;EAKA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;AAQJ;;;;;;;;;;;;AAYA;;;;;;;;;;;;;;AAcA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Home.css"], "sourcesContent": ["/* ../../styles/Home.css */\r\n\r\n.loading {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    height: 100vh;\r\n    background: linear-gradient(135deg, #1a1a1a, #2c2c2c);\r\n    color: #ffffff;\r\n    font-family: 'Inter', sans-serif;\r\n    font-size: 1.5rem;\r\n    font-weight: 500;\r\n    text-transform: uppercase;\r\n    letter-spacing: 1.2px;\r\n    animation: pulse 1.5s ease-in-out infinite;\r\n  }\r\n  \r\n  .loading::before {\r\n    content: '';\r\n    width: 12px;\r\n    height: 12px;\r\n    background: #00ff88;\r\n    border-radius: 50%;\r\n    margin-right: 10px;\r\n    animation: bounce 0.6s ease-in-out infinite alternate;\r\n  }\r\n\r\n  @keyframes pulse {\r\n    0%, 100% {\r\n      opacity: 1;\r\n    }\r\n    50% {\r\n      opacity: 0.7;\r\n    }\r\n  }\r\n  \r\n  @keyframes bounce {\r\n    0% {\r\n      transform: translateY(0);\r\n    }\r\n    100% {\r\n      transform: translateY(-8px);\r\n    }\r\n  }\r\n\r\n  .error {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    min-height: 100vh;\r\n    background: linear-gradient(135deg, #2c0b0b, #3d1c1c);\r\n    color: #ff4d4d;\r\n    font-family: 'Inter', sans-serif;\r\n    font-size: 1.25rem;\r\n    font-weight: 600;\r\n    text-align: center;\r\n    padding: 20px;\r\n    border-radius: 12px;\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);\r\n    animation: shake 0.4s ease-in-out, fadeIn 0.8s ease-out;\r\n  }\r\n  \r\n  .error::before {\r\n    content: '⚠';\r\n    display: inline-block;\r\n    margin-right: 10px;\r\n    font-size: 1.5rem;\r\n    animation: pulseIcon 1.2s ease-in-out infinite;\r\n  }\r\n  \r\n  @keyframes shake {\r\n    0%, 100% {\r\n      transform: translateX(0);\r\n    }\r\n    25% {\r\n      transform: translateX(-5px);\r\n    }\r\n    75% {\r\n      transform: translateX(5px);\r\n    }\r\n  }\r\n  \r\n  @keyframes fadeIn {\r\n    0% {\r\n      opacity: 0;\r\n      transform: scale(0.9);\r\n    }\r\n    100% {\r\n      opacity: 1;\r\n      transform: scale(1);\r\n    }\r\n  }\r\n  \r\n  @keyframes pulseIcon {\r\n    0%, 100% {\r\n      transform: scale(1);\r\n    }\r\n    50% {\r\n      transform: scale(1.2);\r\n    }\r\n  }\r\n  \r\n.home-container {\r\n    padding: 20px;\r\n    min-height: calc(100vh - 60px);\r\n    overflow-y: auto;\r\n    perspective: 1000px;\r\n}\r\n\r\n.home-title {\r\n    font-size: 1.8rem;\r\n    font-weight: 800;\r\n    text-align: center;\r\n    margin-bottom: 40px;\r\n    color: #3b82f6;\r\n    text-transform: uppercase;\r\n    letter-spacing: 2px;\r\n    animation: pulse 2s infinite;\r\n}\r\n\r\n.home-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr 1fr; /* 3 columns on desktop */\r\n    gap: 20px;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.notification-section, .user-section {\r\n    padding: 20px;\r\n    border-radius: 15px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.section-title {\r\n    font-size: 1.8rem;\r\n    font-weight: 700;\r\n    margin-bottom: 25px;\r\n    color: inherit;\r\n    position: relative;\r\n    display: inline-block;\r\n}\r\n\r\n.section-title::after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: -5px;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 3px;\r\n    background: linear-gradient(90deg, #3b82f6, #60a5fa);\r\n    border-radius: 2px;\r\n    animation: slideInLeft 1s ease;\r\n}\r\n\r\n.notification-list {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    max-height: 400px; /* Taller on desktop */\r\n    overflow-y: auto;\r\n}\r\n\r\n.notification-card {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 15px;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.notification-card:hover {\r\n    transform: translateX(10px);\r\n    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\r\n    background: rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.notif-avatar {\r\n    width: 40px;\r\n    height: 40px;\r\n    border-radius: 50%;\r\n    margin-right: 15px;\r\n    object-fit: cover;\r\n    border: 2px solid #3b82f6;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.notification-card:hover .notif-avatar {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.notif-content {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.notif-message {\r\n    font-size: 1rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.notif-time {\r\n    font-size: 0.8rem;\r\n    color: #60a5fa;\r\n    margin-top: 5px;\r\n}\r\n\r\n.user-list {\r\n    display: grid;\r\n    grid-template-columns: 1fr; /* Single column within each section */\r\n    gap: 20px;\r\n}\r\n\r\n.user-card {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 80px;\r\n    transform-style: preserve-3d;\r\n    transition: transform 0.6s ease;\r\n    cursor: pointer;\r\n}\r\n\r\n.user-card.flipped {\r\n    transform: rotateY(180deg);\r\n}\r\n\r\n.card-front, .card-back {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    backface-visibility: hidden;\r\n    border-radius: 12px;\r\n    padding: 15px;\r\n    display: flex;\r\n    align-items: center;\r\n    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);\r\n    transition: box-shadow 0.3s ease;\r\n}\r\n\r\n.card-front:hover, .card-back:hover {\r\n    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.card-front {\r\n    z-index: 2;\r\n}\r\n\r\n.card-back {\r\n    transform: rotateY(180deg);\r\n    justify-content: center;\r\n}\r\n\r\n.user-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n    margin-right: 20px;\r\n    object-fit: cover;\r\n    border: 3px solid #3b82f6;\r\n    transition: transform 0.4s ease, border-color 0.3s ease;\r\n}\r\n\r\n.user-card:hover .user-avatar {\r\n    transform: scale(1.15) rotate(5deg);\r\n    border-color: #60a5fa;\r\n}\r\n\r\n.user-name {\r\n    flex: 1;\r\n    font-size: 1.2rem;\r\n    font-weight: 600;\r\n    letter-spacing: 0.5px;\r\n}\r\n\r\n.user-bio {\r\n    font-size: 1rem;\r\n    font-style: italic;\r\n    color: #60a5fa;\r\n    text-align: center;\r\n}\r\n\r\n.follow-btn, .accept-btn {\r\n    padding: 8px 20px;\r\n    border: none;\r\n    border-radius: 25px;\r\n    font-size: 0.95rem;\r\n    font-weight: 600;\r\n    color: #ffffff;\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.follow-btn:hover, .accept-btn:hover {\r\n    transform: translateY(-3px);\r\n    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5);\r\n}\r\n\r\n.follow-btn:active, .accept-btn:active {\r\n    transform: translateY(1px);\r\n    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.simulate-buttons {\r\n    margin-top: 15px;\r\n    display: flex;\r\n    gap: 10px;\r\n    justify-content: center;\r\n}\r\n\r\n.simulate-btn {\r\n    padding: 6px 12px;\r\n    border: none;\r\n    border-radius: 20px;\r\n    background: linear-gradient(45deg, #3b82f6, #60a5fa);\r\n    color: #ffffff;\r\n    font-size: 0.9rem;\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.simulate-btn:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);\r\n}\r\n\r\n.simulate-btn:active {\r\n    transform: translateY(1px);\r\n    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\r\n}\r\n\r\n.loading, .error {\r\n    text-align: center;\r\n    font-size: 1.5rem;\r\n    padding: 50px;\r\n    color: #3b82f6;\r\n    animation: fadeIn 0.5s ease;\r\n}\r\n\r\n.error {\r\n    color: #ef4444;\r\n    font-weight: 500;\r\n}\r\n\r\n/* Animations */\r\n@keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }\r\n@keyframes slideInLeft { from { width: 0; } to { width: 100%; } }\r\n@keyframes bounceIn { 0% { transform: scale(0.8); opacity: 0; } 60% { transform: scale(1.05); opacity: 1; } 100% { transform: scale(1); } }\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n    .home-grid {\r\n        grid-template-columns: 1fr; /* Single column on mobile */\r\n    }\r\n\r\n    .home-title { font-size: 1rem; }\r\n    .section-title { font-size: 1.4rem; }\r\n    \r\n    .notification-list { max-height: 300px; }\r\n    .notification-card { padding: 10px; }\r\n    .notif-avatar { width: 35px; height: 35px; }\r\n    .notif-message { font-size: 0.9rem; }\r\n    .notif-time { font-size: 0.7rem; }\r\n    \r\n    .user-card { height: 70px; }\r\n    .user-avatar { width: 45px; height: 45px; margin-right: 15px; }\r\n    .user-name { font-size: 1rem; }\r\n    .user-bio { font-size: 0.9rem; }\r\n    .follow-btn, .accept-btn { padding: 6px 16px; font-size: 0.85rem; }\r\n    .simulate-btn { padding: 5px 10px; font-size: 0.8rem; }\r\n    .home-container {\r\n    padding: 2px;\r\n    position: relative;\r\n}\r\n}\r\n\r\n.notification-card, .user-card { animation: bounceIn 0.5s ease; }\r\n\r\n\r\n.home-content {\r\n    display: flex;\r\n    gap: 20px;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n}\r\n\r\n.image-section {\r\n    flex: 1;\r\n}\r\n\r\n.image-list {\r\n    display: flex;\r\n    overflow-x: auto;\r\n    gap: 15px;\r\n    padding: 10px 0;\r\n    scroll-snap-type: x mandatory;\r\n    -webkit-overflow-scrolling: touch;\r\n    scroll-behavior: smooth;\r\n}\r\n\r\n.image-card {\r\n    flex: 0 0 auto;\r\n    scroll-snap-align: start;\r\n    text-align: center;\r\n    position: relative;\r\n}\r\n\r\n.image-wrapper {\r\n    position: relative;\r\n    cursor: pointer;\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n\r\n.image-item {\r\n    width: 80px;\r\n    height: 80px;\r\n    padding: 2.5px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border: 2.5px solid rgb(49, 49, 191);\r\n    transition: border-image 0.3s ease;\r\n}\r\n\r\n\r\n.image-username {\r\n    display: block;\r\n    font-size: 0.8rem;\r\n    margin-top: 5px;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n\r\n.tooltip {\r\n    position: absolute;\r\n    top: -60px;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n    color: #fff;\r\n    padding: 5px 10px;\r\n    border-radius: 5px;\r\n    font-size: 0.8rem;\r\n    display: none;\r\n    flex-direction: column;\r\n    text-align: center;\r\n    z-index: 10;\r\n}\r\n\r\n.image-wrapper:hover .tooltip {\r\n    display: flex;\r\n}\r\n\r\n.suggested-section {\r\n    flex: 0 0 300px;\r\n    border-radius: 15px;\r\n    padding: 15px;\r\n    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3), -5px -5px 15px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.suggested-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.section-title {\r\n    font-size: 1.2rem;\r\n    font-weight: 600;\r\n}\r\n\r\n.see-all {\r\n    font-size: 0.9rem;\r\n    color: #1e90ff;\r\n    text-decoration: none;\r\n}\r\n\r\n.see-all:hover {\r\n    text-decoration: none;\r\n}\r\n\r\n.follow-all-btn {\r\n    width: 100%;\r\n    padding: 8px;\r\n    border: none;\r\n    border-radius: 5px;\r\n    background: linear-gradient(45deg, #3b82f6, #60a5fa);\r\n    color: white;\r\n    cursor: pointer;\r\n    font-size: 0.9rem;\r\n    margin-bottom: 15px;\r\n    transition: background 0.3s ease, transform 0.3s ease;\r\n}\r\n\r\n.follow-all-btn:hover {\r\n    background: linear-gradient(45deg, #033483, #054a9d);\r\n    transform: scale(1.02);\r\n}\r\n\r\n.suggested-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr;\r\n    scroll-behavior: smooth;\r\n    gap: 15px;\r\n    max-height: 400px;\r\n    overflow-y: auto;\r\n}\r\n::-webkit-scrollbar{\r\n    display: none;\r\n}\r\n.suggested-card {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    cursor: default;\r\n    padding: 15px;\r\n    border-radius: 10px;\r\n    background: rgba(255, 255, 255, 0.05);\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.suggested-card:hover {\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.suggested-image-wrapper {\r\n    position: relative;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.suggested-image {\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border: 2px solid;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.suggested-card:hover .suggested-image {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.suggested-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 5px;\r\n    text-align: center;\r\n}\r\n\r\n.suggested-name {\r\n    font-size: 0.9rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.suggested-followed-by {\r\n    font-size: 0.75rem;\r\n    color: #b0b0b0;\r\n}\r\n\r\n.follow-btn {\r\n    padding: 5px 15px;\r\n    border: none;\r\n    border-radius: 5px;\r\n    cursor: pointer;\r\n    color: white;\r\n    font-size: 0.9rem;\r\n    background: linear-gradient(45deg, #3b82f6, #60a5fa);\r\n    transition: transform 0.3s ease, background 0.3s ease;\r\n    margin-top: 5px;\r\n}\r\n\r\n.follow-btn:hover {\r\n    transform: scale(1.05);\r\n    background: linear-gradient(45deg, #2563eb, #3b82f6);\r\n}\r\n\r\n/* Modal Styles */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: rgba(0,0,0,0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  \r\n  /* Start with hidden and slightly moved up */\r\n  opacity: 0;\r\n  transform: translateY(-20px);\r\n  \r\n  /* Animate when appearing */\r\n  animation: fadeSlideIn 0.3s forwards;\r\n  z-index: 1000;\r\n}\r\n\r\n@keyframes fadeSlideIn {\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.modal-content {\r\n    background: #1e293b;\r\n    border-radius: 15px;\r\n    padding: 20px;\r\n    max-width: 400px;\r\n    width: 90%;\r\n    position: relative;\r\n    animation: modalFadeIn 0.3s ease;\r\n}\r\n\r\n.modal-close {\r\n    position: absolute;\r\n    top: 10px;\r\n    right: 10px;\r\n    background: none;\r\n    border: none;\r\n    color: #fff;\r\n    font-size: 1.5rem;\r\n    cursor: pointer;\r\n}\r\n\r\n.modal-body {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    gap: 10px;\r\n}\r\n\r\n.modal-image {\r\n    width: 100px;\r\n    height: 100px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border-radius: 50%;\r\n    border: 2px solid;\r\n}\r\n\r\n.modal-body h3 {\r\n    margin: 10px 0 5px;\r\n    font-size: 1.2rem;\r\n    color: #b0b0b0;\r\n}\r\n\r\n.modal-body p {\r\n    font-size: 0.9rem;\r\n    color: #b0b0b0;\r\n    text-align: center;\r\n}\r\n\r\n/* Animations */\r\n@keyframes glow {\r\n    from {\r\n        box-shadow: 0 0 10px #00ffcc, 0 0 20px #00ffcc, 0 0 30px #00ffcc;\r\n    }\r\n    to {\r\n        box-shadow: 0 0 20px #00ffcc, 0 0 30px #00ffcc, 0 0 40px #00ffcc;\r\n    }\r\n}\r\n\r\n@keyframes modalFadeIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: scale(0.8);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: scale(1);\r\n    }\r\n}\r\n\r\n.animate-fade-in {\r\n    animation: fadeIn 0.5s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Mobile Responsiveness */\r\n@media (max-width: 768px) {\r\n    .home-content {\r\n        flex-direction: column;\r\n    }\r\n\r\n    .image-list {\r\n        display: flex;\r\n        overflow-x: auto;\r\n        gap: 10px;\r\n        -webkit-overflow-scrolling: touch;\r\n    }\r\n\r\n    .image-card {\r\n        flex: 0 0 calc(25% - 10px); /* 4 images */\r\n    }\r\n\r\n    .image-item {\r\n        width: 80px;\r\n        height: 80px;\r\n    }\r\n\r\n    .suggested-section {\r\n        flex: 1;\r\n    }\r\n}\r\n\r\n/* Desktop Responsiveness */\r\n@media (min-width: 769px) {\r\n    .image-list {\r\n        display: flex;\r\n        overflow-x: auto;\r\n        gap: 15px;\r\n    }\r\n\r\n    .image-card {\r\n        flex: 0 0 calc(14.28% - 15px); /* 7 images */\r\n    }\r\n\r\n    .image-item {\r\n        width: 80px;\r\n        height: 80px;\r\n    }\r\n}\r\n\r\n/* Hide scrollbar but keep functionality */\r\n.image-list::-webkit-scrollbar {\r\n    display: none;\r\n}\r\n\r\n.image-list {\r\n    -ms-overflow-style: none;\r\n    scrollbar-width: none;\r\n}\r\n\r\n.image-card.session-user {\r\n    position: relative;\r\n    margin-right: 1.2rem;\r\n    padding-right: 1rem;\r\n}\r\n\r\n.image-card.session-user::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 5%;\r\n    right: 0;\r\n    height: 80%;\r\n    width: 2px;\r\n    background: linear-gradient(to bottom, #4f46e5, #9333ea); /* Styled look */\r\n    border-radius: 2px;\r\n}\r\n\r\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;AAeE;;;;;;;;;;AAgVF;;;;;;;;;;;;;;AA7TE;;;;;;;;;;AASA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;AAQA;;;;;;;;;;;;;;AAwmBF;;;;;;;;;;;;AAjlBE;;;;;;;;;;AASF;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;AAKA;;;;;;;;AAQA;;;;;AAOA;;;;;;;;;;AACA;;;;;;;;;;;;;;;;AAGA;EACI;;;;EAIA;;;;EACA;;;;EAEA;;;;EACA;;;;EACA;;;;;EACA;;;;EACA;;;;EAEA;;;;EACA;;;;;;EACA;;;;EACA;;;;EACA;;;;;EACA;;;;;EACA;;;;;;AAMJ;;;;AAGA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAOA;;;;;;;;;;AAWA;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;AAIA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;;;;;;;;;;;;;AAoBA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;;;AAOA;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;AAgBA;EACI;;;;EAIA;;;;;;;EAOA;;;;EAIA;;;;;EAKA;;;;;AAMJ;EACI;;;;;;EAMA;;;;EAIA;;;;;;AAOJ;;;;AAIA;;;;;AAKA;;;;;;AAMA", "debugId": null}}, {"offset": {"line": 1036, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/DashboardLayout.css"], "sourcesContent": ["/* ../../styles/DashboardLayout.css */\r\n.dashboard-wrapper {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all 0.4s ease;\r\n  overflow: hidden;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n}\r\n\r\n/* Sleek Navbar */\r\n.sleek-navbar {\r\n  padding: 1rem 1.5rem;\r\n  background: linear-gradient(90deg, #0f172a 0%, #1e293b 100%) !important;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.sleek-navbar.bg-light {\r\n  background: linear-gradient(90deg, #f1f5f9 0%, #e2e8f0 100%) !important;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.sleek-brand {\r\n  font-size: 1.6rem;\r\n  letter-spacing: 1.5px;\r\n  color: #3b82f6 !important;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.sleek-brand:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.sleek-toggler {\r\n  border: none;\r\n  padding: 0.5rem;\r\n  transition: transform 0.3s ease;\r\n  background: transparent;\r\n}\r\n\r\n.sleek-toggler:hover {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n/* Fix Toggler Icon Visibility */\r\n.sleek-toggler.dark-toggler .navbar-toggler-icon {\r\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(226, 232, 240, 0.95)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\") !important;\r\n}\r\n\r\n.sleek-toggler.light-toggler .navbar-toggler-icon {\r\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(30, 41, 59, 0.95)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\") !important;\r\n}\r\n\r\n/* Sleek Sidebar */\r\n.sleek-sidebar {\r\n  width: 300px;\r\n  height: 100dvh; /* <== this is the game-changer */\r\n  position: fixed;\r\n  top: 0;\r\n  left: -300px;\r\n  scroll-behavior: smooth;\r\n  background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%) !important;\r\n  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  z-index: 1000;\r\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.4);\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n\r\n.sleek-sidebar.bg-light {\r\n  background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%) !important;\r\n  box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.sleek-sidebar.open {\r\n  left: 0;\r\n  scroll-behavior: smooth;\r\n}\r\n\r\n.sleek-header h4 {\r\n  color: #3b82f6;\r\n  font-size: 1.4rem;\r\n  letter-spacing: 2px;\r\n}\r\n\r\n.sleek-close {\r\n  filter: brightness(1.8);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.sleek-close:hover {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n/* Navigation */\r\n.sleek-nav {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.sleek-nav-link {\r\n  padding: 12px 20px;\r\n  border-radius: 12px;\r\n  margin: 8px 0;\r\n  font-size: 1.1rem;\r\n  font-weight: 500;\r\n  color: inherit;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.sleek-nav-link:hover {\r\n  background: linear-gradient(90deg, #667eea, #764ba2);\r\n  color: #ffffff !important;\r\n  transform: translateX(8px);\r\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n/* Footer */\r\n.sleek-footer {\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  background: rgba(0, 0, 0, 0.05);\r\n  border-radius: 0 0 12px 12px;\r\n}\r\n\r\n.sleek-profile {\r\n  padding: 15px;\r\n  border-radius: 12px;\r\n  background: rgba(255, 255, 255, 0.08);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.sleek-profile:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.sleek-avatar {\r\n  border: 2px solid #3b82f6;\r\n  padding: 2px;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.sleek-avatar:hover {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.sleek-username {\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.sleek-status {\r\n  font-size: 0.85rem;\r\n  opacity: 0.9;\r\n}\r\n\r\n.sleek-status.online::before {\r\n  content: \"• \";\r\n  color: #22c55e;\r\n  font-size: 1.2rem;\r\n  vertical-align: middle;\r\n}\r\n\r\n/* Buttons */\r\n.sleek-btn {\r\n  padding: 10px 20px;\r\n  border-radius: 30px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  border: none;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.theme-toggle {\r\n  background: #3b82f6;\r\n  color: #ffffff;\r\n}\r\n\r\n.theme-toggle:hover {\r\n  background: #2563eb;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);\r\n}\r\n\r\n.logout-btn {\r\n  background: #ef4444;\r\n  color: #ffffff;\r\n}\r\n\r\n.logout-btn:hover {\r\n  background: #dc2626;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(239, 68, 68, 0.4);\r\n}\r\n\r\n/* Main Content */\r\n.sleek-main {\r\n  flex: 1;\r\n  margin-left: 0;\r\n  transition: all 0.4s ease;\r\n  border-radius: 20px 0 0 20px;\r\n  box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.sleek-container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n@media (min-width: 992px) {\r\n  .sleek-sidebar {\r\n      left: 0;\r\n  }\r\n  .sleek-main {\r\n      margin-left: 300px;\r\n  }\r\n  .sleek-sidebar.bg-light {\r\n      box-shadow: 4px 0 15px rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.user-result {\r\n  display: flex;\r\n  gap: 40px;\r\n  transition: background-color 0.3s ease;\r\n}\r\n.user-result:hover {\r\n  background-color: rgba(105, 105, 105, 0.5);\r\n}\r\n\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-in-out forwards;\r\n  opacity: 0;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    transform: translateY(10px);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateY(0px);\r\n    opacity: 1;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;AAUA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;AAIA;;;;;;;AAOA;;;;AAKA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;;;AAiBA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;;AAKA;EACE;;;;EAGA;;;;EAGA;;;;;AAKF;;;;;;AAKA;;;;AAIA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 1282, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/globals.css"], "sourcesContent": ["body {\r\n    margin: 0;\r\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',\r\n      'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n      sans-serif;\r\n    -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n    background: linear-gradient(135deg, #667eea, #764ba2);\r\n    color: white;\r\n  }\r\n\r\n  /* Links */\r\na {\r\n  text-decoration: none;\r\n}\r\n\r\n\r\na:hover {\r\n  text-decoration: underline;\r\n}\r\n  \r\n::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n/* Typography */\r\nh2,\r\nh3 {\r\n    font-family: 'Poppins', sans-serif;\r\n    font-weight: 700;\r\n}\r\n\r\n\r\np {\r\n  font-family: 'Poppins', sans-serif;\r\n  font-size: 1.1rem;\r\n}\r\n  code {\r\n    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n      monospace;\r\n  }\r\n  @media (max-width: 767.98px) {\r\n  h2,\r\n  h3 {\r\n      font-size: 1.5rem;\r\n      /* Smaller headings */\r\n  }\r\n  p {\r\n    font-size: 0.9rem;\r\n    /* Smaller text */\r\n}\r\n}\r\n\r\n\r\n@media (min-width: 767.98px) {\r\nbody {\r\n  height: 100vh;\r\n}\r\n}"], "names": [], "mappings": "AAAA;;;;;;;;;AAYA;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAOA;;;;;AAIE;;;;AAIA;EACA;;;;EAKA;;;;;AAOF;EACA", "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Profile.css"], "sourcesContent": ["/* ../styles/Profile.css */\r\n.skeleton {\r\n    position: relative;\r\n    overflow: hidden;\r\n    background: #ddd;\r\n    border-radius: 8px;\r\n}\r\n\r\n.skeleton::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 0;\r\n    left: -150px;\r\n    height: 100%;\r\n    width: 150px;\r\n    background: linear-gradient(90deg,\r\n            transparent,\r\n            rgba(255, 255, 255, 0.4),\r\n            transparent);\r\n    animation: shimmer 1.5s infinite;\r\n}\r\n\r\n@keyframes shimmer {\r\n    0% {\r\n        left: -150px;\r\n    }\r\n\r\n    100% {\r\n        left: 100%;\r\n    }\r\n}\r\n\r\n\r\n.skeleton-avatar {\r\n    width: 60px;\r\n    height: 60px;\r\n    border-radius: 50%;\r\n}\r\n\r\n.skeleton-username {\r\n    width: 150px;\r\n    height: 25px;\r\n}\r\n\r\n.skeleton-button {\r\n    width: 100px;\r\n    height: 30px;\r\n    border-radius: 5px;\r\n}\r\n\r\n.skeleton-icon {\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 5px;\r\n}\r\n\r\n.skeleton-text {\r\n    width: 60px;\r\n    height: 20px;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.skeleton-line {\r\n    width: 100%;\r\n    height: 15px;\r\n    margin-top: 10px;\r\n}\r\n\r\n.skeleton-line.short {\r\n    width: 70%;\r\n}\r\n\r\n/* Profile Page Styles */\r\n.profile-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 20px;\r\n}\r\n\r\n.profile-card:hover {\r\n    box-shadow: 0 6px 20px rgba(255, 77, 77, 0.2);\r\n    /* Subtle red glow on hover */\r\n}\r\n\r\n.profile-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.profile-avatar {\r\n    width: 110px;\r\n    height: 110px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    border: 3px solid gray;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.profile-card:hover .profile-avatar {\r\n    transform: scale(1.05);\r\n}\r\n\r\n.profile-info {\r\n    flex: 1;\r\n    text-align: left;\r\n}\r\n\r\n.profile-username {\r\n    font-size: 1.5rem;\r\n    font-weight: 600;\r\n    color: #0f172a;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.profile-name {\r\n    font-size: 1.1rem;\r\n    font-weight: 400;\r\n    color: #0f172a;\r\n    margin-bottom: 8px;\r\n}\r\n\r\n.profile-bio {\r\n    font-size: 0.9rem;\r\n    color: #4a5568;\r\n    margin-bottom: 10px;\r\n    line-height: 1.4;\r\n}\r\n\r\n.profile-stats {\r\n    display: flex;\r\n    gap: 15px;\r\n}\r\n\r\n.stat-item {\r\n    font-size: 0.9rem;\r\n    color: #0f172a;\r\n}\r\n\r\n.stat-item strong {\r\n    color: #ff4d4d;\r\n}\r\n\r\n.ot-but {\r\n    display: none;\r\n}\r\n\r\n.edit-button {\r\n    display: block;\r\n    width: 100%;\r\n    padding: 12px;\r\n    background: #ff4d4d;\r\n    color: #f1f5f9;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: background 0.3s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.edit-button:hover {\r\n    background: #e04343;\r\n    box-shadow: 0 0 15px rgba(255, 77, 77, 0.4);\r\n    /* Glowing effect */\r\n}\r\n\r\n.edit-button:active {\r\n    box-shadow: none;\r\n}\r\n\r\n/* Edit Profile Page Styles (unchanged) */\r\n.edit-profile-layout {\r\n    display: flex;\r\n    height: 98vh;\r\n    overflow: hidden;\r\n}\r\n\r\n.sidebar-title {\r\n    font-size: 1.8rem;\r\n    font-weight: 600;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.sidebar-section {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.sidebar-section-title {\r\n    font-size: 1.2rem;\r\n    font-weight: 500;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.sidebar-section-content {\r\n    font-size: 0.9rem;\r\n    color: gray;\r\n    line-height: 1.5;\r\n}\r\n\r\n.sidebar-buttons {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n}\r\n\r\n.sidebar-button {\r\n    padding: 10px 15px;\r\n    background: #3a3a3a;\r\n    color: #f1f5f9;\r\n    border: none;\r\n    border-radius: 8px;\r\n    font-size: 1rem;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: background 0.3s ease, transform 0.1s ease;\r\n}\r\n\r\n.sidebar-button:hover {\r\n    background: #4a4a4a;\r\n    transform: translateX(5px);\r\n}\r\n\r\n.sidebar-button:active {\r\n    transform: translateX(0);\r\n}\r\n\r\n.sidebar-button.active {\r\n    background: #3b82f6;\r\n    cursor: default;\r\n}\r\n\r\n.sidebar-button.active:hover {\r\n    background: #2563eb;\r\n    transform: none;\r\n}\r\n\r\n.edit-profile-card {\r\n    border-radius: 15px;\r\n    padding: 40px;\r\n    width: 100%;\r\n    max-width: 600px;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.edit-profile-title {\r\n    font-size: 1.5rem;\r\n    font-weight: 600;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.edit-form {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20px;\r\n}\r\n\r\n.form-group {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n}\r\n\r\n.form-group label {\r\n    font-size: 1rem;\r\n    font-weight: 500;\r\n}\r\n\r\n.form-group input,\r\n.form-group textarea {\r\n    padding: 10px;\r\n    border: none;\r\n    border-radius: 8px;\r\n    background: #e2e8f0;\r\n    font-size: 1rem;\r\n    transition: background 0.3s ease, transform 0.1s ease;\r\n}\r\n\r\n.form-group input:focus,\r\n.form-group textarea:focus {\r\n    background: #d1d5db;\r\n    outline: none;\r\n    transform: scale(1.01);\r\n}\r\n\r\n.form-group textarea {\r\n    resize: vertical;\r\n}\r\n\r\n.avatar-preview {\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 50%;\r\n    object-fit: cover;\r\n    margin-top: 10px;\r\n    border: 2px solid #ff4d4d;\r\n    transition: transform 0.3s ease;\r\n}\r\n\r\n.avatar-preview:hover {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.form-instructions {\r\n    margin-top: 20px;\r\n    font-size: 0.1rem;\r\n    color: #4a5568;\r\n}\r\n\r\n.visibility-link {\r\n    color: #ff4d4d;\r\n    cursor: pointer;\r\n    transition: color 0.3s ease;\r\n}\r\n\r\n.visibility-link:hover {\r\n    color: #e04343;\r\n    text-decoration: underline;\r\n}\r\n\r\n.form-buttons {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    gap: 10px;\r\n    margin-top: 20px;\r\n}\r\n\r\n.submit-button,\r\n.cancel-button {\r\n    flex: 1;\r\n    padding: 12px;\r\n    border: none;\r\n    border-radius: 25px;\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    cursor: pointer;\r\n    transition: background 0.3s ease, transform 0.1s ease;\r\n}\r\n\r\n.submit-button {\r\n    background: #3b82f6;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.submit-button:hover {\r\n    background: #2563eb;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.submit-button:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n.cancel-button {\r\n    background: #3a3a3a;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.cancel-button:hover {\r\n    background: #4a4a4a;\r\n    transform: translateY(-2px);\r\n}\r\n\r\n.cancel-button:active {\r\n    transform: translateY(0);\r\n}\r\n\r\n/* Shared Styles */\r\n.loading,\r\n.error {\r\n    text-align: center;\r\n    font-size: 1.2rem;\r\n    color: #f1f5f9;\r\n    padding: 20px;\r\n}\r\n\r\n.error {\r\n    color: #ff4d4d;\r\n}\r\n\r\n@media (min-width: 770px) {\r\n    .edit-profile-main {\r\n        flex: 1;\r\n        padding: 40px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: flex-start;\r\n    }\r\n\r\n    .edit-profile-sidebar {\r\n        width: 350px;\r\n        border-right: 1px solid #3a3a3a;\r\n        padding: 20px;\r\n        flex-shrink: 0;\r\n        overflow-y: auto;\r\n    }\r\n\r\n    .p-car {\r\n        justify-content: space-between;\r\n        width: 600px;\r\n    }\r\n\r\n    .edit-profile-main {\r\n        flex: 1;\r\n        overflow-y: auto;\r\n        padding: 20px;\r\n        scroll-behavior: smooth;\r\n    }\r\n\r\n    .p-card {\r\n        justify-content: space-around;\r\n        gap: 40px;\r\n    }\r\n\r\n    .profile-card {\r\n        background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);\r\n        border-radius: 15px;\r\n        padding: 25px;\r\n        width: 700px;\r\n        /* Wider card to accommodate the new layout */\r\n        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n        transition: box-shadow 0.3s ease;\r\n    }\r\n\r\n    .edit-profile-sidebar {\r\n        display: block;\r\n    }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n\r\n    html,\r\n    body {\r\n        overflow-x: hidden;\r\n        overflow-y: auto;\r\n    }\r\n\r\n    .edit-profile-sidebar {\r\n        display: none;\r\n    }\r\n\r\n    .p-car {\r\n        gap: 14px;\r\n    }\r\n\r\n    .p-card {\r\n        gap: 20px;\r\n    }\r\n    .edit-profile-main {\r\n        padding: 4px;\r\n    }\r\n    .profile-card {\r\n        padding: 20px;\r\n        max-width: 100%;\r\n        background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);\r\n        /* Subtle gradient */\r\n        border-radius: 15px;\r\n        padding: 25px;\r\n        width: 100%;\r\n        /* Wider card to accommodate the new layout */\r\n        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n        transition: box-shadow 0.3s ease;\r\n    }\r\n\r\n    .profile-content {\r\n        flex-direction: column;\r\n        align-items: center;\r\n        text-align: center;\r\n    }\r\n\r\n    .profile-avatar {\r\n        width: 60px;\r\n        height: 60px;\r\n    }\r\n\r\n    .ot-but {\r\n        display: block;\r\n    }\r\n\r\n    .ot-butt {\r\n        display: none;\r\n    }\r\n\r\n    .profile-username {\r\n        font-size: 1.4rem;\r\n    }\r\n\r\n    .profile-name {\r\n        font-size: 1rem;\r\n    }\r\n\r\n    .profile-stats {\r\n        flex-direction: column;\r\n        gap: 8px;\r\n    }\r\n\r\n    .edit-profile-layout {\r\n        flex-direction: column;\r\n    }\r\n\r\n    .edit-profile-sidebar {\r\n        width: 100%;\r\n        border-right: none;\r\n        border-bottom: 1px solid #3a3a3a;\r\n    }\r\n\r\n   .edit-profile-layout {\r\n    display: flex;\r\n    flex-direction: column; /* Important for stacking on mobile */\r\n    height: auto;\r\n    min-height: 100vh;\r\n    overflow: visible; /* Avoid blocking scroll */\r\n}\r\n\r\n\r\n    .edit-profile-card {\r\n        padding: 8px;\r\n    }\r\n\r\n    .edit-profile-title {\r\n        font-size: 1.5rem;\r\n    }\r\n}\r\n\r\n/* Modal Styles */\r\n@keyframes fadeIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: scale(0.95);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: scale(1);\r\n    }\r\n}\r\n\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background: rgba(15, 23, 42, 0.7); /* Semi-transparent dark overlay */\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n    border-radius: 15px;\r\n    padding: 25px;\r\n    width: 100%;\r\n    color: white;\r\n    max-width: 400px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.9);\r\n    animation: fadeIn 0.3s ease-out forwards;\r\n    text-align: center;\r\n    background:  rgb(22, 33, 62);\r\n}\r\n\r\n.modal-title {\r\n    font-size: 1.5rem;\r\n    font-weight: 600;\r\n    margin-bottom: 15px;\r\n}\r\n\r\n.modal-text {\r\n    font-size: 1rem;\r\n    margin-bottom: 20px;\r\n    line-height: 1.5;\r\n}\r\n\r\n.modal-actions {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    gap: 10px;\r\n}\r\n\r\n.modal-btn {\r\n    flex: 1;\r\n    padding: 10px;\r\n    border: none;\r\n    border-radius: 10px;\r\n    font-size: 1rem;\r\n    font-weight: 500;\r\n    cursor: pointer;\r\n    transition: background 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;\r\n}\r\n\r\n.modal-btn-success {\r\n    background: #3b82f6;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.modal-btn-success:hover {\r\n    background: #2563eb;\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 15px rgba(255, 77, 77, 0.3);\r\n}\r\n\r\n.modal-btn-success:active {\r\n    transform: translateY(0);\r\n    box-shadow: none;\r\n}\r\n\r\n.modal-btn-cancel {\r\n    background: #3a3a3a;\r\n    color: #f1f5f9;\r\n}\r\n\r\n.modal-btn-cancel:hover {\r\n    background: #4a4a4a;\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 15px rgba(58, 58, 58, 0.3);\r\n}\r\n\r\n.modal-btn-cancel:active {\r\n    transform: translateY(0);\r\n    box-shadow: none;\r\n}\r\n\r\n.custom-alert-container {\r\n  position: fixed;\r\n  top: 30px;\r\n  right: 30px;\r\n  z-index: 9999;\r\n  animation: slideIn 0.5s ease forwards;\r\n}\r\n\r\n.custom-alert {\r\n  background-color: #dc3545;\r\n  color: white;\r\n  padding: 16px 24px;\r\n  border-radius: 8px;\r\n  font-weight: bold;\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);\r\n  position: relative;\r\n  min-width: 300px;\r\n}\r\n\r\n.timer-bar {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  height: 5px;\r\n  background-color: #ffffff;\r\n  animation: countdownBar 4s linear forwards;\r\n  border-radius: 0 0 8px 8px;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes countdownBar {\r\n  from {\r\n    width: 100%;\r\n  }\r\n  to {\r\n    width: 0%;\r\n  }\r\n}\r\n\r\n.no-followers-container {\r\n  animation: fadeInUp 0.6s ease-in-out;\r\n}\r\n\r\n@keyframes fadeInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.icon-wrapper {\r\n  animation: iconPop 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes iconPop {\r\n  0% {\r\n    transform: scale(0.8);\r\n    opacity: 0;\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;;;;;;AAcA;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;AAKA;;;;;;;AAOA;;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;AAcA;;;;;AAMA;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AAUA;;;;;;AAOA;;;;AAIA;;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;;;AAQA;;;;AAIA;EACI;;;;;;;;EAQA;;;;;;;;EAQA;;;;;EAKA;;;;;;;EAOA;;;;;EAKA;;;;;;;;;EAUA;;;;;AAMJ;EAEI;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;;EAaA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;;EAMD;;;;;;;;EASC;;;;EAIA;;;;;AAMJ;;;;;;;;;;;;AAWA;;;;;;;;;;;;;AAaA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA", "debugId": null}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Chats.css"], "sourcesContent": ["/* ../../styles/Chats.css */\r\n.chat-wrapper {\r\n    position: fixed; /* Fixes the entire chat component in place */\r\n    top: 10px; /* Margin from top */\r\n    left: 300px; /* Accounts for sidebar width on desktop */\r\n    right: 10px; /* Margin from right */\r\n    bottom: 10px; /* Margin from bottom */\r\n    display: flex;\r\n    flex-direction: column;\r\n    transition: all 0.4s ease;\r\n    border-radius: 15px;\r\n    overflow: hidden; /* Prevents wrapper from scrolling */\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n    z-index: 5; /* Ensures it stays above other content */\r\n}\r\n\r\n/* Chat Header */\r\n.chat-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    background: rgba(0, 0, 0, 0.05);\r\n    flex-shrink: 0; /* Fixed height, no shrinking */\r\n}\r\n\r\n/* Chat Title */\r\n.chat-title {\r\n    margin: 0;\r\n    font-size: 1.5rem;\r\n    font-weight: 600;\r\n    color: #3b82f6;\r\n    letter-spacing: 1px;\r\n}\r\n\r\n/* Chat Messages */\r\n.chat-messages {\r\n    flex: 1; /* Takes remaining space between header and input */\r\n    padding: 20px;\r\n    overflow-y: auto; /* Messages scrollable */\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 15px;\r\n}\r\n\r\n/* Message Styling */\r\n.message {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    animation: slideIn 0.3s ease;\r\n}\r\n\r\n.message-you {\r\n    justify-content: flex-end;\r\n}\r\n\r\n.message-friend {\r\n    justify-content: flex-start;\r\n}\r\n\r\n.message-bubble {\r\n    max-width: 70%;\r\n    padding: 12px 18px;\r\n    border-radius: 20px;\r\n    position: relative;\r\n    transition: transform 0.2s ease;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.message-bubble:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.message-text {\r\n    display: block;\r\n    font-size: 1rem;\r\n    word-wrap: break-word;\r\n}\r\n\r\n.message-timestamp {\r\n    display: block;\r\n    font-size: 0.75rem;\r\n    opacity: 0.7;\r\n    margin-top: 5px;\r\n}\r\n\r\n/* Chat Input */\r\n.chat-input-form {\r\n    padding: 15px 20px;\r\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n    background: rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    flex-shrink: 0; /* Fixed height, no shrinking */\r\n}\r\n\r\n/* Input Field */\r\n.chat-input {\r\n    flex: 1;\r\n    padding: 12px 20px;\r\n    border: none;\r\n    border-radius: 30px;\r\n    font-size: 1rem;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-input:focus {\r\n    outline: none;\r\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* Send Button */\r\n.chat-send-btn {\r\n    padding: 12px 20px;\r\n    border-radius: 50%;\r\n    background: #3b82f6;\r\n    color: #ffffff;\r\n    border: none;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.chat-send-btn:hover {\r\n    background: #2563eb;\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.5);\r\n}\r\n\r\n/* Animation */\r\n@keyframes slideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Scrollbar Styling */\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 8px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(59, 130, 246, 0.5);\r\n    border-radius: 10px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: #3b82f6;\r\n}\r\n\r\n/* Mobile Adjustments */\r\n@media (max-width: 991px) {\r\n    .chat-wrapper {\r\n        left: 10px; /* No sidebar on mobile, full width */\r\n        top: 70px; /* Accounts for navbar height */\r\n    }\r\n}\r\n\r\n/* Messages Component Responsive Styles */\r\n.chat-list-with-panel {\r\n    width: 350px !important;\r\n}\r\n\r\n@media (max-width: 1024px) {\r\n    .chat-container {\r\n        flex-direction: column;\r\n        height: 90vh;\r\n    }\r\n\r\n    .chat-list {\r\n        width: 100% !important;\r\n        max-height: none;\r\n        border-right: none;\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n\r\n    .chat-panel {\r\n        width: 100% !important;\r\n        display: flex !important;\r\n    }\r\n\r\n    .chat-list-with-panel {\r\n        display: none; /* Hide chat list when chat is open on mobile */\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .chat-container {\r\n        height: 95vh;\r\n    }\r\n\r\n    .chat-item {\r\n        padding: 10px 15px;\r\n    }\r\n\r\n    .chat-messages {\r\n        max-height: 300px;\r\n    }\r\n\r\n    .message-bubble {\r\n        max-width: 85%;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .chat-input-form {\r\n        padding: 15px;\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 10px 14px;\r\n        font-size: 0.9rem;\r\n    }\r\n\r\n    .chat-send-btn {\r\n        width: 45px;\r\n        height: 45px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 1.2rem;\r\n    }\r\n\r\n    .user-name {\r\n        font-size: 0.8rem;\r\n    }\r\n\r\n    .last-message {\r\n        font-size: 0.7rem;\r\n    }\r\n}\r\n\r\n/* Typing Indicator Animation */\r\n.typing-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    padding: 8px 0;\r\n}\r\n\r\n.typing-indicator span {\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    background-color: #6b7280;\r\n    animation: typing 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-indicator span:nth-child(1) {\r\n    animation-delay: -0.32s;\r\n}\r\n\r\n.typing-indicator span:nth-child(2) {\r\n    animation-delay: -0.16s;\r\n}\r\n\r\n@keyframes typing {\r\n    0%, 80%, 100% {\r\n        transform: scale(0.8);\r\n        opacity: 0.5;\r\n    }\r\n    40% {\r\n        transform: scale(1);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Status Indicator Styles */\r\n.status-indicator {\r\n    position: absolute;\r\n    bottom: 2px;\r\n    right: 2px;\r\n    width: 12px;\r\n    height: 12px;\r\n    border-radius: 50%;\r\n    border: 2px solid white;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n/* Chat Messages Scrollbar */\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 6px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: rgba(0, 0, 0, 0.1);\r\n    border-radius: 3px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(0, 0, 0, 0.3);\r\n    border-radius: 3px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* Fixed Chat Input Position */\r\n.chat-input-form {\r\n    position: sticky;\r\n    bottom: 0;\r\n    background: inherit;\r\n    padding: 15px 20px;\r\n    border-top: 1px solid rgba(0, 0, 0, 0.1);\r\n    z-index: 10;\r\n}\r\n\r\n/* Typing Indicator in User List */\r\n.typing-text {\r\n    color: #10b981;\r\n    font-style: italic;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.typing-text .typing-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.typing-text .typing-indicator span {\r\n    width: 4px;\r\n    height: 4px;\r\n    border-radius: 50%;\r\n    background-color: #10b981;\r\n    animation: typing 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-text .typing-indicator span:nth-child(1) {\r\n    animation-delay: -0.32s;\r\n}\r\n\r\n.typing-text .typing-indicator span:nth-child(2) {\r\n    animation-delay: -0.16s;\r\n}\r\n\r\n/* Enhanced Status Indicator */\r\n.status-indicator {\r\n    position: absolute;\r\n    bottom: 2px;\r\n    right: 2px;\r\n    width: 12px;\r\n    height: 12px;\r\n    border-radius: 50%;\r\n    border: 2px solid white;\r\n    transition: background-color 0.3s ease;\r\n    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.status-indicator.online {\r\n    background-color: #10b981;\r\n    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);\r\n}\r\n\r\n.status-indicator.offline {\r\n    background-color: #ef4444;\r\n    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAgBA;;;;;;;AAQA;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;;;;;;;AAgBA;;;;AAcA;EACI;;;;;;AAOJ;;;;AAIA;EACI;;;;;EAKA;;;;;;;EAOA;;;;;EAKA;;;;;AAKJ;EACI;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAMJ;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAKA;;;;;;;;;;;;AAYA;;;;;AAKA", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Messages.css"], "sourcesContent": [".chat-container {\r\n  display: flex;\r\n  height: 100%;\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.chat-list {\r\n  width: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  border-right: 1px solid rgba(160, 174, 192, 0.2);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.chat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  border-radius: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-item:hover {\r\n  background: rgba(124, 135, 151, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n}\r\n\r\n.avatar {\r\n  border-radius: 50%;\r\n  border: 2px solid #a0aec0;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-item:hover .avatar {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: 10px;\r\n  height: 10px;\r\n  background: radial-gradient(circle, #48bb78 60%, #38a169 100%);\r\n  border-radius: 50%;\r\n  border: 2px solid #2d3748;\r\n  box-shadow: 0 0 5px #48bb78;\r\n}\r\n\r\n.chat-details {\r\n  margin-left: 15px;\r\n  flex: 1;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.user-name {\r\n  font-weight: bold;\r\n  font-size: 0.875rem;\r\n  text-transform: capitalize;\r\n}\r\n\r\n.timestamp {\r\n  font-size: 0.75rem;\r\n  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.last-message {\r\n  font-size: 0.75rem;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.reaction {\r\n  color: #f56565;\r\n  animation: pulse 1.5s infinite;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.chat-panel {\r\n  display: none;\r\n  width: 66.666667%;\r\n  padding: 24px;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* Desktop - Show both list and panel */\r\n@media (min-width: 1024px) {\r\n  .chat-list {\r\n    width: 33.333333%;\r\n    display: block;\r\n  }\r\n  .chat-panel {\r\n    display: flex;\r\n    width: 66.666667%;\r\n  }\r\n}\r\n\r\n/* Mobile - Responsive behavior */\r\n@media (max-width: 1023px) {\r\n  .chat-container {\r\n    position: relative;\r\n  }\r\n\r\n  /* When no chat is selected, show only chat list */\r\n  .chat-list {\r\n    width: 100%;\r\n    display: block;\r\n  }\r\n\r\n  .chat-panel {\r\n    display: none;\r\n  }\r\n\r\n  /* When chat is selected, hide list and show panel */\r\n  .chat-list.chat-list-with-panel {\r\n    display: none;\r\n  }\r\n\r\n  .chat-panel.chat-panel-active {\r\n    display: flex;\r\n    width: 100%;\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: inherit;\r\n    z-index: 10;\r\n  }\r\n}\r\n\r\n.chat-window, .instructions {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);\r\n  animation: fadeIn 0.5s ease-in;\r\n}\r\n\r\n.chat-header {\r\n  font-size: 1.125rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n  text-align: center;\r\n}\r\n\r\n.chat-content, .instruction-text {\r\n  margin-top: 1rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.icon-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chat-icon {\r\n  font-size: 3rem;\r\n  display: inline-block;\r\n  padding: 4px;\r\n  width: 80px;\r\n  height: 80px;\r\n  line-height: 80px;\r\n  border: 2px solid #fff;\r\n  border-radius: 50%;\r\n  text-align: center;\r\n}\r\n\r\n.instruction-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.instruction-text {\r\n  font-size: 1rem;\r\n  color: #ccc;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.send-button {\r\n  background-color: #6366f1;\r\n  color: #fff;\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.send-button:hover {\r\n  background-color: #4f46e5;\r\n}\r\n\r\n.instructions{\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n@keyframes pulse {\r\n  0% { transform: scale(1); }\r\n  50% { transform: scale(1.2); }\r\n  100% { transform: scale(1); }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAQA;EACE;;;;;EAIA;;;;;;AAOF;EACE;;;;EAKA;;;;;EAKA;;;;EASA;;;;;;;;;;AAaF;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;;;AAMA", "debugId": null}}]}