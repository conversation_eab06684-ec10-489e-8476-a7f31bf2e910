/* [project]/pages/styles/Chats.css [client] (css) */
.chat-wrapper {
  z-index: 5;
  border-radius: 15px;
  flex-direction: column;
  transition: all .4s;
  display: flex;
  position: fixed;
  inset: 10px 10px 10px 300px;
  overflow: hidden;
  box-shadow: 0 4px 20px #0000001a;
}

.chat-header {
  background: #0000000d;
  border-bottom: 1px solid #ffffff1a;
  flex-shrink: 0;
  padding: 15px 20px;
}

.chat-title {
  color: #3b82f6;
  letter-spacing: 1px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.chat-messages {
  flex-direction: column;
  flex: 1;
  gap: 15px;
  padding: 20px;
  display: flex;
  overflow-y: auto;
}

.message {
  align-items: flex-end;
  animation: .3s slideIn;
  display: flex;
}

.message-you {
  justify-content: flex-end;
}

.message-friend {
  justify-content: flex-start;
}

.message-bubble {
  border-radius: 20px;
  max-width: 70%;
  padding: 12px 18px;
  transition: transform .2s;
  position: relative;
  box-shadow: 0 2px 10px #0000001a;
}

.message-bubble:hover {
  transform: scale(1.02);
}

.message-text {
  word-wrap: break-word;
  font-size: 1rem;
  display: block;
}

.message-timestamp {
  opacity: .7;
  margin-top: 5px;
  font-size: .75rem;
  display: block;
}

.chat-input-form {
  background: #0000000d;
  border-top: 1px solid #ffffff1a;
  flex-shrink: 0;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  display: flex;
}

.chat-input {
  border: none;
  border-radius: 30px;
  flex: 1;
  padding: 12px 20px;
  font-size: 1rem;
  transition: all .3s;
  box-shadow: 0 2px 10px #0000001a;
}

.chat-input:focus {
  outline: none;
  box-shadow: 0 4px 15px #0003;
}

.chat-send-btn {
  color: #fff;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  padding: 12px 20px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 2px 10px #3b82f64d;
}

.chat-send-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px #3b82f680;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-messages::-webkit-scrollbar-track {
  background: none;
}

@media (width <= 991px) {
  .chat-wrapper {
    top: 70px;
    left: 10px;
  }
}

.chat-list-with-panel {
  width: 350px !important;
}

@media (width <= 1024px) {
  .chat-container {
    flex-direction: column;
    height: 90vh;
  }

  .chat-list {
    border-bottom: 1px solid #ffffff1a;
    border-right: none;
    max-height: none;
    width: 100% !important;
  }

  .chat-panel {
    width: 100% !important;
    display: flex !important;
  }

  .chat-list-with-panel {
    display: none;
  }
}

@media (width <= 768px) {
  .chat-container {
    height: 95vh;
  }

  .chat-item {
    padding: 10px 15px;
  }

  .chat-messages {
    max-height: 300px;
  }

  .message-bubble {
    max-width: 85%;
  }
}

@media (width <= 480px) {
  .chat-input-form {
    padding: 15px;
  }

  .chat-input {
    padding: 10px 14px;
    font-size: .9rem;
  }

  .chat-send-btn {
    width: 45px;
    height: 45px;
  }

  .chat-title {
    font-size: 1.2rem;
  }

  .user-name {
    font-size: .8rem;
  }

  .last-message {
    font-size: .7rem;
  }
}

.typing-indicator {
  align-items: center;
  gap: 4px;
  padding: 8px 0;
  display: flex;
}

.typing-indicator span {
  background-color: #6b7280;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  animation: 1.4s ease-in-out infinite typing;
}

.typing-indicator span:first-child {
  animation-delay: -.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    opacity: .5;
    transform: scale(.8);
  }

  40% {
    opacity: 1;
    transform: scale(1);
  }
}

.status-indicator {
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: background-color .3s;
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #0000001a;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #0000004d;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #00000080;
}

.chat-input-form {
  background: inherit;
  z-index: 10;
  border-top: 1px solid #0000001a;
  padding: 15px 20px;
  position: sticky;
  bottom: 0;
}

.chat-input-form .input-group {
  align-items: center;
  gap: 10px;
  display: flex;
}

.chat-input {
  border: 1px solid #0000001a;
  border-radius: 25px;
  outline: none;
  flex: 1;
  padding: 12px 20px;
  transition: all .3s;
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.chat-send-btn {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 45px;
  height: 45px;
  transition: all .3s;
  display: flex;
}

.chat-send-btn:hover {
  background: #2563eb;
  transform: scale(1.05);
}

.chat-send-btn:active {
  transform: scale(.95);
}

.message-footer {
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  display: flex;
}

.message-timestamp {
  opacity: .7;
  font-size: .75rem;
}

.delivery-status {
  opacity: .8;
  font-size: .8rem;
}

.delivery-status i {
  font-size: 12px;
}

.message-bubble {
  word-wrap: break-word;
  border-radius: 18px;
  max-width: 70%;
  padding: 8px 12px;
  position: relative;
}

.message-you .message-bubble {
  color: #fff;
  background: #3b82f6;
  margin-left: auto;
}

.message-friend .message-bubble {
  background: #0000000d;
  margin-right: auto;
}

.message-text {
  line-height: 1.4;
  display: block;
}

.typing-text {
  color: #10b981;
  align-items: center;
  gap: 8px;
  font-style: italic;
  display: flex;
}

.typing-text .typing-indicator {
  align-items: center;
  gap: 2px;
  display: flex;
}

.typing-text .typing-indicator span {
  background-color: #10b981;
  border-radius: 50%;
  width: 4px;
  height: 4px;
  animation: 1.4s ease-in-out infinite typing;
}

.typing-text .typing-indicator span:first-child {
  animation-delay: -.32s;
}

.typing-text .typing-indicator span:nth-child(2) {
  animation-delay: -.16s;
}

.status-indicator {
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: background-color .3s;
  position: absolute;
  bottom: 2px;
  right: 2px;
  box-shadow: 0 0 0 1px #0000001a;
}

.status-indicator.online {
  background-color: #10b981;
  box-shadow: 0 0 0 1px #10b9814d;
}

.status-indicator.offline {
  background-color: #ef4444;
  box-shadow: 0 0 0 1px #ef44444d;
}

.unread-badge {
  color: #fff;
  background: #ff3b30;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  height: 20px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 600;
  animation: 2s infinite unreadPulse;
  display: flex;
  position: absolute;
  top: 8px;
  right: 8px;
  box-shadow: 0 2px 4px #ff3b304d;
}

@keyframes unreadPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: .8;
    transform: scale(1.1);
  }
}

.chat-item {
  cursor: pointer;
  border-radius: 12px;
  align-items: center;
  padding: 12px;
  transition: all .3s;
  display: flex;
  position: relative;
}

.chat-item:hover {
  background-color: #0000000d;
}

.chat-item.selected {
  background-color: #3b82f61a;
  border-left: 4px solid #3b82f6;
}

.status-indicator {
  z-index: 2;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
  position: absolute;
  bottom: 2px;
  right: 2px;
  box-shadow: 0 0 0 1px #0000001a;
}


/* [project]/pages/styles/Messages.css [client] (css) */
.chat-container {
  height: 100%;
  font-family: Arial, sans-serif;
  display: flex;
}

.chat-list {
  border-right: 1px solid #a0aec033;
  width: 100%;
  padding: 20px;
  transition: transform .3s;
  overflow-y: auto;
}

.chat-title {
  text-shadow: 0 0 5px #ffffff1a;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: bold;
}

.chat-item {
  cursor: pointer;
  border-radius: 10px;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 6px #0000001a;
}

.chat-item:hover {
  background: #7c8797e6;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px #0003;
}

.avatar-container {
  position: relative;
}

.avatar {
  border: 2px solid #a0aec0;
  border-radius: 50%;
  transition: transform .3s;
}

.chat-item:hover .avatar {
  transform: scale(1.05);
}

.status-indicator {
  border: 2px solid #2d3748;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  position: absolute;
  bottom: 0;
  right: 0;
  box-shadow: 0 0 5px #48bb78;
}

.chat-details {
  flex: 1;
  margin-left: 15px;
}

.chat-header {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.user-name {
  text-transform: capitalize;
  font-size: .875rem;
  font-weight: bold;
}

.timestamp {
  text-shadow: 0 0 2px #ffffff1a;
  font-size: .75rem;
}

.last-message {
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: .75rem;
  overflow: hidden;
}

.reaction {
  color: #f56565;
  font-size: .875rem;
  animation: 1.5s infinite pulse;
}

.chat-panel {
  width: 66.6667%;
  padding: 24px;
  transition: opacity .3s;
  display: none;
}

@media (width >= 1024px) {
  .chat-list {
    width: 33.3333%;
    display: block;
  }

  .chat-panel {
    width: 66.6667%;
    display: flex;
  }
}

@media (width <= 1023px) {
  .chat-container {
    padding: 6px;
    position: relative;
  }

  .chat-list {
    width: 100%;
    padding: 6px;
    display: block;
  }

  .chat-panel, .chat-list.chat-list-with-panel {
    display: none;
  }

  .chat-panel.chat-panel-active {
    background: inherit;
    z-index: 10;
    width: 100%;
    padding: 6px;
    display: flex;
    position: absolute;
    inset: 0;
  }

  .chat-input-form {
    background: inherit;
    border-top: 1px solid #0000001a;
    padding: 10px 6px;
  }

  .chat-input-form .input-group {
    align-items: center;
    gap: 8px;
    display: flex;
  }

  .chat-input {
    flex: 1;
    border: 1px solid #0000001a !important;
    border-radius: 20px !important;
    padding: 10px 16px !important;
  }

  .chat-send-btn {
    border-radius: 50% !important;
    justify-content: center !important;
    align-items: center !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    display: flex !important;
  }

  .chat-messages {
    height: calc(100vh - 200px) !important;
    padding: 6px 10px !important;
  }

  .chat-header {
    border-bottom: 1px solid #0000001a;
    padding: 10px 6px;
  }

  .chat-item {
    border-radius: 12px;
    margin: 4px 0;
    padding: 12px 6px;
  }
}

.chat-window, .instructions {
  border-radius: 12px;
  width: 100%;
  height: 100%;
  padding: 20px;
  animation: .5s ease-in fadeIn;
  box-shadow: inset 0 0 10px #0000004d;
}

.chat-header {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: bold;
}

.chat-content, .instruction-text {
  margin-top: 1rem;
  line-height: 1.5;
}

.icon-container {
  margin-bottom: 20px;
}

.chat-icon {
  text-align: center;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  padding: 4px;
  font-size: 3rem;
  line-height: 80px;
  display: inline-block;
}

.instruction-title {
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: bold;
}

.instruction-text {
  color: #ccc;
  margin-bottom: 20px;
  font-size: 1rem;
}

.send-button {
  color: #fff;
  cursor: pointer;
  background-color: #6366f1;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1rem;
  transition: background-color .3s;
}

.send-button:hover {
  background-color: #4f46e5;
}

.instructions {
  justify-content: center;
  align-items: center;
  display: flex;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}


/*# sourceMappingURL=pages_styles_2e2118fb._.css.map*/