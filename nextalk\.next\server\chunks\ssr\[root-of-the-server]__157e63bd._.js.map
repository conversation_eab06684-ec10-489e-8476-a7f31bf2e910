{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/axiosConfig.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create an instance with proper server URL\r\nconst axiosInstance = axios.create({\r\n    baseURL: process.env.REACT_APP_API_URL ||\r\n             process.env.NEXT_PUBLIC_SERVER_URL ||\r\n             (process.env.NODE_ENV === 'production'\r\n               ? 'https://nextalk-u0y1.onrender.com'\r\n               : 'http://localhost:5000'),\r\n    withCredentials: true,           // Crucial for sending cookies\r\n    headers: {\r\n        \"Content-Type\": \"application/json\",\r\n    },\r\n});\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,4CAA4C;AAC5C,MAAM,gBAAgB,0GAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAC7B,QAAQ,GAAG,CAAC,sBAAsB,IAClC,CAAC,6EAEG,uBAAuB;IACpC,iBAAiB;IACjB,SAAS;QACL,gBAAgB;IACpB;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/public/Images/predefine.webp.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 200, height: 200, blurDataURL: \"data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA2S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Components/DashboardLayout.js"], "sourcesContent": ["// DashboardLayout.jsx\r\n'use client'; // if you're using App Router (recommended)\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from 'next/image';\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport axios from '../../utils/axiosConfig';\r\n\r\nexport default function DashboardLayout({ children }) {\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const { theme, handleThemeClick } = useTheme();\r\n    const router = useRouter(); // corrected here\r\n\r\n    const toggleTheme = () => {\r\n        const newTheme = theme === 'dark' ? 'light' : 'dark';\r\n        handleThemeClick(newTheme);\r\n    };\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return { background: '#0f172a', color: '#e2e8f0', chatBackground: '#1e293b', cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)' };\r\n        }\r\n        return { background: '#f1f5f9', color: '#1e293b', chatBackground: '#ffffff' };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyles();\r\n    const [user, setUser] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const storedUser = sessionStorage.getItem(\"user\");\r\n        if (!storedUser) {\r\n            router.push(\"/\");\r\n        } else {\r\n            try {\r\n                const parsed = JSON.parse(storedUser);\r\n                setUser(parsed.user);\r\n            } catch (err) {\r\n                console.error(\"Error parsing sessionStorage user:\", err);\r\n                router.push(\"/\");\r\n            }\r\n        }\r\n    }, [router]);\r\n\r\n    const [profile, setProfile] = useState(null);\r\n\r\n    const [tempProfile, setTempProfile] = useState(profile);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchProfile = async () => {\r\n        setLoading(true);\r\n        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');\r\n\r\n        if (!userData?.user?.id) {\r\n            setError('No user data found. Please log in.');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const response = await axios.get('https://nextalk-u0y1.onrender.com/profile', {\r\n                headers: {\r\n                    Authorization: `Bearer ${userData.user.id}`,\r\n                },\r\n            });\r\n            const fetchedProfile = response.data.user || response.data;\r\n            setProfile(fetchedProfile);\r\n            setTempProfile(fetchedProfile);\r\n            setLoading(false);\r\n        } catch (err) {\r\n            const errorMessage = err.response?.data?.message || 'Failed to load profile.';\r\n            setError(errorMessage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (typeof window !== 'undefined') {\r\n            fetchProfile();\r\n        }\r\n    }, []);\r\n\r\n\r\n    const pathname = usePathname();\r\n    const [brandText, setBrandText] = useState(\"Nextalk\");\r\n    useEffect(() => {\r\n        if (pathname === \"/Dashboard/Profile\") {\r\n            setBrandText(profile?.name || \"User\");\r\n        } else {\r\n            setBrandText(\"Nextalk\");\r\n        }\r\n    }, [pathname, profile]);\r\n\r\n\r\n    const [showConfirm, setShowConfirm] = useState(false);\r\n    const handleLogout = () => {\r\n        setShowConfirm(true);\r\n    };\r\n\r\n    const handleConfirmUpload = async (e) => {\r\n        sessionStorage.removeItem(\"user\");\r\n        router.push(\"/\");\r\n    }\r\n\r\n    const [pendingCount, setPendingCount] = useState(0);\r\n\r\n    useEffect(() => {\r\n        const fetchPendingRequests = async () => {\r\n            try {\r\n                const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n                const sessionId = storedUser?.user?.id;\r\n\r\n                if (!sessionId) {\r\n                    console.log(\"No session ID found, skipping pending requests fetch\");\r\n                    return;\r\n                }\r\n\r\n                // Add timeout and better error handling\r\n                const controller = new AbortController();\r\n                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout\r\n\r\n                const res = await axios.get(\r\n                    `https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`,\r\n                    {\r\n                        signal: controller.signal,\r\n                        timeout: 10000,\r\n                        headers: {\r\n                            'Content-Type': 'application/json'\r\n                        }\r\n                    }\r\n                );\r\n\r\n                clearTimeout(timeoutId);\r\n                setPendingCount(res.data.count || 0);\r\n                console.log(\"✅ Pending requests fetched successfully:\", res.data.count);\r\n\r\n            } catch (err) {\r\n                if (err.name === 'AbortError') {\r\n                    console.log(\"⏰ Request timeout - skipping pending requests\");\r\n                } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error')) {\r\n                    console.log(\"🌐 Network error - will retry later\");\r\n                } else {\r\n                    console.error(\"❌ Failed to fetch pending request count:\", err.message);\r\n                }\r\n                // Set default count on error\r\n                setPendingCount(0);\r\n            }\r\n        };\r\n\r\n        // Initial fetch with delay to avoid immediate network issues\r\n        const initialTimeout = setTimeout(() => {\r\n            fetchPendingRequests();\r\n        }, 2000);\r\n\r\n        // OPTIONAL: Poll every 60s for updates (increased interval to reduce network load)\r\n        const interval = setInterval(fetchPendingRequests, 60000);\r\n\r\n        return () => {\r\n            clearTimeout(initialTimeout);\r\n            clearInterval(interval);\r\n        };\r\n    }, []);\r\n\r\n    const [users, setUsers] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                const response = await axios.post(\r\n                    \"https://nextalk-u0y1.onrender.com/displayusersProfile\",\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true\r\n                    }\r\n                );\r\n\r\n                const allUsers = response.data;\r\n                const filtered = allUsers.filter(user => user._id !== sessionId);\r\n                setUsers(filtered);\r\n            } catch (error) {\r\n                console.error(\"Error fetching users:\", error);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (searchTerm.trim() === '') {\r\n            setSearchResults([]);\r\n            return;\r\n        }\r\n\r\n        const results = users.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setSearchResults(results);\r\n    }, [searchTerm, users]);\r\n\r\n\r\n    return (\r\n        <div className=\"dashboard-wrapper\" style={{ background: currentThemeStyles.background, color: currentThemeStyles.color }}>\r\n            {/* Mobile Navbar */}\r\n            <nav className={`navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`}>\r\n                <div className=\"container-fluid\">\r\n                    <Link className=\"navbar-brand fw-bold sleek-brand\" style={{ textDecoration: \"none\" }} href=\"/dashboard/profile\">{brandText}</Link>\r\n                    <button\r\n                        className={`navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`}\r\n                        type=\"button\"\r\n                        onClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n                    >\r\n                        <span className=\"navbar-toggler-icon\"></span>\r\n                    </button>\r\n                </div>\r\n            </nav>\r\n\r\n            {/* Sidebar */}\r\n            <aside className={`sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`}>\r\n                <div className=\"sidebar-header sleek-header\">\r\n                    <h4 className=\"p-3 fw-bold text-uppercase d-none d-lg-block\">{brandText}</h4>\r\n                </div>\r\n\r\n                <ul className=\"nav flex-column p-3 sleek-nav\">\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard\">\r\n                            <i className=\"bi bi-house-fill me-2\"></i>Home\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <button className=\"nav-link sleek-nav-link w-100\" data-bs-toggle=\"offcanvas\" data-bs-target=\"#Search\" style={{ textDecoration: \"none\" }}>\r\n                            <i className=\"bi bi-search me-2\"></i>Search\r\n                        </button>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-box me-2\"></i>Chat with Random\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-plus-square me-2\"></i>Create\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Messages\">\r\n                            <i className=\"bi bi-chat-fill me-2\"></i>Messages\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-cpu me-2\"></i>Chat with NexTalk\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item ot-but\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Settings\">\r\n                            <i className=\"bi bi-gear-wide-connected me-2\"></i>Settings\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link justify-content-between\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Notification\">\r\n                            <div>\r\n                                <i className=\"bi bi-heart me-2\"></i>Notification\r\n                            </div>\r\n                            {pendingCount > 0 && (\r\n                                <span\r\n                                    style={{\r\n                                        backgroundColor: \"#008080\",\r\n                                        color: \"white\",\r\n                                        fontSize: \"0.7rem\",\r\n                                        padding: \"6px 12px\",\r\n                                        borderRadius: \"50%\",\r\n                                        top: \"10px\",\r\n                                        right: \"10px\",\r\n                                    }}\r\n                                >\r\n                                    {pendingCount}\r\n                                </span>\r\n                            )}\r\n                        </Link>\r\n                    </li><br />\r\n                    <li className='nav-item'>\r\n                        <Link href=\"\"\r\n                            className=\"btn btn-primary w-100 p-2 d-lg-none\" style={{ textDecoration: \"none\" }}\r\n                            type=\"button\"\r\n                            onClick={() => setIsSidebarOpen(false)}\r\n                        >Close </Link></li>\r\n                </ul>\r\n\r\n\r\n                <div className=\"sidebar-footer p-3 sleek-footer\">\r\n                    {loading ? (\r\n                        <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ background: \"darkgray\", textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                            <div\r\n                                className=\"skeleton\"\r\n                                style={{\r\n                                    width: \"45px\",\r\n                                    height: \"45px\",\r\n                                    borderRadius: \"50%\",\r\n                                }}\r\n                            ></div>\r\n                            <div>\r\n                                <div\r\n                                    className=\"skeleton\"\r\n                                    style={{\r\n                                        width: \"120px\",\r\n                                        height: \"16px\",\r\n                                        borderRadius: \"4px\",\r\n                                        marginBottom: \"8px\",\r\n                                    }}\r\n                                ></div>\r\n                                <span className=\"sleek-status online\">Online</span>\r\n                            </div>\r\n                        </Link>\r\n                    ) :\r\n                        (\r\n                            <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                                {profile && (\r\n                                    <Image\r\n                                        key={profile.image}\r\n                                        src={profile.image || \"/Images/predefine.webp\"}\r\n                                        width={45}\r\n                                        height={45}\r\n                                        alt=\"User\"\r\n                                        className=\"rounded-circle sleek-avatar\"\r\n                                    />\r\n                                )}\r\n                                <div>\r\n                                    <span className=\"d-block fw-semibold sleek-username\">{profile.name || \"Guest\"}</span>\r\n                                    <span className=\"sleek-status online\">Online</span>\r\n                                </div>\r\n                            </Link>\r\n                        )\r\n                    }\r\n                    <div className=\"mt-4 sleek-actions\">\r\n                        <button\r\n                            onClick={toggleTheme}\r\n                            className=\"btn sleek-btn theme-toggle w-100 mb-2\"\r\n                        >\r\n                            {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\r\n                        </button>\r\n                        <button\r\n                            onClick={handleLogout}\r\n                            className=\"btn sleek-btn logout-btn w-100\"\r\n                        >\r\n                            Log Out\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </aside>\r\n\r\n            {/* Main Content */}\r\n            <main className=\"main-content sleek-main\" style={{ background: currentThemeStyles.chatBackground }}>\r\n                <div className=\"container-fluid p-1 sleek-container\">\r\n                    {children} {/* NOT Outlet! */}\r\n                </div>\r\n                {showConfirm && (\r\n                    <div className=\"modal-overlay\">\r\n                        <div className=\"modal-content\">\r\n                            <h4 className=\"modal-title\">Log Out?</h4>\r\n                            <p className=\"modal-text\">\r\n                                Are you sure you want to log out? You will be signed out of your account and redirected to the login page.\r\n                            </p>\r\n\r\n                            <div className=\"modal-actions\">\r\n                                <button className=\"modal-btn modal-btn-success\" onClick={handleConfirmUpload}>\r\n                                    ✅ Confirm !\r\n                                </button>\r\n                                <button className=\"modal-btn modal-btn-cancel\" onClick={() => setShowConfirm(false)}>\r\n                                    ❌ Cancel\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n                <div className=\"offcanvas offcanvas-start\" style={{ background: currentThemeStyles.cardBg, color: currentThemeStyles.color }} id=\"Search\">\r\n                    <div className=\"offcanvas-header\">\r\n                        <h3 className=\"offcanvas-title\">Search</h3>\r\n                        <button type=\"button\" className=\"btn-close bg-danger\" data-bs-dismiss=\"offcanvas\"></button>\r\n                    </div>\r\n                    <div className=\"offcanvas-body\">\r\n                        <input\r\n                            type=\"search\"\r\n                            name=\"search\"\r\n                            id=\"search\"\r\n                            className=\"form-control mb-3\"\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            style={{\r\n                                backgroundColor: \"white\",\r\n                                transition: \"background 0.3s\",\r\n                                gap: \"10px\",\r\n                                border: \"1px solid #333\"\r\n                            }}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                            onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                        />\r\n                        {\r\n                            loading ? (\r\n                                <div className='d-flex gap-4'>\r\n                                    <div\r\n                                        className=\"skeleton\"\r\n                                        style={{\r\n                                            width: \"45px\",\r\n                                            height: \"45px\",\r\n                                            borderRadius: \"50%\",\r\n                                        }}\r\n                                    ></div>\r\n                                    <div>\r\n                                        <div\r\n                                            className=\"skeleton\"\r\n                                            style={{\r\n                                                width: \"120px\",\r\n                                                height: \"16px\",\r\n                                                borderRadius: \"4px\",\r\n                                                marginBottom: \"8px\",\r\n                                            }}\r\n                                        ></div>\r\n                                    </div>\r\n                                </div>\r\n                            ) : (\r\n                                <div>\r\n                                    {searchResults.map(user => (\r\n                                        <div\r\n                                            key={user._id}\r\n                                            className=\"d-flex gap-4 align-items-center user-result mb-2 p-2 rounded\"\r\n                                            style={{\r\n                                                cursor: \"pointer\",\r\n                                            }}\r\n                                        >\r\n                                            <Image\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={60}\r\n                                                height={60}\r\n                                                className=\"rounded-circle\"\r\n                                                style={{ objectFit: \"cover\" }}\r\n                                            />\r\n                                            <div>\r\n                                                <strong>{user.username}</strong><br />\r\n                                                <span>{user.name}</span>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                    ))}\r\n\r\n                                    {searchResults.length === 0 && searchTerm && (\r\n                                        <div className=\"text-center mt-4 fade-in\">\r\n                                            <svg\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                width=\"64\"\r\n                                                height=\"64\"\r\n                                                fill=\"gray\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                style={{ opacity: 0.5 }}\r\n                                            >\r\n                                                <path d=\"M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z\" />\r\n                                            </svg>\r\n                                            <p className=\"mt-2\">No users found</p>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            )\r\n                        }\r\n                    </div>\r\n                </div>\r\n\r\n            </main>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AARA,cAAc,2CAA2C;;;;;;;;;AAU1C,SAAS,gBAAgB,EAAE,QAAQ,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,YAAS,AAAD,KAAK,iBAAiB;IAE7C,MAAM,cAAc;QAChB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,iBAAiB;IACrB;IAEA,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBAAE,YAAY;gBAAW,OAAO;gBAAW,gBAAgB;gBAAW,QAAQ;YAAoD;QAC7I;QACA,OAAO;YAAE,YAAY;YAAW,OAAO;YAAW,gBAAgB;QAAU;IAChF;IAEA,MAAM,qBAAqB;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa,eAAe,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;YACb,OAAO,IAAI,CAAC;QAChB,OAAO;YACH,IAAI;gBACA,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,QAAQ,OAAO,IAAI;YACvB,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ,GAAG;QAAC;KAAO;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe;QACjB,WAAW;QACX,MAAM,WAAW,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,WAAW;QAE9D,IAAI,CAAC,UAAU,MAAM,IAAI;YACrB,SAAS;YACT,WAAW;YACX;QACJ;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,6CAA6C;gBAC1E,SAAS;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE;gBAC/C;YACJ;YACA,MAAM,iBAAiB,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YAC1D,WAAW;YACX,eAAe;YACf,WAAW;QACf,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YACT,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,uCAAmC;;QAEnC;IACJ,GAAG,EAAE;IAGL,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa,sBAAsB;YACnC,aAAa,SAAS,QAAQ;QAClC,OAAO;YACH,aAAa;QACjB;IACJ,GAAG;QAAC;QAAU;KAAQ;IAGtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;QACjB,eAAe;IACnB;IAEA,MAAM,sBAAsB,OAAO;QAC/B,eAAe,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC;IAChB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,uBAAuB;YACzB,IAAI;gBACA,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;gBACrD,MAAM,YAAY,YAAY,MAAM;gBAEpC,IAAI,CAAC,WAAW;oBACZ,QAAQ,GAAG,CAAC;oBACZ;gBACJ;gBAEA,wCAAwC;gBACxC,MAAM,aAAa,IAAI;gBACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,oBAAoB;gBAEnF,MAAM,MAAM,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CACvB,CAAC,0DAA0D,EAAE,WAAW,EACxE;oBACI,QAAQ,WAAW,MAAM;oBACzB,SAAS;oBACT,SAAS;wBACL,gBAAgB;oBACpB;gBACJ;gBAGJ,aAAa;gBACb,gBAAgB,IAAI,IAAI,CAAC,KAAK,IAAI;gBAClC,QAAQ,GAAG,CAAC,4CAA4C,IAAI,IAAI,CAAC,KAAK;YAE1E,EAAE,OAAO,KAAK;gBACV,IAAI,IAAI,IAAI,KAAK,cAAc;oBAC3B,QAAQ,GAAG,CAAC;gBAChB,OAAO,IAAI,IAAI,IAAI,KAAK,mBAAmB,IAAI,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAC9E,QAAQ,GAAG,CAAC;gBAChB,OAAO;oBACH,QAAQ,KAAK,CAAC,4CAA4C,IAAI,OAAO;gBACzE;gBACA,6BAA6B;gBAC7B,gBAAgB;YACpB;QACJ;QAEA,6DAA6D;QAC7D,MAAM,iBAAiB,WAAW;YAC9B;QACJ,GAAG;QAEH,mFAAmF;QACnF,MAAM,WAAW,YAAY,sBAAsB;QAEnD,OAAO;YACH,aAAa;YACb,cAAc;QAClB;IACJ,GAAG,EAAE;IAEL,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa;YACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YACpC,iBAAiB;YAEjB,IAAI;gBACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;oBACI,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,iBAAiB;gBACrB;gBAGJ,MAAM,WAAW,SAAS,IAAI;gBAC9B,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBACtD,SAAS;YACb,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,yBAAyB;YAC3C;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,WAAW,IAAI,OAAO,IAAI;YAC1B,iBAAiB,EAAE;YACnB;QACJ;QAEA,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAG3D,iBAAiB;IACrB,GAAG;QAAC;QAAY;KAAM;IAGtB,qBACI,qKAAC;QAAI,WAAU;QAAoB,OAAO;YAAE,YAAY,mBAAmB,UAAU;YAAE,OAAO,mBAAmB,KAAK;QAAC;;0BAEnH,qKAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,UAAU,SAAS,YAAY,WAAW,UAAU,CAAC;0BACxF,cAAA,qKAAC;oBAAI,WAAU;;sCACX,qKAAC,qHAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,OAAO;gCAAE,gBAAgB;4BAAO;4BAAG,MAAK;sCAAsB;;;;;;sCACjH,qKAAC;4BACG,WAAW,CAAC,6BAA6B,EAAE,UAAU,SAAS,iBAAiB,iBAAiB;4BAChG,MAAK;4BACL,SAAS,IAAM,iBAAiB,CAAC;sCAEjC,cAAA,qKAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,qKAAC;gBAAM,WAAW,CAAC,sBAAsB,EAAE,UAAU,SAAS,YAAY,WAAW,CAAC,EAAE,gBAAgB,SAAS,IAAI;;kCACjH,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAG,WAAU;sCAAgD;;;;;;;;;;;kCAGlE,qKAAC;wBAAG,WAAU;;0CACV,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAGjD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC;oCAAO,WAAU;oCAAgC,kBAAe;oCAAY,kBAAe;oCAAU,OAAO;wCAAE,gBAAgB;oCAAO;;sDAClI,qKAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;0CAG7C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA6B;;;;;;;;;;;;0CAGlD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA2B;;;;;;;;;;;;0CAGhD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqC;;;;;;;;;;;;0CAG1D,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAAkD,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDAC9I,qKAAC;;8DACG,qKAAC;oDAAE,WAAU;;;;;;gDAAuB;;;;;;;wCAEvC,eAAe,mBACZ,qKAAC;4CACG,OAAO;gDACH,iBAAiB;gDACjB,OAAO;gDACP,UAAU;gDACV,SAAS;gDACT,cAAc;gDACd,KAAK;gDACL,OAAO;4CACX;sDAEC;;;;;;;;;;;;;;;;;0CAIZ,qKAAC;;;;;0CACN,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,MAAK;oCACP,WAAU;oCAAsC,OAAO;wCAAE,gBAAgB;oCAAO;oCAChF,MAAK;oCACL,SAAS,IAAM,iBAAiB;8CACnC;;;;;;;;;;;;;;;;;kCAIT,qKAAC;wBAAI,WAAU;;4BACV,wBACG,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,YAAY;oCAAY,gBAAgB;gCAAO;gCAAG,WAAU;;kDACzI,qKAAC;wCACG,WAAU;wCACV,OAAO;4CACH,OAAO;4CACP,QAAQ;4CACR,cAAc;wCAClB;;;;;;kDAEJ,qKAAC;;0DACG,qKAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;oDACd,cAAc;gDAClB;;;;;;0DAEJ,qKAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;qDAK1C,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,gBAAgB;gCAAO;gCAAG,WAAU;;oCAChH,yBACG,qKAAC,sHAAA,CAAA,UAAK;wCAEF,KAAK,QAAQ,KAAK,IAAI;wCACtB,OAAO;wCACP,QAAQ;wCACR,KAAI;wCACJ,WAAU;uCALL,QAAQ,KAAK;;;;;kDAQ1B,qKAAC;;0DACG,qKAAC;gDAAK,WAAU;0DAAsC,QAAQ,IAAI,IAAI;;;;;;0DACtE,qKAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAKtD,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCACG,SAAS;wCACT,WAAU;kDAET,UAAU,SAAS,eAAe;;;;;;kDAEvC,qKAAC;wCACG,SAAS;wCACT,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,qKAAC;gBAAK,WAAU;gBAA0B,OAAO;oBAAE,YAAY,mBAAmB,cAAc;gBAAC;;kCAC7F,qKAAC;wBAAI,WAAU;;4BACV;4BAAS;;;;;;;oBAEb,6BACG,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAI,WAAU;;8CACX,qKAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,qKAAC;oCAAE,WAAU;8CAAa;;;;;;8CAI1B,qKAAC;oCAAI,WAAU;;sDACX,qKAAC;4CAAO,WAAU;4CAA8B,SAAS;sDAAqB;;;;;;sDAG9E,qKAAC;4CAAO,WAAU;4CAA6B,SAAS,IAAM,eAAe;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOrG,qKAAC;wBAAI,WAAU;wBAA4B,OAAO;4BAAE,YAAY,mBAAmB,MAAM;4BAAE,OAAO,mBAAmB,KAAK;wBAAC;wBAAG,IAAG;;0CAC7H,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAG,WAAU;kDAAkB;;;;;;kDAChC,qKAAC;wCAAO,MAAK;wCAAS,WAAU;wCAAsB,mBAAgB;;;;;;;;;;;;0CAE1E,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCACG,MAAK;wCACL,MAAK;wCACL,IAAG;wCACH,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,OAAO;4CACH,iBAAiB;4CACjB,YAAY;4CACZ,KAAK;4CACL,QAAQ;wCACZ;wCACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;oCAG3D,wBACI,qKAAC;wCAAI,WAAU;;0DACX,qKAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;gDAClB;;;;;;0DAEJ,qKAAC;0DACG,cAAA,qKAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,cAAc;oDAClB;;;;;;;;;;;;;;;;6DAKZ,qKAAC;;4CACI,cAAc,GAAG,CAAC,CAAA,qBACf,qKAAC;oDAEG,WAAU;oDACV,OAAO;wDACH,QAAQ;oDACZ;;sEAEA,qKAAC,sHAAA,CAAA,UAAK;4DACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;4DAC5B,KAAK,KAAK,IAAI;4DACd,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,OAAO;gEAAE,WAAW;4DAAQ;;;;;;sEAEhC,qKAAC;;8EACG,qKAAC;8EAAQ,KAAK,QAAQ;;;;;;8EAAU,qKAAC;;;;;8EACjC,qKAAC;8EAAM,KAAK,IAAI;;;;;;;;;;;;;mDAhBf,KAAK,GAAG;;;;;4CAsBpB,cAAc,MAAM,KAAK,KAAK,4BAC3B,qKAAC;gDAAI,WAAU;;kEACX,qKAAC;wDACG,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,MAAK;wDACL,SAAQ;wDACR,OAAO;4DAAE,SAAS;wDAAI;kEAEtB,cAAA,qKAAC;4DAAK,GAAE;;;;;;;;;;;kEAEZ,qKAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Dashboard/ModalPortal.js"], "sourcesContent": ["import { useEffect } from 'react';\r\nimport { createPortal } from 'react-dom';\r\n\r\nexport default function ModalPortal({ children }) {\r\n  const modalRoot = typeof window !== 'undefined' ? document.getElementById('modal-root') : null;\r\n\r\n  useEffect(() => {\r\n    document.body.style.overflow = 'hidden';\r\n    document.documentElement.style.overflow = 'hidden';\r\n\r\n    return () => {\r\n      document.body.style.overflow = 'auto';\r\n      document.documentElement.style.overflow = 'auto';\r\n    };\r\n  }, []);\r\n\r\n  return modalRoot ? createPortal(children, modalRoot) : null;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe,SAAS,YAAY,EAAE,QAAQ,EAAE;IAC9C,MAAM,YAAY,6EAAwE;IAE1F,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG;QAE1C,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG;QAC5C;IACF,GAAG,EAAE;IAEL,OAAO,6EAAgD;AACzD", "debugId": null}}, {"offset": {"line": 1126, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Dashboard/index.js"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport axios from '../../utils/axiosConfig';\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport DashboardLayout from '../Components/DashboardLayout';\r\nimport Image from \"next/image\";\r\nimport Head from 'next/head';\r\nimport ModalPortal from \"./ModalPortal\";\r\n\r\nexport default function Home() {\r\n    const { theme } = useTheme();\r\n    const [users, setUsers] = useState([]);\r\n    const [notifications, setNotifications] = useState([]);\r\n    const [following, setFollowing] = useState(new Set());\r\n    const [accepted, setAccepted] = useState(new Set());\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [sessionUser, setSessionUser] = useState(null); // 👈 NEW\r\n    const [selectedUser, setSelectedUser] = useState(null); // 👈 MODAL only\r\n\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem('user'));\r\n            const sessionId = storedUser?.user?.id;\r\n\r\n            if (!sessionId) {\r\n                setError('No session found.');\r\n                return;\r\n            }\r\n\r\n            try {\r\n                // 1. Fetch all users\r\n                const response = await axios.post(\r\n                    'https://nextalk-u0y1.onrender.com/displayusersProfile',\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true,\r\n                    }\r\n                );\r\n\r\n                const allUsers = response.data;\r\n                const filteredUsers = allUsers.filter(user => user._id !== sessionId);\r\n                const sessionUser = allUsers.find(user => user._id === sessionId);\r\n                setSessionUser(sessionUser);\r\n\r\n                // 2. Fetch follow status\r\n                const followRes = await fetch(`https://nextalk-u0y1.onrender.com/follow-status/${sessionId}`);\r\n                const followData = await followRes.json();\r\n                setFollowing(new Set(followData.following));\r\n                setAccepted(new Set(followData.accepted));\r\n\r\n                // 3. Finally set users after everything else is ready\r\n                setUsers(filteredUsers);\r\n                setLoading(false);\r\n            } catch (err) {\r\n                console.error('❌ Error fetching data:', err);\r\n                setError('Failed to load data.');\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n\r\n\r\n    useEffect(() => {\r\n        localStorage.setItem('notifications', JSON.stringify(notifications));\r\n    }, [notifications]);\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',\r\n                color: '#e2e8f0',\r\n                cardBg: 'rgba(255, 255, 255, 0.1)',\r\n                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',\r\n                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',\r\n                notificationBg: 'rgba(51, 65, 85, 0.9)',\r\n            };\r\n        }\r\n        return {\r\n            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',\r\n            color: '#1e293b',\r\n            cardBg: 'rgba(255, 255, 255, 0.8)',\r\n            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)',\r\n        };\r\n    };\r\n\r\n    const styles = getThemeStyles();\r\n\r\n    const addNotification = (type, userName, userAvatar) => {\r\n        const newNotification = {\r\n            id: Date.now(),\r\n            type,\r\n            message: getNotificationMessage(type, userName),\r\n            avatar: predefine,\r\n            createdAt: new Date().toISOString(),\r\n            read: false,\r\n        };\r\n        setNotifications(prev => [newNotification, ...prev].slice(0, 50));\r\n    };\r\n\r\n    const getNotificationMessage = (type, userName) => {\r\n        switch (type) {\r\n            case 'new_message': return `${userName} sent you a new message!`;\r\n            case 'followed': return `${userName} followed you!`;\r\n            case 'request_accepted': return `${userName} accepted your follow request!`;\r\n            case 'user_online': return `${userName} is now online!`;\r\n            default: return 'New event occurred!';\r\n        }\r\n    };\r\n\r\n    const handleFollow = async (userId) => {\r\n        const user = users.find(u => u._id === userId);\r\n\r\n        try {\r\n            const response = await fetch(\"https://nextalk-u0y1.onrender.com/follow\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                    \"Content-Type\": \"application/json\"\r\n                },\r\n                credentials: \"include\", // if you're using sessions/cookies\r\n                body: JSON.stringify({\r\n                    followerId: sessionUser._id,\r\n                    followeeId: userId\r\n                })\r\n            });\r\n\r\n            const data = await response.json();\r\n\r\n            if (response.ok) {\r\n                setFollowing(prev => {\r\n                    const updated = new Set(prev);\r\n                    updated.add(userId);\r\n                    return updated;\r\n                });\r\n\r\n                console.log(`You followed ${user.name} at ${data.follow.time}`);\r\n                // Optional: Show toast or notification with data.follow.time\r\n            } else {\r\n                alert(data.message);\r\n            }\r\n\r\n        } catch (error) {\r\n            console.error(\"Follow request failed:\", error);\r\n        }\r\n    };\r\n\r\n\r\n    const handleFollowAll = () => {\r\n        const newFollowing = new Set(Requested);\r\n        users.forEach(user => {\r\n            if (!newFollowing.has(user._id)) {\r\n                newFollowing.add(user._id);\r\n                addNotification('followed', user.name, user.avatar);\r\n            }\r\n        });\r\n        setFollowing(newFollowing);\r\n    };\r\n\r\n    const openModal = (user) => {\r\n        setSelectedUser(user);\r\n    };\r\n\r\n    const closeModal = () => {\r\n        setSelectedUser(null);\r\n    };\r\n    useEffect(() => {\r\n        if (selectedUser) {\r\n            document.body.style.overflow = 'hidden';\r\n            document.documentElement.style.overflow = 'hidden';\r\n        } else {\r\n            document.body.style.overflow = 'auto';\r\n            document.documentElement.style.overflow = 'auto';\r\n        }\r\n    }, [selectedUser]);\r\n    if (loading) return <div className=\"custom-loader-overlay\">\r\n        <svg viewBox=\"0 0 100 100\">\r\n            <g fill=\"none\" stroke=\"#fff\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"6\">\r\n                {/* left line */}\r\n                <path d=\"M 21 40 V 59\">\r\n                    <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 21 59; 180 21 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                </path>\r\n                {/* right line */}\r\n                <path d=\"M 79 40 V 59\">\r\n                    <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 79 59; -180 79 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                </path>\r\n                {/* top line */}\r\n                <path d=\"M 50 21 V 40\">\r\n                    <animate attributeName=\"d\" values=\"M 50 21 V 40; M 50 59 V 40\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                </path>\r\n                {/* bottom line */}\r\n                <path d=\"M 50 60 V 79\">\r\n                    <animate attributeName=\"d\" values=\"M 50 60 V 79; M 50 98 V 79\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                </path>\r\n                {/* top box */}\r\n                <path d=\"M 50 21 L 79 40 L 50 60 L 21 40 Z\">\r\n                    <animate attributeName=\"stroke\" values=\"rgba(255,255,255,1); rgba(100,100,100,0)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                </path>\r\n                {/* mid box */}\r\n                <path d=\"M 50 40 L 79 59 L 50 79 L 21 59 Z\" />\r\n                {/* bottom box */}\r\n                <path d=\"M 50 59 L 79 78 L 50 98 L 21 78 Z\">\r\n                    <animate attributeName=\"stroke\" values=\"rgba(100,100,100,0); rgba(255,255,255,1)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                </path>\r\n                <animateTransform attributeName=\"transform\" type=\"translate\" values=\"0 0; 0 -19\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n            </g>\r\n        </svg>\r\n    </div>;\r\n    //if (error) return <div className=\"error\">{error}</div>;\r\n\r\n    return (\r\n        <DashboardLayout>\r\n            <Head>\r\n                <title>Welcome to NexTalk</title>\r\n            </Head>\r\n            <div className=\"home-container\" style={{ background: styles.background, color: styles.color }}>\r\n                <div className=\"home-content\">\r\n                    {/* Horizontal Scrollable Image Section */}\r\n                    <section className=\"image-section\">\r\n                        <div className=\"image-list\" aria-label=\"User profile images\">\r\n                            {/* 🔥 Always show session user image */}\r\n                            {sessionUser && (\r\n                                <div key={sessionUser._id} className=\"image-card session-user\">\r\n                                    <div className=\"image-wrapper\">\r\n                                        <Image\r\n                                            key={sessionUser.image}\r\n                                            src={sessionUser.image || predefine}\r\n                                            alt={sessionUser.name}\r\n                                            width={85}\r\n                                            height={85}\r\n                                            className=\"rounded-circle image-item\"\r\n                                        /><br />\r\n                                        <span className=\"session-badge\">You</span>\r\n                                        <div className=\"tooltip\">\r\n                                            <span></span>\r\n                                            <span>{sessionUser.bio || \"No bio available\"}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n\r\n                            {/* 🔁 Render other users */}\r\n                            {users.filter(user => accepted.has(user._id))\r\n                                .map(user => (\r\n                                    <div key={user._id} className=\"image-card\">\r\n                                        <div className=\"image-wrapper\" onClick={() => setSelectedUser(user)}>\r\n                                            <Image\r\n                                                key={user.image}\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={85}\r\n                                                height={85}\r\n                                                className=\"image-item rounded-circle\"\r\n                                            />\r\n                                            <div className=\"tooltip\">\r\n                                                <span>{user.name}</span>\r\n                                                <span>{user.bio || \"No bio available\"}</span>\r\n                                            </div>\r\n                                        </div>\r\n                                        <span className=\"image-username\">{user.name}</span>\r\n                                    </div>\r\n                                ))}\r\n\r\n                        </div>\r\n\r\n                    </section>\r\n                    \r\n                    {/* Suggested for you Section */}\r\n                    <section className=\"suggested-section animate-fade-in\">\r\n                        <div className=\"suggested-header\">\r\n                            <h2 className=\"section-title\">Suggested for You</h2>\r\n                            <a href=\"#\" className=\"see-all\">See All</a>\r\n                        </div>\r\n                        <button className=\"follow-all-btn\" onClick={handleFollowAll}>\r\n                            Follow All\r\n                        </button>\r\n                        <div className=\"suggested-grid\">\r\n                            {users\r\n                                .filter(user => !accepted.has(user._id) && user._id !== sessionUser._id)\r\n                                .map(user => {\r\n                                    const isFollowing = following.has(user._id);\r\n\r\n                                    return (\r\n                                        <div key={user._id} className=\"suggested-card\">\r\n                                            <Image\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={85}\r\n                                                height={85}\r\n                                                className=\"image-item rounded-circle\"\r\n                                            />\r\n                                            <div className=\"suggested-info\">\r\n                                                <span className=\"suggested-name\">{user.name}</span>\r\n                                                <span className=\"suggested-followed-by\">\r\n                                                    Followed by {user.followedBy || \"user1, user2\"} + {user.followedByCount || 3} more\r\n                                                </span>\r\n                                                <br />\r\n                                                {isFollowing ? (\r\n                                                    <button disabled className=\"btn btn-secondary\">Requested</button>\r\n                                                ) : (\r\n                                                    <button\r\n                                                        onClick={() => handleFollow(user._id)}\r\n                                                        className=\"btn btn-primary\"\r\n                                                    >\r\n                                                        Follow\r\n                                                    </button>\r\n                                                )}\r\n                                            </div>\r\n                                        </div>\r\n                                    );\r\n                                })}\r\n                        </div>\r\n\r\n                    </section>\r\n                </div>\r\n\r\n                {/* Modal for Profile Preview */}\r\n                <ModalPortal>\r\n                    {selectedUser && (\r\n                        <div className=\"modal-overlay\" onClick={closeModal}>\r\n                            <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\r\n                                <button className=\"modal-close\" ><i onClick={closeModal} className=\"bi bi-x-lg\"></i></button>\r\n                                <div className=\"modal-body\">\r\n                                    {selectedUser.image ? (\r\n                                        <Image key={selectedUser.image}\r\n                                            width={85}\r\n                                            height={85} src={selectedUser.image} alt={selectedUser.name} className={`modal-image ${selectedUser.isSessionUser ? 'session-user' : ''}`} />\r\n                                    ) : (\r\n                                        <Image src={predefine} alt={selectedUser.name} className={`modal-image ${selectedUser.isSessionUser ? 'session-user' : ''}`} />\r\n                                    )}\r\n                                    <h3>{selectedUser.name}</h3>\r\n                                    <p>{selectedUser.bio || \"No bio available\"}</p>\r\n                                    <button\r\n                                        className=\"btn btn-outline-primary w-50 btn-sm\"\r\n                                        style={{ background: following.has(selectedUser._id) }}\r\n                                    >\r\n                                        Message\r\n                                    </button>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n                </ModalPortal>\r\n            </div>\r\n        </DashboardLayout>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEe,SAAS;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,SAAS;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,gBAAgB;IAGxE,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,YAAY;YACd,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YAEpC,IAAI,CAAC,WAAW;gBACZ,SAAS;gBACT;YACJ;YAEA,IAAI;gBACA,qBAAqB;gBACrB,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;oBACI,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,iBAAiB;gBACrB;gBAGJ,MAAM,WAAW,SAAS,IAAI;gBAC9B,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBAC3D,MAAM,cAAc,SAAS,IAAI,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBACvD,eAAe;gBAEf,yBAAyB;gBACzB,MAAM,YAAY,MAAM,MAAM,CAAC,gDAAgD,EAAE,WAAW;gBAC5F,MAAM,aAAa,MAAM,UAAU,IAAI;gBACvC,aAAa,IAAI,IAAI,WAAW,SAAS;gBACzC,YAAY,IAAI,IAAI,WAAW,QAAQ;gBAEvC,sDAAsD;gBACtD,SAAS;gBACT,WAAW;YACf,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS;gBACT,WAAW;YACf;QACJ;QAEA;IACJ,GAAG,EAAE;IAIL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACzD,GAAG;QAAC;KAAc;IAElB,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,aAAa;gBACb,gBAAgB;YACpB;QACJ;QACA,OAAO;YACH,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,aAAa;QACjB;IACJ;IAEA,MAAM,SAAS;IAEf,MAAM,kBAAkB,CAAC,MAAM,UAAU;QACrC,MAAM,kBAAkB;YACpB,IAAI,KAAK,GAAG;YACZ;YACA,SAAS,uBAAuB,MAAM;YACtC,QAAQ,qRAAA,CAAA,UAAS;YACjB,WAAW,IAAI,OAAO,WAAW;YACjC,MAAM;QACV;QACA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK,CAAC,KAAK,CAAC,GAAG;IACjE;IAEA,MAAM,yBAAyB,CAAC,MAAM;QAClC,OAAQ;YACJ,KAAK;gBAAe,OAAO,GAAG,SAAS,wBAAwB,CAAC;YAChE,KAAK;gBAAY,OAAO,GAAG,SAAS,cAAc,CAAC;YACnD,KAAK;gBAAoB,OAAO,GAAG,SAAS,8BAA8B,CAAC;YAC3E,KAAK;gBAAe,OAAO,GAAG,SAAS,eAAe,CAAC;YACvD;gBAAS,OAAO;QACpB;IACJ;IAEA,MAAM,eAAe,OAAO;QACxB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;QAEvC,IAAI;YACA,MAAM,WAAW,MAAM,MAAM,4CAA4C;gBACrE,QAAQ;gBACR,SAAS;oBACL,gBAAgB;gBACpB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACjB,YAAY,YAAY,GAAG;oBAC3B,YAAY;gBAChB;YACJ;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACb,aAAa,CAAA;oBACT,MAAM,UAAU,IAAI,IAAI;oBACxB,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACX;gBAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC,IAAI,EAAE;YAC9D,6DAA6D;YACjE,OAAO;gBACH,MAAM,KAAK,OAAO;YACtB;QAEJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,0BAA0B;QAC5C;IACJ;IAGA,MAAM,kBAAkB;QACpB,MAAM,eAAe,IAAI,IAAI;QAC7B,MAAM,OAAO,CAAC,CAAA;YACV,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK,GAAG,GAAG;gBAC7B,aAAa,GAAG,CAAC,KAAK,GAAG;gBACzB,gBAAgB,YAAY,KAAK,IAAI,EAAE,KAAK,MAAM;YACtD;QACJ;QACA,aAAa;IACjB;IAEA,MAAM,YAAY,CAAC;QACf,gBAAgB;IACpB;IAEA,MAAM,aAAa;QACf,gBAAgB;IACpB;IACA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,cAAc;YACd,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG;QAC9C,OAAO;YACH,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAC/B,SAAS,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG;QAC9C;IACJ,GAAG;QAAC;KAAa;IACjB,IAAI,SAAS,qBAAO,qKAAC;QAAI,WAAU;kBAC/B,cAAA,qKAAC;YAAI,SAAQ;sBACT,cAAA,qKAAC;gBAAE,MAAK;gBAAO,QAAO;gBAAO,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;;kCAElF,qKAAC;wBAAK,GAAE;kCACJ,cAAA,qKAAC;4BAAiB,eAAc;4BAAY,MAAK;4BAAS,QAAO;4BAAqB,KAAI;4BAAK,aAAY;;;;;;;;;;;kCAG/G,qKAAC;wBAAK,GAAE;kCACJ,cAAA,qKAAC;4BAAiB,eAAc;4BAAY,MAAK;4BAAS,QAAO;4BAAsB,KAAI;4BAAK,aAAY;;;;;;;;;;;kCAGhH,qKAAC;wBAAK,GAAE;kCACJ,cAAA,qKAAC;4BAAQ,eAAc;4BAAI,QAAO;4BAA6B,KAAI;4BAAK,aAAY;;;;;;;;;;;kCAGxF,qKAAC;wBAAK,GAAE;kCACJ,cAAA,qKAAC;4BAAQ,eAAc;4BAAI,QAAO;4BAA6B,KAAI;4BAAK,aAAY;;;;;;;;;;;kCAGxF,qKAAC;wBAAK,GAAE;kCACJ,cAAA,qKAAC;4BAAQ,eAAc;4BAAS,QAAO;4BAA2C,KAAI;4BAAK,aAAY;;;;;;;;;;;kCAG3G,qKAAC;wBAAK,GAAE;;;;;;kCAER,qKAAC;wBAAK,GAAE;kCACJ,cAAA,qKAAC;4BAAQ,eAAc;4BAAS,QAAO;4BAA2C,KAAI;4BAAK,aAAY;;;;;;;;;;;kCAE3G,qKAAC;wBAAiB,eAAc;wBAAY,MAAK;wBAAY,QAAO;wBAAa,KAAI;wBAAK,aAAY;;;;;;;;;;;;;;;;;;;;;;IAIlH,yDAAyD;IAEzD,qBACI,qKAAC,+HAAA,CAAA,UAAe;;0BACZ,qKAAC,qHAAA,CAAA,UAAI;0BACD,cAAA,qKAAC;8BAAM;;;;;;;;;;;0BAEX,qKAAC;gBAAI,WAAU;gBAAiB,OAAO;oBAAE,YAAY,OAAO,UAAU;oBAAE,OAAO,OAAO,KAAK;gBAAC;;kCACxF,qKAAC;wBAAI,WAAU;;0CAEX,qKAAC;gCAAQ,WAAU;0CACf,cAAA,qKAAC;oCAAI,WAAU;oCAAa,cAAW;;wCAElC,6BACG,qKAAC;4CAA0B,WAAU;sDACjC,cAAA,qKAAC;gDAAI,WAAU;;kEACX,qKAAC,sHAAA,CAAA,UAAK;wDAEF,KAAK,YAAY,KAAK,IAAI,qRAAA,CAAA,UAAS;wDACnC,KAAK,YAAY,IAAI;wDACrB,OAAO;wDACP,QAAQ;wDACR,WAAU;uDALL,YAAY,KAAK;;;;;kEAMxB,qKAAC;;;;;kEACH,qKAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,qKAAC;wDAAI,WAAU;;0EACX,qKAAC;;;;;0EACD,qKAAC;0EAAM,YAAY,GAAG,IAAI;;;;;;;;;;;;;;;;;;2CAb5B,YAAY,GAAG;;;;;wCAoB5B,MAAM,MAAM,CAAC,CAAA,OAAQ,SAAS,GAAG,CAAC,KAAK,GAAG,GACtC,GAAG,CAAC,CAAA,qBACD,qKAAC;gDAAmB,WAAU;;kEAC1B,qKAAC;wDAAI,WAAU;wDAAgB,SAAS,IAAM,gBAAgB;;0EAC1D,qKAAC,sHAAA,CAAA,UAAK;gEAEF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;gEAC5B,KAAK,KAAK,IAAI;gEACd,OAAO;gEACP,QAAQ;gEACR,WAAU;+DALL,KAAK,KAAK;;;;;0EAOnB,qKAAC;gEAAI,WAAU;;kFACX,qKAAC;kFAAM,KAAK,IAAI;;;;;;kFAChB,qKAAC;kFAAM,KAAK,GAAG,IAAI;;;;;;;;;;;;;;;;;;kEAG3B,qKAAC;wDAAK,WAAU;kEAAkB,KAAK,IAAI;;;;;;;+CAfrC,KAAK,GAAG;;;;;;;;;;;;;;;;0CAwBlC,qKAAC;gCAAQ,WAAU;;kDACf,qKAAC;wCAAI,WAAU;;0DACX,qKAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,qKAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAU;;;;;;;;;;;;kDAEpC,qKAAC;wCAAO,WAAU;wCAAiB,SAAS;kDAAiB;;;;;;kDAG7D,qKAAC;wCAAI,WAAU;kDACV,MACI,MAAM,CAAC,CAAA,OAAQ,CAAC,SAAS,GAAG,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,YAAY,GAAG,EACtE,GAAG,CAAC,CAAA;4CACD,MAAM,cAAc,UAAU,GAAG,CAAC,KAAK,GAAG;4CAE1C,qBACI,qKAAC;gDAAmB,WAAU;;kEAC1B,qKAAC,sHAAA,CAAA,UAAK;wDACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;wDAC5B,KAAK,KAAK,IAAI;wDACd,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAEd,qKAAC;wDAAI,WAAU;;0EACX,qKAAC;gEAAK,WAAU;0EAAkB,KAAK,IAAI;;;;;;0EAC3C,qKAAC;gEAAK,WAAU;;oEAAwB;oEACvB,KAAK,UAAU,IAAI;oEAAe;oEAAI,KAAK,eAAe,IAAI;oEAAE;;;;;;;0EAEjF,qKAAC;;;;;4DACA,4BACG,qKAAC;gEAAO,QAAQ;gEAAC,WAAU;0EAAoB;;;;;qFAE/C,qKAAC;gEACG,SAAS,IAAM,aAAa,KAAK,GAAG;gEACpC,WAAU;0EACb;;;;;;;;;;;;;+CApBH,KAAK,GAAG;;;;;wCA2B1B;;;;;;;;;;;;;;;;;;kCAOhB,qKAAC,0HAAA,CAAA,UAAW;kCACP,8BACG,qKAAC;4BAAI,WAAU;4BAAgB,SAAS;sCACpC,cAAA,qKAAC;gCAAI,WAAU;gCAAgB,SAAS,CAAC,IAAM,EAAE,eAAe;;kDAC5D,qKAAC;wCAAO,WAAU;kDAAe,cAAA,qKAAC;4CAAE,SAAS;4CAAY,WAAU;;;;;;;;;;;kDACnE,qKAAC;wCAAI,WAAU;;4CACV,aAAa,KAAK,iBACf,qKAAC,sHAAA,CAAA,UAAK;gDACF,OAAO;gDACP,QAAQ;gDAAI,KAAK,aAAa,KAAK;gDAAE,KAAK,aAAa,IAAI;gDAAE,WAAW,CAAC,YAAY,EAAE,aAAa,aAAa,GAAG,iBAAiB,IAAI;+CAFjI,aAAa,KAAK;;;;qEAI9B,qKAAC,sHAAA,CAAA,UAAK;gDAAC,KAAK,qRAAA,CAAA,UAAS;gDAAE,KAAK,aAAa,IAAI;gDAAE,WAAW,CAAC,YAAY,EAAE,aAAa,aAAa,GAAG,iBAAiB,IAAI;;;;;;0DAE/H,qKAAC;0DAAI,aAAa,IAAI;;;;;;0DACtB,qKAAC;0DAAG,aAAa,GAAG,IAAI;;;;;;0DACxB,qKAAC;gDACG,WAAU;gDACV,OAAO;oDAAE,YAAY,UAAU,GAAG,CAAC,aAAa,GAAG;gDAAE;0DACxD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrC", "debugId": null}}]}