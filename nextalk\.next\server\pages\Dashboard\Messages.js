const CHUNK_PUBLIC_PATH = "server/pages/Dashboard/Messages.js";
const runtime = require("../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_95f47d48._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__656b87da._.js");
runtime.loadChunk("server/chunks/ssr/_9e7f5c81._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__8b8dfb2b._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_173a5315._.js");
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/pages/Dashboard/Messages.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/pages/_document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/pages/_app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/pages/Dashboard/Messages.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/pages/_document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/pages/_app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
