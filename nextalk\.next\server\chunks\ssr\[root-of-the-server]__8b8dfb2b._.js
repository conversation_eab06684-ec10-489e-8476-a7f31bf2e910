module.exports = {

"[externals]/axios [external] (axios, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("axios");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/utils/axiosConfig.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/axios [external] (axios, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
// Create an instance
const axiosInstance = __TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__["default"].create({
    baseURL: process.env.REACT_APP_API_URL,
    withCredentials: true,
    headers: {
        "Content-Type": "application/json"
    }
});
const __TURBOPACK__default__export__ = axiosInstance;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/public/Images/predefine.webp (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/predefine.008bb5f7.webp");}}),
"[project]/public/Images/predefine.webp.mjs { IMAGE => \"[project]/public/Images/predefine.webp (static in ecmascript)\" } [ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/Images/predefine.webp (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$29$__["default"],
    width: 200,
    height: 200,
    blurDataURL: "data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==",
    blurWidth: 8,
    blurHeight: 8
};
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/pages/Components/DashboardLayout.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// DashboardLayout.jsx
__turbopack_context__.s({
    "default": (()=>DashboardLayout)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/Images/predefine.webp.mjs { IMAGE => "[project]/public/Images/predefine.webp (static in ecmascript)" } [ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/context/ThemeContext.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/axiosConfig.js [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
'use client'; // if you're using App Router (recommended)
;
;
;
;
;
;
;
;
function DashboardLayout({ children }) {
    const [isSidebarOpen, setIsSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const { theme, handleThemeClick } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])(); // corrected here
    const toggleTheme = ()=>{
        const newTheme = theme === 'dark' ? 'light' : 'dark';
        handleThemeClick(newTheme);
    };
    const getThemeStyles = ()=>{
        if (theme === 'dark') {
            return {
                background: '#0f172a',
                color: '#e2e8f0',
                chatBackground: '#1e293b',
                cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)'
            };
        }
        return {
            background: '#f1f5f9',
            color: '#1e293b',
            chatBackground: '#ffffff'
        };
    };
    const currentThemeStyles = getThemeStyles();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const storedUser = sessionStorage.getItem("user");
        if (!storedUser) {
            router.push("/");
        } else {
            try {
                const parsed = JSON.parse(storedUser);
                setUser(parsed.user);
            } catch (err) {
                console.error("Error parsing sessionStorage user:", err);
                router.push("/");
            }
        }
    }, [
        router
    ]);
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [tempProfile, setTempProfile] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(profile);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const fetchProfile = async ()=>{
        setLoading(true);
        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');
        if (!userData?.user?.id) {
            setError('No user data found. Please log in.');
            setLoading(false);
            return;
        }
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].get('https://nextalk-u0y1.onrender.com/profile', {
                headers: {
                    Authorization: `Bearer ${userData.user.id}`
                }
            });
            const fetchedProfile = response.data.user || response.data;
            setProfile(fetchedProfile);
            setTempProfile(fetchedProfile);
            setLoading(false);
        } catch (err) {
            const errorMessage = err.response?.data?.message || 'Failed to load profile.';
            setError(errorMessage);
            setLoading(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, []);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [brandText, setBrandText] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])("Nextalk");
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (pathname === "/Dashboard/Profile") {
            setBrandText(profile?.name || "User");
        } else {
            setBrandText("Nextalk");
        }
    }, [
        pathname,
        profile
    ]);
    const [showConfirm, setShowConfirm] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const handleLogout = ()=>{
        setShowConfirm(true);
    };
    const handleConfirmUpload = async (e)=>{
        sessionStorage.removeItem("user");
        router.push("/");
    };
    const [pendingCount, setPendingCount] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(0);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchPendingRequests = async ()=>{
            const storedUser = JSON.parse(sessionStorage.getItem("user"));
            const sessionId = storedUser?.user?.id;
            if (!sessionId) return;
            try {
                const res = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].get(`https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`);
                setPendingCount(res.data.count);
            } catch (err) {
                console.error("❌ Failed to fetch pending request count:", err);
            }
        };
        fetchPendingRequests();
        // OPTIONAL: Poll every 30s for updates
        const interval = setInterval(fetchPendingRequests, 30000);
        return ()=>clearInterval(interval); // cleanup on unmount
    }, []);
    const [users, setUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])('');
    const [searchResults, setSearchResults] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [sessionUserId, setSessionUserId] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchUsers = async ()=>{
            const storedUser = JSON.parse(sessionStorage.getItem("user"));
            const sessionId = storedUser?.user?.id;
            setSessionUserId(sessionId);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].post("https://nextalk-u0y1.onrender.com/displayusersProfile", {}, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    withCredentials: true
                });
                const allUsers = response.data;
                const filtered = allUsers.filter((user)=>user._id !== sessionId);
                setUsers(filtered);
            } catch (error) {
                console.error("Error fetching users:", error);
            }
        };
        fetchUsers();
    }, []);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (searchTerm.trim() === '') {
            setSearchResults([]);
            return;
        }
        const results = users.filter((user)=>user.name.toLowerCase().includes(searchTerm.toLowerCase()));
        setSearchResults(results);
    }, [
        searchTerm,
        users
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        className: "dashboard-wrapper",
        style: {
            background: currentThemeStyles.background,
            color: currentThemeStyles.color
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("nav", {
                className: `navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "container-fluid",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                            className: "navbar-brand fw-bold sleek-brand",
                            style: {
                                textDecoration: "none"
                            },
                            href: "/dashboard/profile",
                            children: brandText
                        }, void 0, false, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 184,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                            className: `navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`,
                            type: "button",
                            onClick: ()=>setIsSidebarOpen(!isSidebarOpen),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                className: "navbar-toggler-icon"
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 190,
                                columnNumber: 25
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 185,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/pages/Components/DashboardLayout.js",
                    lineNumber: 183,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Components/DashboardLayout.js",
                lineNumber: 182,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("aside", {
                className: `sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "sidebar-header sleek-header",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                            className: "p-3 fw-bold text-uppercase d-none d-lg-block",
                            children: brandText
                        }, void 0, false, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 198,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 197,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("ul", {
                        className: "nav flex-column p-3 sleek-nav",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-house-fill me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 204,
                                            columnNumber: 29
                                        }, this),
                                        "Home"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 203,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 202,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                    className: "nav-link sleek-nav-link w-100",
                                    "data-bs-toggle": "offcanvas",
                                    "data-bs-target": "#Search",
                                    style: {
                                        textDecoration: "none"
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-search me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 209,
                                            columnNumber: 29
                                        }, this),
                                        "Search"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 208,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 207,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Chats",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-box me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 214,
                                            columnNumber: 29
                                        }, this),
                                        "Chat with Random"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 213,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 212,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Chats",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-plus-square me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 219,
                                            columnNumber: 29
                                        }, this),
                                        "Create"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 218,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 217,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Messages",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-chat-fill me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 224,
                                            columnNumber: 29
                                        }, this),
                                        "Messages"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 223,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 222,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Chats",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-cpu me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 229,
                                            columnNumber: 29
                                        }, this),
                                        "Chat with NexTalk"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 228,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 227,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item ot-but",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Settings",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-gear-wide-connected me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 234,
                                            columnNumber: 29
                                        }, this),
                                        "Settings"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 233,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 232,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link justify-content-between",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Notification",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                    className: "bi bi-heart me-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                    lineNumber: 240,
                                                    columnNumber: 33
                                                }, this),
                                                "Notification"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 239,
                                            columnNumber: 29
                                        }, this),
                                        pendingCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                            style: {
                                                backgroundColor: "#008080",
                                                color: "white",
                                                fontSize: "0.7rem",
                                                padding: "6px 12px",
                                                borderRadius: "50%",
                                                top: "10px",
                                                right: "10px"
                                            },
                                            children: pendingCount
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 243,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 238,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 237,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("br", {}, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 258,
                                columnNumber: 26
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "",
                                    className: "btn btn-primary w-100 p-2 d-lg-none",
                                    style: {
                                        textDecoration: "none"
                                    },
                                    type: "button",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    children: "Close "
                                }, void 0, false, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 260,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 259,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 201,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "sidebar-footer p-3 sleek-footer",
                        children: [
                            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/Dashboard/Profile",
                                onClick: ()=>setIsSidebarOpen(false),
                                style: {
                                    background: "darkgray",
                                    textDecoration: "none"
                                },
                                className: "user-profile sleek-profile nav-link d-flex align-items-center gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "skeleton",
                                        style: {
                                            width: "45px",
                                            height: "45px",
                                            borderRadius: "50%"
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 271,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "skeleton",
                                                style: {
                                                    width: "120px",
                                                    height: "16px",
                                                    borderRadius: "4px",
                                                    marginBottom: "8px"
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 280,
                                                columnNumber: 33
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                className: "sleek-status online",
                                                children: "Online"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 289,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 279,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 270,
                                columnNumber: 25
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/Dashboard/Profile",
                                onClick: ()=>setIsSidebarOpen(false),
                                style: {
                                    textDecoration: "none"
                                },
                                className: "user-profile sleek-profile nav-link d-flex align-items-center gap-3",
                                children: [
                                    profile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        src: profile.image || "/Images/predefine.webp",
                                        width: 45,
                                        height: 45,
                                        alt: "User",
                                        className: "rounded-circle sleek-avatar"
                                    }, profile.image, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 296,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                className: "d-block fw-semibold sleek-username",
                                                children: profile.name || "Guest"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 306,
                                                columnNumber: 37
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                className: "sleek-status online",
                                                children: "Online"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 307,
                                                columnNumber: 37
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 305,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 294,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "mt-4 sleek-actions",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        onClick: toggleTheme,
                                        className: "btn sleek-btn theme-toggle w-100 mb-2",
                                        children: theme === 'dark' ? 'Light Mode' : 'Dark Mode'
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 313,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        onClick: handleLogout,
                                        className: "btn sleek-btn logout-btn w-100",
                                        children: "Log Out"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 319,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 312,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 268,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Components/DashboardLayout.js",
                lineNumber: 196,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("main", {
                className: "main-content sleek-main",
                style: {
                    background: currentThemeStyles.chatBackground
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "container-fluid p-1 sleek-container",
                        children: [
                            children,
                            " "
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 331,
                        columnNumber: 17
                    }, this),
                    showConfirm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "modal-overlay",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "modal-content",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                                    className: "modal-title",
                                    children: "Log Out?"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 337,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                    className: "modal-text",
                                    children: "Are you sure you want to log out? You will be signed out of your account and redirected to the login page."
                                }, void 0, false, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 338,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "modal-actions",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            className: "modal-btn modal-btn-success",
                                            onClick: handleConfirmUpload,
                                            children: "✅ Confirm !"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 343,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            className: "modal-btn modal-btn-cancel",
                                            onClick: ()=>setShowConfirm(false),
                                            children: "❌ Cancel"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 346,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 342,
                                    columnNumber: 29
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 336,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 335,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "offcanvas offcanvas-start",
                        style: {
                            background: currentThemeStyles.cardBg,
                            color: currentThemeStyles.color
                        },
                        id: "Search",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "offcanvas-header",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                        className: "offcanvas-title",
                                        children: "Search"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 355,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        className: "btn-close bg-danger",
                                        "data-bs-dismiss": "offcanvas"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 356,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 354,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "offcanvas-body",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                        type: "search",
                                        name: "search",
                                        id: "search",
                                        className: "form-control mb-3",
                                        placeholder: "Search users...",
                                        value: searchTerm,
                                        style: {
                                            backgroundColor: "white",
                                            transition: "background 0.3s",
                                            gap: "10px",
                                            border: "1px solid #333"
                                        },
                                        onChange: (e)=>setSearchTerm(e.target.value),
                                        onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = "white",
                                        onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = "whitesmock"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 359,
                                        columnNumber: 25
                                    }, this),
                                    loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "d-flex gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "skeleton",
                                                style: {
                                                    width: "45px",
                                                    height: "45px",
                                                    borderRadius: "50%"
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 379,
                                                columnNumber: 37
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "skeleton",
                                                    style: {
                                                        width: "120px",
                                                        height: "16px",
                                                        borderRadius: "4px",
                                                        marginBottom: "8px"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                    lineNumber: 388,
                                                    columnNumber: 41
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 387,
                                                columnNumber: 37
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 378,
                                        columnNumber: 33
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: [
                                            searchResults.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "d-flex gap-4 align-items-center user-result mb-2 p-2 rounded",
                                                    style: {
                                                        cursor: "pointer"
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            src: user.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                            alt: user.name,
                                                            width: 60,
                                                            height: 60,
                                                            className: "rounded-circle",
                                                            style: {
                                                                objectFit: "cover"
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                                            lineNumber: 409,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("strong", {
                                                                    children: user.username
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                                    lineNumber: 418,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                                    lineNumber: 418,
                                                                    columnNumber: 81
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                    children: user.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                                    lineNumber: 419,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                                            lineNumber: 417,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, user._id, true, {
                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                    lineNumber: 402,
                                                    columnNumber: 41
                                                }, this)),
                                            searchResults.length === 0 && searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "text-center mt-4 fade-in",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("svg", {
                                                        xmlns: "http://www.w3.org/2000/svg",
                                                        width: "64",
                                                        height: "64",
                                                        fill: "gray",
                                                        viewBox: "0 0 24 24",
                                                        style: {
                                                            opacity: 0.5
                                                        },
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                                            d: "M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                                            lineNumber: 435,
                                                            columnNumber: 49
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                                        lineNumber: 427,
                                                        columnNumber: 45
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                        className: "mt-2",
                                                        children: "No users found"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                                        lineNumber: 437,
                                                        columnNumber: 45
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 426,
                                                columnNumber: 41
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 400,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 358,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 353,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Components/DashboardLayout.js",
                lineNumber: 330,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/pages/Components/DashboardLayout.js",
        lineNumber: 180,
        columnNumber: 9
    }, this);
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[project]/pages/Dashboard/Messages.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>Messages)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/axiosConfig.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/Images/predefine.webp.mjs { IMAGE => "[project]/public/Images/predefine.webp (static in ecmascript)" } [ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/pages/Components/DashboardLayout.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/router.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/context/ThemeContext.js [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
function Messages() {
    const [users, setUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [sessionUser, setSessionUser] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [following, setFollowing] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [accepted, setAccepted] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])('');
    const [selectedChat, setSelectedChat] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])('');
    const [displayCount, setDisplayCount] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(6);
    const [visibleUsers, setVisibleUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [filteredFollowers, setFilteredFollowers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({
        username: 'user123',
        name: 'John Doe',
        email: '<EMAIL>',
        bio: 'No bio yet.',
        avatar: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        posts: 15,
        followersCount: 250,
        followingCount: 180
    });
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const [showOnlineOnly, setShowOnlineOnly] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Chat functionality states
    const [chatMessages, setChatMessages] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [newMessage, setNewMessage] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])("");
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])(null);
    const [sessionUserId, setSessionUserId] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [selectedUser, setSelectedUser] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    // Mock last message data and reactions
    const [lastMessages, setLastMessages] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [reactions, setReactions] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    // Local Storage utility functions
    const getChatHistory = (userId)=>{
        try {
            if (!sessionUserId || !userId) return [];
            const chatKey1 = `chat_${sessionUserId}_${userId}`;
            const stored = localStorage.getItem(chatKey1);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading chat history:', error);
            return [];
        }
    };
    const saveChatHistory = (userId, messages)=>{
        try {
            if (!sessionUserId || !userId) return;
            const chatKey1 = `chat_${sessionUserId}_${userId}`;
            // Limit messages to last 1000 to prevent localStorage overflow
            const limitedMessages = messages.slice(-1000);
            localStorage.setItem(chatKey1, JSON.stringify(limitedMessages));
        } catch (error) {
            console.error('Error saving chat history:', error);
            // If localStorage is full, try to clear old chats
            if (error.name === 'QuotaExceededError') {
                console.warn('localStorage quota exceeded, clearing old chats...');
                clearOldChats();
                // Try saving again
                try {
                    localStorage.setItem(chatKey, JSON.stringify(messages.slice(-500)));
                } catch (retryError) {
                    console.error('Failed to save even after cleanup:', retryError);
                }
            }
        }
    };
    const clearOldChats = ()=>{
        try {
            const keys = Object.keys(localStorage);
            const chatKeys = keys.filter((key)=>key.startsWith('chat_'));
            // Remove oldest chats (simple cleanup strategy)
            chatKeys.slice(0, Math.floor(chatKeys.length / 2)).forEach((key)=>{
                localStorage.removeItem(key);
            });
        } catch (error) {
            console.error('Error clearing old chats:', error);
        }
    };
    const getLastMessageForUser = (userId)=>{
        const messages = getChatHistory(userId);
        if (messages.length === 0) return 'No messages yet';
        const lastMsg = messages[messages.length - 1];
        const preview = lastMsg.text.length > 30 ? lastMsg.text.substring(0, 30) + '...' : lastMsg.text;
        return lastMsg.sender === 'You' ? `You: ${preview}` : preview;
    };
    const formatMessageTime = (timestamp)=>{
        const now = new Date();
        const msgTime = new Date(timestamp);
        const diffInHours = (now - msgTime) / (1000 * 60 * 60);
        if (diffInHours < 24) {
            return msgTime.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
            });
        } else if (diffInHours < 168) {
            return msgTime.toLocaleDateString([], {
                weekday: 'short'
            });
        } else {
            return msgTime.toLocaleDateString([], {
                month: 'short',
                day: 'numeric'
            });
        }
    };
    // Handle chat selection with URL update
    const handleChatSelect = (user)=>{
        setSelectedChat(user._id);
        setSelectedUser(user);
        // Update URL with user ID
        router.push(`/Dashboard/Messages?userId=${user._id}`, undefined, {
            shallow: true
        });
        // Load chat history for this user
        const history = getChatHistory(user._id);
        setChatMessages((prev)=>({
                ...prev,
                [user._id]: history
            }));
    };
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchData = async ()=>{
            const storedUser = JSON.parse(sessionStorage.getItem('user'));
            const sessionId = storedUser?.user?.id;
            setSessionUserId(sessionId);
            if (!sessionId) {
                setError('No session found.');
                setLoading(false);
                return;
            }
            try {
                // Fetch all users
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].post('https://nextalk-u0y1.onrender.com/displayusersProfile', {}, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    withCredentials: true
                });
                const personally = response.data;
                const filteredUsers = personally.filter((user)=>user._id !== sessionId);
                const sessionUser = personally.find((user)=>user._id === sessionId);
                setSessionUser(sessionUser);
                // Fetch follow status
                const followRes = await fetch(`https://nextalk-u0y1.onrender.com/follow-status/${sessionId}`);
                const followData = await followRes.json();
                setFollowing(new Set(followData.following));
                setAccepted(new Set(followData.accepted));
                // Generate last messages from local storage
                const mockLastMessages = filteredUsers.reduce((acc, user)=>({
                        ...acc,
                        [user._id]: getLastMessageForUser(user._id)
                    }), {});
                setLastMessages(mockLastMessages);
                setUsers(filteredUsers);
                setLoading(false);
                // Check if there's a userId in URL and auto-select that chat
                const { userId } = router.query;
                if (userId) {
                    const userToSelect = filteredUsers.find((u)=>u._id === userId);
                    if (userToSelect) {
                        handleChatSelect(userToSelect);
                    }
                }
            } catch (err) {
                console.error('❌ Error fetching data:', err);
                setError('Failed to load data.');
                setLoading(false);
            }
        };
        fetchData();
    }, [
        router.query
    ]);
    // Auto-scroll to bottom when new messages are added
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({
                behavior: 'smooth'
            });
        }
    }, [
        chatMessages,
        selectedChat
    ]);
    // Filter users based on search query and online status
    const filteredUsers = users.filter((user)=>accepted.has(user._id)).filter((user)=>user.name.toLowerCase().includes(searchQuery.toLowerCase())).filter((user)=>showOnlineOnly ? user.isOnline : true);
    const getThemeStyles = ()=>{
        if (theme === 'dark') {
            return {
                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
                color: '#e2e8f0',
                cardBg: 'rgba(255, 255, 255, 0.1)',
                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',
                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',
                notificationBg: 'rgba(51, 65, 85, 0.9)'
            };
        }
        return {
            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',
            color: '#1e293b',
            cardBg: 'rgba(255, 255, 255, 0.8)',
            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)'
        };
    };
    const styles = getThemeStyles();
    const getThemeStyless = ()=>{
        if (theme === 'dark') {
            return {
                background: '#1e293b',
                color: '#e2e8f0',
                inputBg: '#334155',
                inputColor: '#e2e8f0',
                messageBgYou: '#3b82f6',
                messageBgFriend: '#4b5563'
            };
        }
        // Default to light styles for 'homeback' or any other theme
        return {
            background: '#ffffff',
            color: '#1e293b',
            inputBg: '#f1f5f9',
            inputColor: '#1e293b',
            messageBgYou: '#3b82f6',
            messageBgFriend: '#e5e7eb'
        };
    };
    const currentThemeStyles = getThemeStyless();
    const handleSendMessage = (e)=>{
        e.preventDefault();
        if (newMessage.trim() && selectedChat) {
            const currentMessages = chatMessages[selectedChat] || [];
            const newMsg = {
                id: Date.now(),
                sender: "You",
                text: newMessage,
                timestamp: new Date().toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                })
            };
            const updatedMessages = [
                ...currentMessages,
                newMsg
            ];
            // Update chat messages state
            setChatMessages((prev)=>({
                    ...prev,
                    [selectedChat]: updatedMessages
                }));
            // Save to local storage
            saveChatHistory(selectedChat, updatedMessages);
            // Update last messages for the chat list
            setLastMessages((prev)=>({
                    ...prev,
                    [selectedChat]: newMsg.text.length > 30 ? newMsg.text.substring(0, 30) + '...' : newMsg.text
                }));
            setNewMessage("");
        }
    };
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (!profile || !Array.isArray(profile.followers)) return;
        // Extract follower user IDs from the populated followers array
        const followersArray = profile.followers.map((f)=>f._id?.toString());
        // Filter only users who are followers
        const followedUsers = users.filter((user)=>followersArray.includes(user._id?.toString()));
        // Filter for search
        const filtered = followedUsers.filter((user)=>user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()));
        setFilteredFollowers(filtered); // useful for Load More check
        // Control visible users based on search or default count
        if (searchTerm.trim() === '') {
            setVisibleUsers(followedUsers.slice(0, displayCount));
        } else {
            setVisibleUsers(filtered.slice(0, displayCount));
        }
    }, [
        searchTerm,
        users,
        displayCount,
        profile
    ]);
    const handleLoadMore = ()=>{
        const prevCount = displayCount;
        const newCount = prevCount + 6;
        setDisplayCount(newCount);
        // Scroll to the previous 6th user (after DOM update)
        setTimeout(()=>{
            const userElems = document.querySelectorAll(".user-result");
            if (userElems[prevCount]) {
                userElems[prevCount].scrollIntoView({
                    behavior: "smooth",
                    block: "start"
                });
            }
        }, 100); // wait a moment for new DOM elements to render
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
        children: [
            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "custom-loader-overlay",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 100 100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("g", {
                        fill: "none",
                        stroke: "#fff",
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: "6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 21 40 V 59",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animateTransform", {
                                    attributeName: "transform",
                                    type: "rotate",
                                    values: "0 21 59; 180 21 59",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 348,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 347,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 79 40 V 59",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animateTransform", {
                                    attributeName: "transform",
                                    type: "rotate",
                                    values: "0 79 59; -180 79 59",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 352,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 351,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 21 V 40",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "d",
                                    values: "M 50 21 V 40; M 50 59 V 40",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 356,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 355,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 60 V 79",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "d",
                                    values: "M 50 60 V 79; M 50 98 V 79",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 360,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 359,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 21 L 79 40 L 50 60 L 21 40 Z",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "stroke",
                                    values: "rgba(255,255,255,1); rgba(100,100,100,0)",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 364,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 363,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 40 L 79 59 L 50 79 L 21 59 Z"
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 367,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 59 L 79 78 L 50 98 L 21 78 Z",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "stroke",
                                    values: "rgba(100,100,100,0); rgba(255,255,255,1)",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 370,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 369,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animateTransform", {
                                attributeName: "transform",
                                type: "translate",
                                values: "0 0; 0 -19",
                                dur: "2s",
                                repeatCount: "indefinite"
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 372,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 345,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/pages/Dashboard/Messages.js",
                    lineNumber: 344,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 343,
                columnNumber: 17
            }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "error",
                children: error
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 377,
                columnNumber: 17
            }, this) : filteredUsers.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "no-friends",
                children: "No friends to message."
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 379,
                columnNumber: 17
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: styles.chatContainer,
                style: {
                    background: getThemeStyles().background,
                    color: getThemeStyles().color
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: styles.chatList,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h2", {
                                className: styles.chatTitle,
                                children: "Chats"
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 384,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                style: {
                                    padding: '0 20px 15px 20px'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                    type: "text",
                                    placeholder: "Search chats...",
                                    value: searchQuery,
                                    onChange: (e)=>setSearchQuery(e.target.value),
                                    style: {
                                        width: '100%',
                                        padding: '10px 15px',
                                        border: '1px solid rgba(255, 255, 255, 0.2)',
                                        borderRadius: '20px',
                                        background: 'rgba(255, 255, 255, 0.1)',
                                        color: getThemeStyles().color,
                                        outline: 'none',
                                        fontSize: '0.9rem'
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 386,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 385,
                                columnNumber: 25
                            }, this),
                            filteredUsers.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: `${styles.chatItem} ${selectedChat === user._id ? styles.active : ''}`,
                                    onClick: ()=>handleChatSelect(user),
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: styles.avatarContainer,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    src: user.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                    alt: user.name,
                                                    width: 40,
                                                    height: 40,
                                                    className: styles.avatar
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 410,
                                                    columnNumber: 37
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                    className: styles.statusIndicator
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 417,
                                                    columnNumber: 37
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 409,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: styles.chatDetails,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: styles.chatHeader,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                            className: styles.userName,
                                                            children: user.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 421,
                                                            columnNumber: 41
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                            className: styles.timestamp,
                                                            children: (()=>{
                                                                const messages = getChatHistory(user._id);
                                                                if (messages.length > 0) {
                                                                    return formatMessageTime(messages[messages.length - 1].timestamp);
                                                                }
                                                                return 'Now';
                                                            })()
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 422,
                                                            columnNumber: 41
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 420,
                                                    columnNumber: 37
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                    className: styles.lastMessage,
                                                    children: lastMessages[user._id] || 'No messages yet'
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 432,
                                                    columnNumber: 37
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 419,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, user._id, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 404,
                                    columnNumber: 29
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 383,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: styles.chatPanel,
                        children: selectedChat ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: styles.chatWindow,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "d-flex gap-5",
                                    style: {
                                        alignItems: "center"
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                src: filteredUsers.find((u)=>u._id === selectedChat)?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                alt: filteredUsers.find((u)=>u._id === selectedChat)?.image,
                                                width: 100,
                                                height: 100,
                                                className: "avatar"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 445,
                                                columnNumber: 41
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 444,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                                    className: "chat-header",
                                                    children: [
                                                        "Chat with ",
                                                        filteredUsers.find((u)=>u._id === selectedChat)?.name
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 453,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                    className: "chat-content",
                                                    children: "This is the chat window. Start messaging here!"
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 454,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 452,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 443,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "",
                                    style: {
                                        background: currentThemeStyles.background,
                                        color: currentThemeStyles.color
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: styles.chatMessages,
                                            children: [
                                                chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? chatMessages[selectedChat].map((msg)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: `${styles.message} ${msg.sender === "You" ? styles.messageYou : styles.messageFriend}`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: styles.messageBubble,
                                                            style: {
                                                                background: msg.sender === "You" ? currentThemeStyles.messageBgYou : currentThemeStyles.messageBgFriend,
                                                                color: msg.sender === "You" ? '#ffffff' : currentThemeStyles.color
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                    className: styles.messageText,
                                                                    children: msg.text
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 480,
                                                                    columnNumber: 57
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                    className: styles.messageTimestamp,
                                                                    children: msg.timestamp
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 481,
                                                                    columnNumber: 57
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 471,
                                                            columnNumber: 53
                                                        }, this)
                                                    }, msg.id, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 467,
                                                        columnNumber: 49
                                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: styles.noMessages,
                                                    style: {
                                                        color: currentThemeStyles.color
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                            className: "bi bi-chat-dots"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 487,
                                                            columnNumber: 49
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                                                            children: "No messages yet"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 488,
                                                            columnNumber: 49
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                            children: "Start the conversation by sending a message!"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 489,
                                                            columnNumber: 49
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 486,
                                                    columnNumber: 45
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    ref: messagesEndRef
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 492,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 464,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("form", {
                                            className: styles.chatInputForm,
                                            onSubmit: handleSendMessage,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                                    type: "text",
                                                    className: styles.chatInput,
                                                    placeholder: "Type a message...",
                                                    value: newMessage,
                                                    onChange: (e)=>setNewMessage(e.target.value),
                                                    style: {
                                                        background: currentThemeStyles.inputBg,
                                                        color: currentThemeStyles.inputColor
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 495,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                    type: "submit",
                                                    className: styles.chatSendBtn,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                        className: "bi bi-send-fill"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 507,
                                                        columnNumber: 45
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 506,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 494,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 457,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/pages/Dashboard/Messages.js",
                            lineNumber: 442,
                            columnNumber: 29
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: styles.instructions,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: styles.iconContainer,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                            className: styles.chatIcon,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                className: "bi bi-chat-right-text"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 516,
                                                columnNumber: 75
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 516,
                                            columnNumber: 41
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 515,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                        className: styles.instructionTitle,
                                        children: "Your messages"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 518,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                        className: styles.instructionText,
                                        children: "Send a message to start a chat."
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 519,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        className: styles.sendButton,
                                        "data-bs-toggle": "modal",
                                        "data-bs-target": "#followers",
                                        children: [
                                            profile.followersCount || "0",
                                            " Send message"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 520,
                                        columnNumber: 37
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 514,
                                columnNumber: 33
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/pages/Dashboard/Messages.js",
                            lineNumber: 513,
                            columnNumber: 29
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 440,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 381,
                columnNumber: 17
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "modal",
                id: "followers",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "modal-dialog",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "modal-content",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "d-flex justify-content-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h5", {
                                            children: "Followers"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 534,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 533,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            className: "btn-close bg-primary",
                                            "data-bs-dismiss": "modal"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 537,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 536,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 532,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 539,
                                columnNumber: 31
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                            type: "search",
                                            name: "search",
                                            id: "search",
                                            className: "form-control mb-3",
                                            placeholder: "Search users...",
                                            value: searchTerm,
                                            style: {
                                                backgroundColor: "white",
                                                transition: "background 0.3s",
                                                gap: "10px",
                                                border: "1px solid #333"
                                            },
                                            onChange: (e)=>setSearchTerm(e.target.value),
                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = "white",
                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = "whitesmock"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 542,
                                            columnNumber: 33
                                        }, this),
                                        loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "d-flex gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "skeleton",
                                                    style: {
                                                        width: "45px",
                                                        height: "45px",
                                                        borderRadius: "50%"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 563,
                                                    columnNumber: 45
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "skeleton",
                                                        style: {
                                                            width: "120px",
                                                            height: "16px",
                                                            borderRadius: "4px",
                                                            marginBottom: "8px"
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 572,
                                                        columnNumber: 49
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 571,
                                                    columnNumber: 45
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 562,
                                            columnNumber: 41
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
                                            children: [
                                                visibleUsers.length > 0 ? visibleUsers.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "d-flex align-items-center mb-2 p-2 rounded user-result",
                                                        style: {
                                                            justifyContent: "space-between"
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                className: "d-flex gap-4 align-items-center",
                                                                style: {
                                                                    cursor: "pointer"
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        src: user.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                        alt: user.name,
                                                                        width: 60,
                                                                        height: 60,
                                                                        className: "rounded-circle",
                                                                        style: {
                                                                            objectFit: "cover"
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 594,
                                                                        columnNumber: 61
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("strong", {
                                                                                children: user.username
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 603,
                                                                                columnNumber: 65
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 603,
                                                                                columnNumber: 97
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                                children: user.name
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 604,
                                                                                columnNumber: 65
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 602,
                                                                        columnNumber: 61
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 588,
                                                                columnNumber: 57
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                                    className: "btn btn-primary btn-sm",
                                                                    onClick: ()=>{
                                                                        handleChatSelect(user);
                                                                        // Close modal
                                                                        const modal = document.getElementById('followers');
                                                                        const modalInstance = window.bootstrap?.Modal?.getInstance(modal);
                                                                        if (modalInstance) {
                                                                            modalInstance.hide();
                                                                        }
                                                                    },
                                                                    "data-bs-dismiss": "modal",
                                                                    children: "Message"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 608,
                                                                    columnNumber: 61
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 607,
                                                                columnNumber: 57
                                                            }, this)
                                                        ]
                                                    }, user._id, true, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 587,
                                                        columnNumber: 53
                                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "no-followers-container text-center mt-5",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "icon-wrapper mb-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                                className: "bi bi-person-x",
                                                                style: {
                                                                    fontSize: "3rem",
                                                                    color: "#6c757d"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 629,
                                                                columnNumber: 57
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 628,
                                                            columnNumber: 53
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h5", {
                                                            style: {
                                                                color: "#6c757d"
                                                            },
                                                            children: "No Followers Found"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 631,
                                                            columnNumber: 53
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 627,
                                                    columnNumber: 49
                                                }, this),
                                                visibleUsers.length < filteredFollowers.length && (searchTerm.trim() === '' ? profile.followers.length || 0 : users.filter((user)=>profile.followers.includes(user._id) && (user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()))).length) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "text-center mt-3",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                        className: "btn w-100 btn-outline-primary",
                                                        onClick: handleLoadMore,
                                                        children: "Load More"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 646,
                                                        columnNumber: 57
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 645,
                                                    columnNumber: 53
                                                }, this)
                                            ]
                                        }, void 0, true)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 541,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 540,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 531,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/pages/Dashboard/Messages.js",
                    lineNumber: 530,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 529,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/pages/Dashboard/Messages.js",
        lineNumber: 341,
        columnNumber: 9
    }, this);
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8b8dfb2b._.js.map