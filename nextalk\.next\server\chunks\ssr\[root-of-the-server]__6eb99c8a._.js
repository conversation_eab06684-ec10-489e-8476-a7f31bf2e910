module.exports = {

"[externals]/axios [external] (axios, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("axios");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/utils/axiosConfig.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/axios [external] (axios, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
// Create an instance with smart server URL detection
let baseURL;
// Check if we're on localhost (client-side)
if ("TURBOPACK compile-time falsy", 0) {
    baseURL = 'http://localhost:5000';
} else if (process.env.NEXT_PUBLIC_SERVER_URL) {
    baseURL = process.env.NEXT_PUBLIC_SERVER_URL;
} else if (process.env.REACT_APP_API_URL) {
    baseURL = process.env.REACT_APP_API_URL;
} else {
    // Default to production server
    baseURL = 'https://nextalk-u0y1.onrender.com';
}
console.log('🌐 Axios baseURL:', baseURL);
const axiosInstance = __TURBOPACK__imported__module__$5b$externals$5d2f$axios__$5b$external$5d$__$28$axios$2c$__esm_import$29$__["default"].create({
    baseURL: baseURL,
    withCredentials: true,
    headers: {
        "Content-Type": "application/json"
    },
    timeout: 10000
});
// Add request interceptor for debugging
axiosInstance.interceptors.request.use((config)=>{
    console.log('📤 Axios request:', config.method?.toUpperCase(), config.url);
    return config;
}, (error)=>{
    console.error('❌ Axios request error:', error);
    return Promise.reject(error);
});
// Add response interceptor for debugging
axiosInstance.interceptors.response.use((response)=>{
    console.log('✅ Axios response:', response.status, response.config.url);
    return response;
}, (error)=>{
    console.error('❌ Axios response error:', error.message);
    if (error.code === 'ECONNREFUSED' || error.message === 'Network Error') {
        console.error('🚨 Server connection failed. Is your backend running on', baseURL, '?');
    }
    return Promise.reject(error);
});
const __TURBOPACK__default__export__ = axiosInstance;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/public/Images/predefine.webp (static in ecmascript)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v("/_next/static/media/predefine.008bb5f7.webp");}}),
"[project]/public/Images/predefine.webp.mjs { IMAGE => \"[project]/public/Images/predefine.webp (static in ecmascript)\" } [ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$29$__ = __turbopack_context__.i("[project]/public/Images/predefine.webp (static in ecmascript)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$29$__["default"],
    width: 200,
    height: 200,
    blurDataURL: "data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==",
    blurWidth: 8,
    blurHeight: 8
};
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/pages/Components/DashboardLayout.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// DashboardLayout.jsx
__turbopack_context__.s({
    "default": (()=>DashboardLayout)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/link.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/Images/predefine.webp.mjs { IMAGE => "[project]/public/Images/predefine.webp (static in ecmascript)" } [ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/context/ThemeContext.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/axiosConfig.js [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
'use client'; // if you're using App Router (recommended)
;
;
;
;
;
;
;
;
function DashboardLayout({ children }) {
    const [isSidebarOpen, setIsSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const { theme, handleThemeClick } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])(); // corrected here
    const toggleTheme = ()=>{
        const newTheme = theme === 'dark' ? 'light' : 'dark';
        handleThemeClick(newTheme);
    };
    const getThemeStyles = ()=>{
        if (theme === 'dark') {
            return {
                background: '#0f172a',
                color: '#e2e8f0',
                chatBackground: '#1e293b',
                cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)'
            };
        }
        return {
            background: '#f1f5f9',
            color: '#1e293b',
            chatBackground: '#ffffff'
        };
    };
    const currentThemeStyles = getThemeStyles();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const storedUser = sessionStorage.getItem("user");
        if (!storedUser) {
            router.push("/");
        } else {
            try {
                const parsed = JSON.parse(storedUser);
                setUser(parsed.user);
            } catch (err) {
                console.error("Error parsing sessionStorage user:", err);
                router.push("/");
            }
        }
    }, [
        router
    ]);
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [tempProfile, setTempProfile] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(profile);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const fetchProfile = async ()=>{
        setLoading(true);
        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');
        if (!userData?.user?.id) {
            setError('No user data found. Please log in.');
            setLoading(false);
            return;
        }
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].get('https://nextalk-u0y1.onrender.com/profile', {
                headers: {
                    Authorization: `Bearer ${userData.user.id}`
                }
            });
            const fetchedProfile = response.data.user || response.data;
            setProfile(fetchedProfile);
            setTempProfile(fetchedProfile);
            setLoading(false);
        } catch (err) {
            const errorMessage = err.response?.data?.message || 'Failed to load profile.';
            setError(errorMessage);
            setLoading(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, []);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [brandText, setBrandText] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])("Nextalk");
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (pathname === "/Dashboard/Profile") {
            setBrandText(profile?.name || "User");
        } else {
            setBrandText("Nextalk");
        }
    }, [
        pathname,
        profile
    ]);
    const [showConfirm, setShowConfirm] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const handleLogout = ()=>{
        setShowConfirm(true);
    };
    const handleConfirmUpload = async (e)=>{
        sessionStorage.removeItem("user");
        router.push("/");
    };
    const [pendingCount, setPendingCount] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(0);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchPendingRequests = async ()=>{
            try {
                const storedUser = JSON.parse(sessionStorage.getItem("user"));
                const sessionId = storedUser?.user?.id;
                if (!sessionId) {
                    console.log("No session ID found, skipping pending requests fetch");
                    return;
                }
                // Add timeout and better error handling
                const controller = new AbortController();
                const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout
                const res = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].get(`https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`, {
                    signal: controller.signal,
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                clearTimeout(timeoutId);
                setPendingCount(res.data.count || 0);
                console.log("✅ Pending requests fetched successfully:", res.data.count);
            } catch (err) {
                if (err.name === 'AbortError') {
                    console.log("⏰ Request timeout - skipping pending requests");
                } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error')) {
                    console.log("🌐 Network error - will retry later");
                } else {
                    console.error("❌ Failed to fetch pending request count:", err.message);
                }
                // Set default count on error
                setPendingCount(0);
            }
        };
        // Initial fetch with delay to avoid immediate network issues
        const initialTimeout = setTimeout(()=>{
            fetchPendingRequests();
        }, 2000);
        // OPTIONAL: Poll every 60s for updates (increased interval to reduce network load)
        const interval = setInterval(fetchPendingRequests, 60000);
        return ()=>{
            clearTimeout(initialTimeout);
            clearInterval(interval);
        };
    }, []);
    const [users, setUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])('');
    const [searchResults, setSearchResults] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [sessionUserId, setSessionUserId] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchUsers = async ()=>{
            const storedUser = JSON.parse(sessionStorage.getItem("user"));
            const sessionId = storedUser?.user?.id;
            setSessionUserId(sessionId);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].post("https://nextalk-u0y1.onrender.com/displayusersProfile", {}, {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    withCredentials: true
                });
                const allUsers = response.data;
                const filtered = allUsers.filter((user)=>user._id !== sessionId);
                setUsers(filtered);
            } catch (error) {
                console.error("Error fetching users:", error);
            }
        };
        fetchUsers();
    }, []);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (searchTerm.trim() === '') {
            setSearchResults([]);
            return;
        }
        const results = users.filter((user)=>user.name.toLowerCase().includes(searchTerm.toLowerCase()));
        setSearchResults(results);
    }, [
        searchTerm,
        users
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
        className: "dashboard-wrapper",
        style: {
            background: currentThemeStyles.background,
            color: currentThemeStyles.color
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("nav", {
                className: `navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "container-fluid",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                            className: "navbar-brand fw-bold sleek-brand",
                            style: {
                                textDecoration: "none"
                            },
                            href: "/dashboard/profile",
                            children: brandText
                        }, void 0, false, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 218,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                            className: `navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`,
                            type: "button",
                            onClick: ()=>setIsSidebarOpen(!isSidebarOpen),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                className: "navbar-toggler-icon"
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 224,
                                columnNumber: 25
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 219,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/pages/Components/DashboardLayout.js",
                    lineNumber: 217,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Components/DashboardLayout.js",
                lineNumber: 216,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("aside", {
                className: `sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "sidebar-header sleek-header",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                            className: "p-3 fw-bold text-uppercase d-none d-lg-block",
                            children: brandText
                        }, void 0, false, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 232,
                            columnNumber: 21
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 231,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("ul", {
                        className: "nav flex-column p-3 sleek-nav",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-house-fill me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 238,
                                            columnNumber: 29
                                        }, this),
                                        "Home"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 237,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 236,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                    className: "nav-link sleek-nav-link w-100",
                                    "data-bs-toggle": "offcanvas",
                                    "data-bs-target": "#Search",
                                    style: {
                                        textDecoration: "none"
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-search me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 243,
                                            columnNumber: 29
                                        }, this),
                                        "Search"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 242,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 241,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Chats",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-box me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 248,
                                            columnNumber: 29
                                        }, this),
                                        "Chat with Random"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 247,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 246,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Chats",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-plus-square me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 253,
                                            columnNumber: 29
                                        }, this),
                                        "Create"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 252,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 251,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Messages",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-chat-fill me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 258,
                                            columnNumber: 29
                                        }, this),
                                        "Messages"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 257,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 256,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Chats",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-cpu me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 263,
                                            columnNumber: 29
                                        }, this),
                                        "Chat with NexTalk"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 262,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 261,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item ot-but",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Settings",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-gear-wide-connected me-2"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 268,
                                            columnNumber: 29
                                        }, this),
                                        "Settings"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 267,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 266,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    className: "nav-link sleek-nav-link justify-content-between",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    style: {
                                        textDecoration: "none"
                                    },
                                    href: "/Dashboard/Notification",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                    className: "bi bi-heart me-2"
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                    lineNumber: 274,
                                                    columnNumber: 33
                                                }, this),
                                                "Notification"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 273,
                                            columnNumber: 29
                                        }, this),
                                        pendingCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                            style: {
                                                backgroundColor: "#008080",
                                                color: "white",
                                                fontSize: "0.7rem",
                                                padding: "6px 12px",
                                                borderRadius: "50%",
                                                top: "10px",
                                                right: "10px"
                                            },
                                            children: pendingCount
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 277,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 272,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 271,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("br", {}, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 292,
                                columnNumber: 26
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("li", {
                                className: "nav-item",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: "",
                                    className: "btn btn-primary w-100 p-2 d-lg-none",
                                    style: {
                                        textDecoration: "none"
                                    },
                                    type: "button",
                                    onClick: ()=>setIsSidebarOpen(false),
                                    children: "Close "
                                }, void 0, false, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 294,
                                    columnNumber: 25
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 293,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 235,
                        columnNumber: 17
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "sidebar-footer p-3 sleek-footer",
                        children: [
                            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/Dashboard/Profile",
                                onClick: ()=>setIsSidebarOpen(false),
                                style: {
                                    background: "darkgray",
                                    textDecoration: "none"
                                },
                                className: "user-profile sleek-profile nav-link d-flex align-items-center gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "skeleton",
                                        style: {
                                            width: "45px",
                                            height: "45px",
                                            borderRadius: "50%"
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 305,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "skeleton",
                                                style: {
                                                    width: "120px",
                                                    height: "16px",
                                                    borderRadius: "4px",
                                                    marginBottom: "8px"
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 314,
                                                columnNumber: 33
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                className: "sleek-status online",
                                                children: "Online"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 323,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 313,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 304,
                                columnNumber: 25
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$link$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/Dashboard/Profile",
                                onClick: ()=>setIsSidebarOpen(false),
                                style: {
                                    textDecoration: "none"
                                },
                                className: "user-profile sleek-profile nav-link d-flex align-items-center gap-3",
                                children: [
                                    profile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                        src: profile.image || "/Images/predefine.webp",
                                        width: 45,
                                        height: 45,
                                        alt: "User",
                                        className: "rounded-circle sleek-avatar"
                                    }, profile.image, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 330,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                className: "d-block fw-semibold sleek-username",
                                                children: profile.name || "Guest"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 340,
                                                columnNumber: 37
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                className: "sleek-status online",
                                                children: "Online"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 341,
                                                columnNumber: 37
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 339,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 328,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "mt-4 sleek-actions",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        onClick: toggleTheme,
                                        className: "btn sleek-btn theme-toggle w-100 mb-2",
                                        children: theme === 'dark' ? 'Light Mode' : 'Dark Mode'
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 347,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        onClick: handleLogout,
                                        className: "btn sleek-btn logout-btn w-100",
                                        children: "Log Out"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 353,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 346,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 302,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Components/DashboardLayout.js",
                lineNumber: 230,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("main", {
                className: "main-content sleek-main",
                style: {
                    background: currentThemeStyles.chatBackground
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "container-fluid p-1 sleek-container",
                        children: [
                            children,
                            " "
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 365,
                        columnNumber: 17
                    }, this),
                    showConfirm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "modal-overlay",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "modal-content",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                                    className: "modal-title",
                                    children: "Log Out?"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 371,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                    className: "modal-text",
                                    children: "Are you sure you want to log out? You will be signed out of your account and redirected to the login page."
                                }, void 0, false, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 372,
                                    columnNumber: 29
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "modal-actions",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            className: "modal-btn modal-btn-success",
                                            onClick: handleConfirmUpload,
                                            children: "✅ Confirm !"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 377,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            className: "modal-btn modal-btn-cancel",
                                            onClick: ()=>setShowConfirm(false),
                                            children: "❌ Cancel"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                            lineNumber: 380,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                    lineNumber: 376,
                                    columnNumber: 29
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/pages/Components/DashboardLayout.js",
                            lineNumber: 370,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 369,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "offcanvas offcanvas-start",
                        style: {
                            background: currentThemeStyles.cardBg,
                            color: currentThemeStyles.color
                        },
                        id: "Search",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "offcanvas-header",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                        className: "offcanvas-title",
                                        children: "Search"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 389,
                                        columnNumber: 25
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        className: "btn-close bg-danger",
                                        "data-bs-dismiss": "offcanvas"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 390,
                                        columnNumber: 25
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 388,
                                columnNumber: 21
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "offcanvas-body",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                        type: "search",
                                        name: "search",
                                        id: "search",
                                        className: "form-control mb-3",
                                        placeholder: "Search users...",
                                        value: searchTerm,
                                        style: {
                                            backgroundColor: "white",
                                            transition: "background 0.3s",
                                            gap: "10px",
                                            border: "1px solid #333"
                                        },
                                        onChange: (e)=>setSearchTerm(e.target.value),
                                        onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = "white",
                                        onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = "whitesmock"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 393,
                                        columnNumber: 25
                                    }, this),
                                    loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "d-flex gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "skeleton",
                                                style: {
                                                    width: "45px",
                                                    height: "45px",
                                                    borderRadius: "50%"
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 413,
                                                columnNumber: 37
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "skeleton",
                                                    style: {
                                                        width: "120px",
                                                        height: "16px",
                                                        borderRadius: "4px",
                                                        marginBottom: "8px"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                    lineNumber: 422,
                                                    columnNumber: 41
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 421,
                                                columnNumber: 37
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 412,
                                        columnNumber: 33
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: [
                                            searchResults.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "d-flex gap-4 align-items-center user-result mb-2 p-2 rounded",
                                                    style: {
                                                        cursor: "pointer"
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                            src: user.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                            alt: user.name,
                                                            width: 60,
                                                            height: 60,
                                                            className: "rounded-circle",
                                                            style: {
                                                                objectFit: "cover"
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                                            lineNumber: 443,
                                                            columnNumber: 45
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("strong", {
                                                                    children: user.username
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                                    lineNumber: 452,
                                                                    columnNumber: 49
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                                    lineNumber: 452,
                                                                    columnNumber: 81
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                    children: user.name
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                                    lineNumber: 453,
                                                                    columnNumber: 49
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                                            lineNumber: 451,
                                                            columnNumber: 45
                                                        }, this)
                                                    ]
                                                }, user._id, true, {
                                                    fileName: "[project]/pages/Components/DashboardLayout.js",
                                                    lineNumber: 436,
                                                    columnNumber: 41
                                                }, this)),
                                            searchResults.length === 0 && searchTerm && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "text-center mt-4 fade-in",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("svg", {
                                                        xmlns: "http://www.w3.org/2000/svg",
                                                        width: "64",
                                                        height: "64",
                                                        fill: "gray",
                                                        viewBox: "0 0 24 24",
                                                        style: {
                                                            opacity: 0.5
                                                        },
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                                            d: "M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Components/DashboardLayout.js",
                                                            lineNumber: 469,
                                                            columnNumber: 49
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                                        lineNumber: 461,
                                                        columnNumber: 45
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                        className: "mt-2",
                                                        children: "No users found"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                                        lineNumber: 471,
                                                        columnNumber: 45
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                                lineNumber: 460,
                                                columnNumber: 41
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Components/DashboardLayout.js",
                                        lineNumber: 434,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Components/DashboardLayout.js",
                                lineNumber: 392,
                                columnNumber: 21
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Components/DashboardLayout.js",
                        lineNumber: 387,
                        columnNumber: 17
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Components/DashboardLayout.js",
                lineNumber: 364,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/pages/Components/DashboardLayout.js",
        lineNumber: 214,
        columnNumber: 9
    }, this);
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/socket.io-client [external] (socket.io-client, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("socket.io-client");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/utils/socket.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$socket$2e$io$2d$client__$5b$external$5d$__$28$socket$2e$io$2d$client$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/socket.io-client [external] (socket.io-client, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$socket$2e$io$2d$client__$5b$external$5d$__$28$socket$2e$io$2d$client$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$socket$2e$io$2d$client__$5b$external$5d$__$28$socket$2e$io$2d$client$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
class SocketService {
    constructor(){
        this.socket = null;
        this.isConnected = false;
        this.userId = null;
        this.messageCallbacks = [];
        this.onlineStatusCallbacks = [];
        this.typingCallbacks = [];
    }
    connect(userId) {
        if (this.socket && this.isConnected) {
            return;
        }
        this.userId = userId;
        // Connect to the server - Smart URL detection
        let serverUrl;
        // Check if we're on localhost
        if ("TURBOPACK compile-time falsy", 0) {
            serverUrl = 'http://localhost:5000';
        } else if (process.env.NEXT_PUBLIC_SERVER_URL) {
            serverUrl = process.env.NEXT_PUBLIC_SERVER_URL;
        } else {
            // Default to production server
            serverUrl = 'https://nextalk-u0y1.onrender.com';
        }
        console.log('🔗 Connecting to server:', serverUrl);
        console.log('🌍 Environment:', ("TURBOPACK compile-time value", "development"));
        console.log('🏠 Hostname:', ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : 'server');
        try {
            this.socket = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$socket$2e$io$2d$client__$5b$external$5d$__$28$socket$2e$io$2d$client$2c$__esm_import$29$__["io"])(serverUrl, {
                transports: [
                    'polling'
                ],
                withCredentials: true,
                forceNew: false,
                timeout: 20000,
                autoConnect: true,
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });
        } catch (error) {
            console.error('❌ Socket connection error:', error);
            // Fallback to basic connection
            this.socket = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$socket$2e$io$2d$client__$5b$external$5d$__$28$socket$2e$io$2d$client$2c$__esm_import$29$__["io"])(serverUrl);
        }
        this.socket.on('connect', ()=>{
            console.log('✅ Connected to server with socket ID:', this.socket.id);
            this.isConnected = true;
            // Join with user ID
            if (this.userId) {
                console.log('🔗 Joining with user ID:', this.userId);
                this.socket.emit('join', this.userId);
            }
        });
        this.socket.on('connect_error', (error)=>{
            console.error('❌ Connection error:', error);
            console.error('🚨 Failed to connect to:', serverUrl);
            // Check if it's a localhost connection issue
            if (serverUrl.includes('localhost')) {
                console.error('🔧 Make sure your backend server is running on http://localhost:5000');
                console.error('💡 Try running: npm start or node server.js in your backend directory');
            }
            // Try to reconnect with different transport
            if (error.message.includes('websocket')) {
                console.log('🔄 Retrying with polling transport...');
                this.socket.io.opts.transports = [
                    'polling'
                ];
            }
        });
        this.socket.on('reconnect', (attemptNumber)=>{
            console.log('🔄 Reconnected after', attemptNumber, 'attempts');
        });
        this.socket.on('reconnect_error', (error)=>{
            console.error('❌ Reconnection error:', error);
        });
        this.socket.on('disconnect', ()=>{
            console.log('Disconnected from server');
            this.isConnected = false;
        });
        // Handle incoming messages
        this.socket.on('receiveMessage', (data)=>{
            console.log('📨 Socket received message:', data);
            this.messageCallbacks.forEach((callback)=>{
                console.log('🔄 Calling message callback');
                callback(data);
            });
        });
        // Handle message delivery confirmation
        this.socket.on('messageDelivered', (data)=>{
            console.log('✅ Message delivered confirmation:', data);
        });
        // Handle message errors
        this.socket.on('messageError', (data)=>{
            console.error('Message error:', data);
        });
        // Handle online users list
        this.socket.on('onlineUsers', (userIds)=>{
            this.onlineStatusCallbacks.forEach((callback)=>callback({
                    type: 'initial',
                    userIds
                }));
        });
        // Handle user coming online
        this.socket.on('userOnline', (userId)=>{
            this.onlineStatusCallbacks.forEach((callback)=>callback({
                    type: 'online',
                    userId
                }));
        });
        // Handle user going offline
        this.socket.on('userOffline', (userId)=>{
            this.onlineStatusCallbacks.forEach((callback)=>callback({
                    type: 'offline',
                    userId
                }));
        });
        // Handle typing indicators
        this.socket.on('userTyping', (data)=>{
            this.typingCallbacks.forEach((callback)=>callback(data));
        });
        // Handle message read status
        this.socket.on('messageRead', (data)=>{
            console.log('Message read:', data);
        });
    }
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            this.userId = null;
        }
    }
    sendMessage(receiverId, message, messageType = 'text') {
        if (!this.socket || !this.isConnected) {
            console.error('❌ Socket not connected');
            return false;
        }
        if (!this.userId || !receiverId || !message) {
            console.error('❌ Missing required data:', {
                userId: this.userId,
                receiverId,
                message
            });
            return false;
        }
        const messageData = {
            senderId: this.userId,
            receiverId,
            message,
            messageType
        };
        console.log('📤 Sending message:', messageData);
        this.socket.emit('sendMessage', messageData);
        return true;
    }
    sendTypingIndicator(receiverId, isTyping) {
        if (!this.socket || !this.isConnected) {
            return;
        }
        this.socket.emit('typing', {
            senderId: this.userId,
            receiverId,
            isTyping
        });
    }
    markAsRead(chatId, messageId) {
        if (!this.socket || !this.isConnected) {
            return;
        }
        this.socket.emit('markAsRead', {
            chatId,
            messageId,
            userId: this.userId
        });
    }
    // Callback management
    onMessage(callback) {
        this.messageCallbacks.push(callback);
        // Return unsubscribe function
        return ()=>{
            this.messageCallbacks = this.messageCallbacks.filter((cb)=>cb !== callback);
        };
    }
    onOnlineStatus(callback) {
        this.onlineStatusCallbacks.push(callback);
        return ()=>{
            this.onlineStatusCallbacks = this.onlineStatusCallbacks.filter((cb)=>cb !== callback);
        };
    }
    onTyping(callback) {
        this.typingCallbacks.push(callback);
        return ()=>{
            this.typingCallbacks = this.typingCallbacks.filter((cb)=>cb !== callback);
        };
    }
    // Get connection status
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            userId: this.userId,
            socketId: this.socket?.id
        };
    }
}
// Create singleton instance
const socketService = new SocketService();
const __TURBOPACK__default__export__ = socketService;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/pages/Dashboard/Messages.js [ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>Messages)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react/jsx-dev-runtime [external] (react/jsx-dev-runtime, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/react [external] (react, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/axiosConfig.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_context__.i('[project]/public/Images/predefine.webp.mjs { IMAGE => "[project]/public/Images/predefine.webp (static in ecmascript)" } [ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/pages/Components/DashboardLayout.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/router.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/context/ThemeContext.js [ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/socket.js [ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__,
    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
;
;
;
;
;
;
;
;
function Messages() {
    const [users, setUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [sessionUser, setSessionUser] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [following, setFollowing] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [accepted, setAccepted] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])('');
    const [selectedChat, setSelectedChat] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])('');
    const [displayCount, setDisplayCount] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(6);
    const [visibleUsers, setVisibleUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [filteredFollowers, setFilteredFollowers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([]);
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({
        username: 'user123',
        name: 'John Doe',
        email: '<EMAIL>',
        bio: 'No bio yet.',
        avatar: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
        posts: 15,
        followersCount: 250,
        followingCount: 180
    });
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$context$2f$ThemeContext$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    const [showOnlineOnly, setShowOnlineOnly] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$router$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // Mock last message data and reactions
    const [lastMessages, setLastMessages] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [reactions, setReactions] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])([
        {
            id: 1,
            sender: "You",
            text: "Hey, how's it going?",
            timestamp: "10:30 AM"
        },
        {
            id: 2,
            sender: "Friend",
            text: "Pretty good, thanks! You?",
            timestamp: "10:32 AM"
        },
        {
            id: 3,
            sender: "You",
            text: "Hey, how's it going?",
            timestamp: "10:42 AM"
        },
        {
            id: 4,
            sender: "Friend",
            text: "Pretty good, thanks! You?",
            timestamp: "10:52 AM"
        }
    ]);
    const [newMessage, setNewMessage] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])("");
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])(null);
    const [sessionUserId, setSessionUserId] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [selectedUser, setSelectedUser] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(null);
    const [chatMessages, setChatMessages] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [onlineUsers, setOnlineUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [typingUsers, setTypingUsers] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [isTyping, setIsTyping] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const [unreadCounts, setUnreadCounts] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])({});
    const [deliveredMessages, setDeliveredMessages] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(new Set());
    const [showMobileModal, setShowMobileModal] = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useState"])(false);
    const typingTimeoutRef = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useRef"])(null);
    // Enhanced Local Storage utility functions
    const getChatHistory = (userId)=>{
        try {
            if (!sessionUserId || !userId) return [];
            const chatKey = `chat_${sessionUserId}_${userId}`;
            const stored = localStorage.getItem(chatKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading chat history:', error);
            return [];
        }
    };
    const saveChatHistory = (userId, messages)=>{
        try {
            if (!sessionUserId || !userId) return;
            const chatKey = `chat_${sessionUserId}_${userId}`;
            localStorage.setItem(chatKey, JSON.stringify(messages));
            console.log('💾 Chat history saved for user:', userId);
        } catch (error) {
            console.error('Error saving chat history:', error);
        }
    };
    const getUnreadCount = (userId)=>{
        try {
            if (!sessionUserId || !userId) return 0;
            const unreadKey = `unread_${sessionUserId}_${userId}`;
            const stored = localStorage.getItem(unreadKey);
            return stored ? parseInt(stored) : 0;
        } catch (error) {
            console.error('Error loading unread count:', error);
            return 0;
        }
    };
    const saveUnreadCount = (userId, count)=>{
        try {
            if (!sessionUserId || !userId) return;
            const unreadKey = `unread_${sessionUserId}_${userId}`;
            localStorage.setItem(unreadKey, count.toString());
            setUnreadCounts((prev)=>({
                    ...prev,
                    [userId]: count
                }));
        } catch (error) {
            console.error('Error saving unread count:', error);
        }
    };
    const getLastMessage = (userId)=>{
        try {
            if (!sessionUserId || !userId) return '';
            const lastMsgKey = `lastmsg_${sessionUserId}_${userId}`;
            return localStorage.getItem(lastMsgKey) || '';
        } catch (error) {
            console.error('Error loading last message:', error);
            return '';
        }
    };
    const saveLastMessage = (userId, message)=>{
        try {
            if (!sessionUserId || !userId) return;
            const lastMsgKey = `lastmsg_${sessionUserId}_${userId}`;
            const truncatedMessage = message.length > 30 ? message.substring(0, 30) + '...' : message;
            localStorage.setItem(lastMsgKey, truncatedMessage);
            setLastMessages((prev)=>({
                    ...prev,
                    [userId]: truncatedMessage
                }));
        } catch (error) {
            console.error('Error saving last message:', error);
        }
    };
    // Handle back to chat list on mobile
    const handleBackToList = ()=>{
        console.log('🔙 Going back to chat list');
        setSelectedChat(null);
        setSelectedUser(null);
        setShowMobileModal(false);
        // Update URL only on desktop
        if (window.innerWidth >= 1024) {
            router.replace('/Dashboard/Messages', undefined, {
                shallow: true
            });
        }
    };
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchData = async ()=>{
            const storedUser = JSON.parse(sessionStorage.getItem('user'));
            const sessionId = storedUser?.user?.id;
            if (!sessionId) {
                setError('No session found.');
                setLoading(false);
                return;
            }
            try {
                // Fetch all users
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].post('/displayusersProfile');
                const personally = response.data;
                const filteredUsers = personally.filter((user)=>user._id !== sessionId);
                const sessionUser = personally.find((user)=>user._id === sessionId);
                setSessionUser(sessionUser);
                // Fetch follow status
                const followRes = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].get(`/follow-status/${sessionId}`);
                const followData = followRes.data;
                setFollowing(new Set(followData.following));
                setAccepted(new Set(followData.accepted));
                // Mock last messages and reactions
                const mockLastMessages = filteredUsers.reduce((acc, user)=>({
                        ...acc,
                        [user._id]: user.lastMessage || `Hey, what's up? ${new Date().toLocaleTimeString()}`
                    }), {});
                const mockReactions = filteredUsers.reduce((acc, user)=>({
                        ...acc,
                        [user._id]: user.reaction || (Math.random() > 0.5 ? '❤️' : null)
                    }), {});
                setLastMessages(mockLastMessages);
                setReactions(mockReactions);
                setUsers(filteredUsers);
                setLoading(false);
            } catch (err) {
                console.error('❌ Error fetching data:', err);
                setError('Failed to load data.');
                setLoading(false);
            }
        };
        fetchData();
    }, []);
    // Load chat history and unread counts when users are loaded
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (users.length > 0 && sessionUserId) {
            console.log('📚 Loading chat history for all users...');
            const loadedChats = {};
            const loadedUnreadCounts = {};
            const loadedLastMessages = {};
            users.forEach((user)=>{
                if (accepted.has(user._id)) {
                    // Load chat history
                    const history = getChatHistory(user._id);
                    if (history.length > 0) {
                        loadedChats[user._id] = history;
                        console.log(`📖 Loaded ${history.length} messages for user:`, user.name);
                    }
                    // Load unread count
                    const unreadCount = getUnreadCount(user._id);
                    if (unreadCount > 0) {
                        loadedUnreadCounts[user._id] = unreadCount;
                    }
                    // Load last message
                    const lastMsg = getLastMessage(user._id);
                    if (lastMsg) {
                        loadedLastMessages[user._id] = lastMsg;
                    }
                }
            });
            setChatMessages(loadedChats);
            setUnreadCounts(loadedUnreadCounts);
            setLastMessages(loadedLastMessages);
            console.log('✅ All chat data loaded from cache');
        }
    }, [
        users,
        sessionUserId,
        accepted
    ]);
    // Socket.IO connection and real-time functionality
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (sessionUserId) {
            // Connect to Socket.IO
            __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].connect(sessionUserId);
            // Listen for incoming messages
            const unsubscribeMessages = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].onMessage((data)=>{
                console.log('📨 Received message:', data);
                console.log('🔍 Current sessionUserId:', sessionUserId);
                console.log('🔍 Current selectedChat:', selectedChat);
                const { senderId, receiverId, message, timestamp, messageId } = data;
                // Add message to chat if it's for current user or current chat
                if (receiverId === sessionUserId || senderId === sessionUserId) {
                    console.log('✅ Message is for current user, adding to chat');
                    const chatPartnerId = receiverId === sessionUserId ? senderId : receiverId;
                    const isIncomingMessage = receiverId === sessionUserId;
                    const newMessage = {
                        id: messageId,
                        sender: senderId === sessionUserId ? "You" : "Friend",
                        text: message,
                        timestamp: new Date(timestamp).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        }),
                        delivered: true,
                        read: false
                    };
                    // Update chat messages
                    setChatMessages((prev)=>{
                        const updatedMessages = [
                            ...prev[chatPartnerId] || [],
                            newMessage
                        ];
                        // Save to local storage
                        saveChatHistory(chatPartnerId, updatedMessages);
                        return {
                            ...prev,
                            [chatPartnerId]: updatedMessages
                        };
                    });
                    // Update last message
                    saveLastMessage(chatPartnerId, message);
                    // Handle unread count for incoming messages
                    if (isIncomingMessage) {
                        const currentUnread = getUnreadCount(chatPartnerId);
                        const newUnreadCount = currentUnread + 1;
                        saveUnreadCount(chatPartnerId, newUnreadCount);
                        console.log(`📬 Unread count for ${chatPartnerId}: ${newUnreadCount}`);
                    }
                }
            });
            // Listen for online status updates
            const unsubscribeOnlineStatus = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].onOnlineStatus((data)=>{
                console.log('👥 Online status update:', data);
                if (data.type === 'initial') {
                    console.log('📋 Setting initial online users:', data.userIds);
                    setOnlineUsers(new Set(data.userIds));
                } else if (data.type === 'online') {
                    console.log('🟢 User came online:', data.userId);
                    setOnlineUsers((prev)=>new Set([
                            ...prev,
                            data.userId
                        ]));
                } else if (data.type === 'offline') {
                    console.log('🔴 User went offline:', data.userId);
                    setOnlineUsers((prev)=>{
                        const newSet = new Set(prev);
                        newSet.delete(data.userId);
                        return newSet;
                    });
                }
            });
            // Listen for typing indicators
            const unsubscribeTyping = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].onTyping((data)=>{
                console.log('⌨️ Typing indicator:', data);
                const { userId, isTyping } = data;
                setTypingUsers((prev)=>{
                    const newSet = new Set(prev);
                    if (isTyping) {
                        console.log('⌨️ User started typing:', userId);
                        newSet.add(userId);
                    } else {
                        console.log('⌨️ User stopped typing:', userId);
                        newSet.delete(userId);
                    }
                    return newSet;
                });
                // Clear typing indicator after 3 seconds
                if (isTyping) {
                    setTimeout(()=>{
                        setTypingUsers((prev)=>{
                            const newSet = new Set(prev);
                            newSet.delete(userId);
                            return newSet;
                        });
                    }, 3000);
                }
            });
            // Cleanup on unmount
            return ()=>{
                unsubscribeMessages();
                unsubscribeOnlineStatus();
                unsubscribeTyping();
                __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].disconnect();
            };
        }
    }, [
        sessionUserId
    ]);
    // Handle direct URL access
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (router.isReady && users.length > 0 && !selectedChat && sessionUserId) {
            const { userId } = router.query;
            if (userId && userId !== 'undefined' && userId !== 'null') {
                const userToSelect = users.find((u)=>u._id === userId);
                if (userToSelect && accepted.has(userToSelect._id)) {
                    setSelectedChat(userToSelect._id);
                    setSelectedUser(userToSelect);
                    // Load chat history for this user
                    const history = getChatHistory(userToSelect._id);
                    setChatMessages((prev)=>({
                            ...prev,
                            [userToSelect._id]: history
                        }));
                }
            }
        }
    }, [
        router.isReady,
        users,
        accepted,
        selectedChat,
        sessionUserId
    ]);
    // Auto-scroll to bottom when new messages are added
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({
                behavior: 'smooth'
            });
        }
    }, [
        chatMessages,
        selectedChat
    ]);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        const fetchUsers = async ()=>{
            const storedUser = JSON.parse(sessionStorage.getItem("user"));
            const sessionId = storedUser?.user?.id;
            setSessionUserId(sessionId);
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$axiosConfig$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].post("/displayusersProfile");
                const allUsers = response.data.filter((user)=>user._id !== sessionId);
                setUsers(allUsers);
                setVisibleUsers(allUsers.slice(0, 6)); // first 6
                setTimeout(()=>setLoading(false), 1000); // optional fake delay
            } catch (error) {
                console.error("Error fetching users:", error);
            }
        };
        fetchUsers();
    }, []);
    // Filter users based on search query and online status
    const filteredUsers = users.filter((user)=>accepted.has(user._id)).filter((user)=>user.name.toLowerCase().includes(searchQuery.toLowerCase())).filter((user)=>showOnlineOnly ? user.isOnline : true);
    const getThemeStyles = ()=>{
        if (theme === 'dark') {
            return {
                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
                color: '#e2e8f0',
                cardBg: 'rgba(255, 255, 255, 0.1)',
                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',
                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',
                notificationBg: 'rgba(51, 65, 85, 0.9)'
            };
        }
        return {
            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',
            color: '#1e293b',
            cardBg: 'rgba(255, 255, 255, 0.8)',
            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)'
        };
    };
    const styles = getThemeStyles();
    const getThemeStyless = ()=>{
        if (theme === 'dark') {
            return {
                background: '#1e293b',
                color: '#e2e8f0',
                inputBg: '#334155',
                inputColor: '#e2e8f0',
                messageBgYou: '#3b82f6',
                messageBgFriend: '#4b5563'
            };
        }
        // Default to light styles for 'homeback' or any other theme
        return {
            background: '#ffffff',
            color: '#1e293b',
            inputBg: '#f1f5f9',
            inputColor: '#1e293b',
            messageBgYou: '#3b82f6',
            messageBgFriend: '#e5e7eb'
        };
    };
    const currentThemeStyles = getThemeStyless();
    const handleSendMessage = (e)=>{
        e.preventDefault();
        console.log('🚀 Attempting to send message:', {
            newMessage: newMessage.trim(),
            selectedChat,
            sessionUserId,
            socketConnected: __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].getConnectionStatus()
        });
        if (newMessage.trim() && selectedChat && sessionUserId) {
            // Send message via Socket.IO for real-time delivery
            console.log('📤 Sending message to:', selectedChat, 'from:', sessionUserId);
            console.log('📤 Message content:', newMessage.trim());
            const success = __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].sendMessage(selectedChat, newMessage.trim());
            console.log('📤 Socket send result:', success);
            console.log('📤 Socket connection status:', __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].getConnectionStatus());
            if (success) {
                // Add message to local state immediately for instant UI update
                const newMsg = {
                    id: Date.now(),
                    sender: "You",
                    text: newMessage.trim(),
                    timestamp: new Date().toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    }),
                    delivered: onlineUsers.has(selectedChat),
                    read: false
                };
                const currentMessages = chatMessages[selectedChat] || [];
                const updatedMessages = [
                    ...currentMessages,
                    newMsg
                ];
                // Update chat messages state and save to local storage
                setChatMessages((prev)=>{
                    const updated = {
                        ...prev,
                        [selectedChat]: updatedMessages
                    };
                    // Save to local storage
                    saveChatHistory(selectedChat, updatedMessages);
                    return updated;
                });
                // Update last message
                saveLastMessage(selectedChat, newMsg.text);
                // Clear input
                setNewMessage("");
                // Stop typing indicator
                if (isTyping) {
                    setIsTyping(false);
                    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].sendTypingIndicator(selectedChat, false);
                }
            } else {
                console.error('Failed to send message - Socket not connected');
            }
        }
    };
    // Handle typing indicator
    const handleTyping = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useCallback"])((value)=>{
        setNewMessage(value);
        if (selectedChat && sessionUserId) {
            if (value.trim() && !isTyping) {
                setIsTyping(true);
                __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].sendTypingIndicator(selectedChat, true);
            } else if (!value.trim() && isTyping) {
                setIsTyping(false);
                __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].sendTypingIndicator(selectedChat, false);
            }
            // Clear typing timeout
            if (typingTimeoutRef.current) {
                clearTimeout(typingTimeoutRef.current);
            }
            // Set new timeout to stop typing indicator
            typingTimeoutRef.current = setTimeout(()=>{
                if (isTyping) {
                    setIsTyping(false);
                    __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$socket$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"].sendTypingIndicator(selectedChat, false);
                }
            }, 2000);
        }
    }, [
        selectedChat,
        sessionUserId,
        isTyping
    ]);
    (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react__$5b$external$5d$__$28$react$2c$__cjs$29$__["useEffect"])(()=>{
        if (!profile || !Array.isArray(profile.followers)) return;
        // Extract follower user IDs from the populated followers array
        const followersArray = profile.followers.map((f)=>f._id?.toString());
        // Filter only users who are followers
        const followedUsers = users.filter((user)=>followersArray.includes(user._id?.toString()));
        // Filter for search
        const filtered = followedUsers.filter((user)=>user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()));
        setFilteredFollowers(filtered); // useful for Load More check
        // Control visible users based on search or default count
        if (searchTerm.trim() === '') {
            setVisibleUsers(followedUsers.slice(0, displayCount));
        } else {
            setVisibleUsers(filtered.slice(0, displayCount));
        }
    }, [
        searchTerm,
        users,
        displayCount,
        profile
    ]);
    const handleLoadMore = ()=>{
        const prevCount = displayCount;
        const newCount = prevCount + 6;
        setDisplayCount(newCount);
        // Scroll to the previous 6th user (after DOM update)
        setTimeout(()=>{
            const userElems = document.querySelectorAll(".user-result");
            if (userElems[prevCount]) {
                userElems[prevCount].scrollIntoView({
                    behavior: "smooth",
                    block: "start"
                });
            }
        }, 100); // wait a moment for new DOM elements to render
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$pages$2f$Components$2f$DashboardLayout$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
        children: [
            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "custom-loader-overlay",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("svg", {
                    viewBox: "0 0 100 100",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("g", {
                        fill: "none",
                        stroke: "#fff",
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: "6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 21 40 V 59",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animateTransform", {
                                    attributeName: "transform",
                                    type: "rotate",
                                    values: "0 21 59; 180 21 59",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 599,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 598,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 79 40 V 59",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animateTransform", {
                                    attributeName: "transform",
                                    type: "rotate",
                                    values: "0 79 59; -180 79 59",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 603,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 602,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 21 V 40",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "d",
                                    values: "M 50 21 V 40; M 50 59 V 40",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 607,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 606,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 60 V 79",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "d",
                                    values: "M 50 60 V 79; M 50 98 V 79",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 611,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 610,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 21 L 79 40 L 50 60 L 21 40 Z",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "stroke",
                                    values: "rgba(255,255,255,1); rgba(100,100,100,0)",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 615,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 614,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 40 L 79 59 L 50 79 L 21 59 Z"
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 618,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("path", {
                                d: "M 50 59 L 79 78 L 50 98 L 21 78 Z",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animate", {
                                    attributeName: "stroke",
                                    values: "rgba(100,100,100,0); rgba(255,255,255,1)",
                                    dur: "2s",
                                    repeatCount: "indefinite"
                                }, void 0, false, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 621,
                                    columnNumber: 33
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 620,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("animateTransform", {
                                attributeName: "transform",
                                type: "translate",
                                values: "0 0; 0 -19",
                                dur: "2s",
                                repeatCount: "indefinite"
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 623,
                                columnNumber: 29
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 596,
                        columnNumber: 25
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/pages/Dashboard/Messages.js",
                    lineNumber: 595,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 594,
                columnNumber: 17
            }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "error",
                children: error
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 628,
                columnNumber: 17
            }, this) : filteredUsers.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "chat-container",
                style: {
                    background: styles.background,
                    color: styles.color
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "chat-list",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h2", {
                                className: "chat-title d-none d-md-block",
                                children: sessionUser?.name || sessionUser?.username || 'User'
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 634,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "mb-3 px-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "position-relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                            type: "text",
                                            className: "form-control ps-5",
                                            placeholder: "Search messages...",
                                            value: searchTerm,
                                            onChange: (e)=>setSearchTerm(e.target.value),
                                            style: {
                                                background: currentThemeStyles.inputBg,
                                                color: currentThemeStyles.inputColor,
                                                border: '1px solid rgba(0,0,0,0.1)',
                                                borderRadius: '25px',
                                                padding: '12px 20px 12px 45px'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 641,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                            className: "bi bi-search position-absolute",
                                            style: {
                                                left: '15px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                color: currentThemeStyles.inputColor,
                                                opacity: 0.6
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 655,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 640,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 639,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "text-center py-5",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                        className: "bi bi-chat-dots",
                                        style: {
                                            fontSize: '3rem',
                                            opacity: 0.5
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 670,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                                        className: "mt-3",
                                        children: "No friends to message"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 671,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                        className: "text-muted",
                                        children: "Add friends to start chatting!"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 672,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 669,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 632,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "chat-panel d-none d-lg-flex",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "instructions text-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "instruction-content",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                        className: "bi bi-chat-heart",
                                        style: {
                                            fontSize: '4rem',
                                            opacity: 0.3,
                                            marginBottom: '20px'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 680,
                                        columnNumber: 33
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                        className: "instruction-title",
                                        children: "Your messages"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 681,
                                        columnNumber: 33
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                        className: "instruction-text",
                                        children: "Send a message to start a chat."
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 682,
                                        columnNumber: 33
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        className: "btn btn-primary",
                                        onClick: ()=>{
                                            // You can add functionality to open friend list or search
                                            console.log('Add friends button clicked');
                                        },
                                        style: {
                                            borderRadius: '25px',
                                            padding: '10px 30px',
                                            fontWeight: '500'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                className: "bi bi-person-plus me-2"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 695,
                                                columnNumber: 37
                                            }, this),
                                            "Add Friends"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 683,
                                        columnNumber: 33
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 679,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/pages/Dashboard/Messages.js",
                            lineNumber: 678,
                            columnNumber: 25
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 677,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 630,
                columnNumber: 17
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "chat-container",
                style: {
                    background: styles.background,
                    color: styles.color
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: `chat-list ${selectedChat ? 'chat-list-with-panel' : ''}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h2", {
                                className: "chat-title d-none d-md-block",
                                children: sessionUser?.name || sessionUser?.username || 'User'
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 707,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "mb-3 px-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "position-relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                            type: "text",
                                            placeholder: "Search messages...",
                                            value: searchQuery,
                                            onChange: (e)=>setSearchQuery(e.target.value),
                                            className: "form-control",
                                            style: {
                                                borderRadius: '25px',
                                                paddingLeft: '20px',
                                                paddingRight: '50px'
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 714,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                            className: "position-absolute",
                                            style: {
                                                right: '15px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                color: '#6c757d'
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                className: "bi bi-search"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 732,
                                                columnNumber: 37
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 726,
                                            columnNumber: 33
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 713,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 712,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                className: "fw-bold mb-2 d-block px-3",
                                children: "Messages"
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 738,
                                columnNumber: 25
                            }, this),
                            filteredUsers.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "chat-item",
                                    onClick: ()=>{
                                        setSelectedChat(user._id);
                                        setSelectedUser(user);
                                        // Mark messages as read and clear unread count
                                        saveUnreadCount(user._id, 0);
                                        // Check if mobile view
                                        const isMobile = window.innerWidth < 1024;
                                        if (isMobile) {
                                            // Open modal on mobile
                                            setShowMobileModal(true);
                                        } else {
                                            // Update URL on desktop
                                            router.replace(`/Dashboard/Messages?userId=${user._id}`, undefined, {
                                                shallow: true
                                            });
                                        }
                                        // Load chat history for this user
                                        const history = getChatHistory(user._id);
                                        if (history.length > 0) {
                                            setChatMessages((prev)=>({
                                                    ...prev,
                                                    [user._id]: history
                                                }));
                                            console.log(`📖 Loaded ${history.length} messages for chat with:`, user.name);
                                        }
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "avatar-container",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    src: user.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                    alt: user.name,
                                                    width: 80,
                                                    height: 80,
                                                    className: "avatar"
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 773,
                                                    columnNumber: 37
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                    className: `status-indicator ${onlineUsers.has(user._id) ? 'online' : 'offline'}`,
                                                    title: onlineUsers.has(user._id) ? 'Online' : 'Offline'
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 780,
                                                    columnNumber: 37
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 772,
                                            columnNumber: 33
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "chat-details",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "chat-header",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                            className: "user-name",
                                                            children: user.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 787,
                                                            columnNumber: 41
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                            className: "timestamp",
                                                            children: new Date().toLocaleTimeString([], {
                                                                hour: '2-digit',
                                                                minute: '2-digit'
                                                            })
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 788,
                                                            columnNumber: 41
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 786,
                                                    columnNumber: 37
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                    className: "last-message",
                                                    children: typingUsers.has(user._id) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                        className: "typing-text",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                className: "typing-indicator",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 796,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 797,
                                                                        columnNumber: 53
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 798,
                                                                        columnNumber: 53
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 795,
                                                                columnNumber: 49
                                                            }, this),
                                                            "typing..."
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 794,
                                                        columnNumber: 45
                                                    }, this) : lastMessages[user._id] || 'No messages yet'
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 792,
                                                    columnNumber: 37
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 785,
                                            columnNumber: 33
                                        }, this),
                                        unreadCounts[user._id] > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "unread-badge",
                                            children: unreadCounts[user._id] > 4 ? '4+' : unreadCounts[user._id]
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 810,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, user._id, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 740,
                                    columnNumber: 29
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 705,
                        columnNumber: 21
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "chat-panel d-none d-lg-flex",
                        children: selectedChat ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "chat-window",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "chat-header d-flex align-items-center justify-content-between w-100",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "d-flex align-items-center gap-3",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    src: filteredUsers.find((u)=>u._id === selectedChat)?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                    alt: filteredUsers.find((u)=>u._id === selectedChat)?.name,
                                                    width: 50,
                                                    height: 50,
                                                    className: "avatar",
                                                    style: {
                                                        borderRadius: '50%'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 825,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                                        className: "mb-0",
                                                        style: {
                                                            fontSize: '1.2rem'
                                                        },
                                                        children: filteredUsers.find((u)=>u._id === selectedChat)?.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 834,
                                                        columnNumber: 45
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 833,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 824,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            className: "btn d-md-none",
                                            onClick: ()=>{
                                                console.log('🔙 Back button clicked');
                                                setSelectedChat(null);
                                                setSelectedUser(null);
                                            },
                                            style: {
                                                fontSize: '1.5rem',
                                                color: getThemeStyles().color,
                                                background: 'none',
                                                border: 'none',
                                                padding: '8px',
                                                minWidth: '44px',
                                                height: '44px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center'
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                className: "bi bi-x-lg"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 862,
                                                columnNumber: 41
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 841,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 823,
                                    columnNumber: 33
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    className: "",
                                    style: {
                                        background: currentThemeStyles.background,
                                        color: currentThemeStyles.color
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "chat-messages",
                                            style: {
                                                height: 'calc(100vh - 300px)',
                                                overflowY: 'auto',
                                                padding: '20px',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                gap: '10px'
                                            },
                                            children: chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
                                                children: [
                                                    chatMessages[selectedChat].map((msg)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: `message ${msg.sender === "You" ? "message-you" : "message-friend"}`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                className: "message-bubble",
                                                                style: {
                                                                    background: msg.sender === "You" ? currentThemeStyles.messageBgYou || '#3b82f6' : 'rgba(255, 255, 255, 0.9)',
                                                                    color: msg.sender === "You" ? '#ffffff' : '#333'
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                        className: "message-text",
                                                                        children: msg.text
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 900,
                                                                        columnNumber: 61
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                        className: "message-footer",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                                className: "message-timestamp",
                                                                                children: msg.timestamp
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 902,
                                                                                columnNumber: 65
                                                                            }, this),
                                                                            msg.sender === "You" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                                className: "delivery-status",
                                                                                children: msg.delivered ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                                    children: "Seen"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                    lineNumber: 906,
                                                                                    columnNumber: 77
                                                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                                    children: "Sent"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                    lineNumber: 908,
                                                                                    columnNumber: 77
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 904,
                                                                                columnNumber: 69
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 901,
                                                                        columnNumber: 61
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 891,
                                                                columnNumber: 57
                                                            }, this)
                                                        }, msg.id, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 887,
                                                            columnNumber: 53
                                                        }, this)),
                                                    typingUsers.has(selectedChat) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "message message-friend",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "message-bubble",
                                                            style: {
                                                                background: 'rgba(255, 255, 255, 0.9)',
                                                                color: '#333'
                                                            },
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                className: "typing-indicator",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 928,
                                                                        columnNumber: 65
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 929,
                                                                        columnNumber: 65
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 930,
                                                                        columnNumber: 65
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 927,
                                                                columnNumber: 61
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 920,
                                                            columnNumber: 57
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 919,
                                                        columnNumber: 53
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        ref: messagesEndRef
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 937,
                                                        columnNumber: 49
                                                    }, this)
                                                ]
                                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "text-center p-4",
                                                style: {
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    height: '100%'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                        className: "bi bi-chat-dots",
                                                        style: {
                                                            fontSize: '3rem',
                                                            opacity: 0.5
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 947,
                                                        columnNumber: 49
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                                                        className: "mt-3",
                                                        children: "No messages yet"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 948,
                                                        columnNumber: 49
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                        className: "",
                                                        children: "Start the conversation by sending a message!"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 949,
                                                        columnNumber: 49
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 940,
                                                columnNumber: 45
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 873,
                                            columnNumber: 37
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("form", {
                                            className: "chat-input-form",
                                            onSubmit: handleSendMessage,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "input-group",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                                        type: "text",
                                                        className: "form-control",
                                                        placeholder: "Type a message...",
                                                        value: newMessage,
                                                        onChange: (e)=>handleTyping(e.target.value),
                                                        onKeyDown: (e)=>{
                                                            if (e.key === 'Enter' && !e.shiftKey) {
                                                                e.preventDefault();
                                                                handleSendMessage(e);
                                                            }
                                                        },
                                                        style: {
                                                            background: currentThemeStyles.inputBg,
                                                            color: currentThemeStyles.inputColor,
                                                            border: 'none',
                                                            borderRadius: '25px',
                                                            padding: '12px 20px'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 955,
                                                        columnNumber: 45
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                        type: "submit",
                                                        className: "chat-send-btn btn btn-primary",
                                                        onClick: handleSendMessage,
                                                        style: {
                                                            borderRadius: '50%',
                                                            width: '45px',
                                                            height: '45px',
                                                            marginLeft: '10px',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center'
                                                        },
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                            className: "bi bi-send-fill"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 989,
                                                            columnNumber: 49
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 975,
                                                        columnNumber: 45
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 954,
                                                columnNumber: 41
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 953,
                                            columnNumber: 37
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 865,
                                    columnNumber: 33
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/pages/Dashboard/Messages.js",
                            lineNumber: 821,
                            columnNumber: 29
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                            className: "instructions text-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "instruction-content",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                        className: "bi bi-chat-heart",
                                        style: {
                                            fontSize: '4rem',
                                            opacity: 0.3,
                                            marginBottom: '20px'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 998,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h3", {
                                        className: "instruction-title",
                                        children: "Your messages"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 999,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                        className: "instruction-text",
                                        children: "Send a message to start a chat."
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1000,
                                        columnNumber: 37
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        className: "btn btn-primary",
                                        "data-bs-toggle": "modal",
                                        "data-bs-target": "#followers",
                                        style: {
                                            borderRadius: '25px',
                                            padding: '10px 30px',
                                            fontWeight: '500'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                className: "bi bi-person-plus me-2"
                                            }, void 0, false, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 1011,
                                                columnNumber: 41
                                            }, this),
                                            "Send Message"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1001,
                                        columnNumber: 37
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 997,
                                columnNumber: 33
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/pages/Dashboard/Messages.js",
                            lineNumber: 996,
                            columnNumber: 29
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 819,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 703,
                columnNumber: 17
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: "modal",
                id: "followers",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "modal-dialog",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "modal-content",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "d-flex justify-content-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h5", {
                                            children: "Followers"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 1025,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1024,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            className: "btn-close bg-primary",
                                            "data-bs-dismiss": "modal"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 1028,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1027,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 1023,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("hr", {}, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 1030,
                                columnNumber: 31
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                            type: "search",
                                            name: "search",
                                            id: "search",
                                            className: "form-control mb-3",
                                            placeholder: "Search users...",
                                            value: searchTerm,
                                            style: {
                                                backgroundColor: "white",
                                                transition: "background 0.3s",
                                                gap: "10px",
                                                border: "1px solid #333"
                                            },
                                            onChange: (e)=>setSearchTerm(e.target.value),
                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = "white",
                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = "whitesmock"
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 1033,
                                            columnNumber: 33
                                        }, this),
                                        loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "d-flex gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "skeleton",
                                                    style: {
                                                        width: "45px",
                                                        height: "45px",
                                                        borderRadius: "50%"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1054,
                                                    columnNumber: 45
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "skeleton",
                                                        style: {
                                                            width: "120px",
                                                            height: "16px",
                                                            borderRadius: "4px",
                                                            marginBottom: "8px"
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1063,
                                                        columnNumber: 49
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1062,
                                                    columnNumber: 45
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 1053,
                                            columnNumber: 41
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
                                            children: [
                                                visibleUsers.length > 0 ? visibleUsers.map((user)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: "d-flex align-items-center mb-2 p-2 rounded user-result",
                                                        style: {
                                                            justifyContent: "space-between"
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                className: "d-flex gap-4 align-items-center",
                                                                style: {
                                                                    cursor: "pointer"
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                                        src: user.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                                        alt: user.name,
                                                                        width: 60,
                                                                        height: 60,
                                                                        className: "rounded-circle",
                                                                        style: {
                                                                            objectFit: "cover"
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 1085,
                                                                        columnNumber: 61
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("strong", {
                                                                                children: user.username
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 1094,
                                                                                columnNumber: 65
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("br", {}, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 1094,
                                                                                columnNumber: 97
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                                children: user.name
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                                lineNumber: 1095,
                                                                                columnNumber: 65
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                                        lineNumber: 1093,
                                                                        columnNumber: 61
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 1079,
                                                                columnNumber: 57
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                                    className: "btn btn-primary btn-sm",
                                                                    onClick: ()=>handleRemoveFollower(user._id),
                                                                    children: "Message"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 1099,
                                                                    columnNumber: 61
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 1098,
                                                                columnNumber: 57
                                                            }, this)
                                                        ]
                                                    }, user._id, true, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1078,
                                                        columnNumber: 53
                                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "no-followers-container text-center mt-5",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "icon-wrapper mb-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                                className: "bi bi-person-x",
                                                                style: {
                                                                    fontSize: "3rem",
                                                                    color: "#6c757d"
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                                lineNumber: 1111,
                                                                columnNumber: 57
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 1110,
                                                            columnNumber: 53
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h5", {
                                                            style: {
                                                                color: "#6c757d"
                                                            },
                                                            children: "No Followers Found"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 1113,
                                                            columnNumber: 53
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1109,
                                                    columnNumber: 49
                                                }, this),
                                                visibleUsers.length < filteredFollowers.length && (searchTerm.trim() === '' ? profile.followers.length || 0 : users.filter((user)=>profile.followers.includes(user._id) && (user.name.toLowerCase().includes(searchTerm.toLowerCase()) || user.username.toLowerCase().includes(searchTerm.toLowerCase()))).length) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "text-center mt-3",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                        className: "btn w-100 btn-outline-primary",
                                                        onClick: handleLoadMore,
                                                        children: "Load More"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1128,
                                                        columnNumber: 57
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1127,
                                                    columnNumber: 53
                                                }, this)
                                            ]
                                        }, void 0, true)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                    lineNumber: 1032,
                                    columnNumber: 29
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 1031,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 1022,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/pages/Dashboard/Messages.js",
                    lineNumber: 1021,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 1020,
                columnNumber: 13
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                className: `modal fade ${showMobileModal ? 'show' : ''}`,
                id: "mobileChat",
                tabIndex: "-1",
                "aria-labelledby": "mobileChatLabel",
                "aria-hidden": !showMobileModal,
                style: {
                    display: showMobileModal ? 'block' : 'none',
                    backgroundColor: showMobileModal ? 'rgba(0,0,0,0.5)' : 'transparent'
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                    className: "modal-dialog modal-fullscreen",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                        className: "modal-content",
                        style: {
                            background: styles.background,
                            color: styles.color
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "modal-header border-bottom",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "d-flex align-items-center gap-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "position-relative",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                        src: selectedUser?.image || __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$Images$2f$predefine$2e$webp$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$Images$2f$predefine$2e$webp__$28$static__in__ecmascript$2922$__$7d$__$5b$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                                        alt: selectedUser?.name || 'User',
                                                        width: 40,
                                                        height: 40,
                                                        className: "rounded-circle"
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1164,
                                                        columnNumber: 37
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                        className: "position-absolute",
                                                        style: {
                                                            bottom: '2px',
                                                            right: '2px',
                                                            width: '10px',
                                                            height: '10px',
                                                            backgroundColor: onlineUsers.has(selectedChat) ? '#10b981' : '#ef4444',
                                                            borderRadius: '50%',
                                                            border: '2px solid white'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1172,
                                                        columnNumber: 37
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 1163,
                                                columnNumber: 33
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h6", {
                                                        className: "mb-0 fw-bold",
                                                        children: selectedUser?.name || 'User'
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1186,
                                                        columnNumber: 37
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("small", {
                                                        className: "text-muted",
                                                        children: onlineUsers.has(selectedChat) ? 'Online' : 'Offline'
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1189,
                                                        columnNumber: 37
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 1185,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1162,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        className: "btn-close",
                                        onClick: handleBackToList,
                                        "aria-label": "Close"
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1194,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 1161,
                                columnNumber: 25
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                className: "modal-body p-0 d-flex flex-column",
                                style: {
                                    height: 'calc(100vh - 120px)'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "flex-grow-1 overflow-auto",
                                        style: {
                                            maxHeight: 'calc(100vh - 200px)',
                                            padding: '8px'
                                        },
                                        children: selectedChat && chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["Fragment"], {
                                            children: [
                                                chatMessages[selectedChat].map((msg)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        className: `d-flex mb-3 ${msg.sender === "You" ? "justify-content-end" : "justify-content-start"}`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            style: {
                                                                backgroundColor: msg.sender === "You" ? '#007bff' : 'rgba(255, 255, 255, 0.9)',
                                                                color: msg.sender === "You" ? 'white' : '#333',
                                                                maxWidth: '70%',
                                                                padding: '10px 15px',
                                                                borderRadius: msg.sender === "You" ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                                                                wordWrap: 'break-word',
                                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                                                textAlign: 'left'
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                    style: {
                                                                        fontSize: '14px',
                                                                        lineHeight: '1.4',
                                                                        marginBottom: '4px',
                                                                        textAlign: 'left'
                                                                    },
                                                                    children: msg.text
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 1234,
                                                                    columnNumber: 53
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                                    style: {
                                                                        display: 'flex',
                                                                        justifyContent: 'space-between',
                                                                        alignItems: 'center',
                                                                        marginTop: '4px'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("small", {
                                                                            style: {
                                                                                opacity: 0.7,
                                                                                fontSize: '11px'
                                                                            },
                                                                            children: msg.timestamp
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                                            lineNumber: 1248,
                                                                            columnNumber: 57
                                                                        }, this),
                                                                        msg.sender === "You" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("small", {
                                                                            style: {
                                                                                opacity: 0.7,
                                                                                fontSize: '11px',
                                                                                marginLeft: '8px'
                                                                            },
                                                                            children: msg.delivered ? "Seen" : "Sent"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                                            lineNumber: 1255,
                                                                            columnNumber: 61
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 1242,
                                                                    columnNumber: 53
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 1218,
                                                            columnNumber: 49
                                                        }, this)
                                                    }, msg.id, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1214,
                                                        columnNumber: 45
                                                    }, this)),
                                                typingUsers.has(selectedChat) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    className: "d-flex mb-3 justify-content-start",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                                            color: '#333',
                                                            maxWidth: '70%',
                                                            padding: '10px 15px',
                                                            borderRadius: '18px 18px 18px 4px',
                                                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                                            textAlign: 'left'
                                                        },
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                            className: "d-flex align-items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {
                                                                    className: "typing-indicator me-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                                            lineNumber: 1284,
                                                                            columnNumber: 61
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                                            lineNumber: 1285,
                                                                            columnNumber: 61
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("span", {}, void 0, false, {
                                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                                            lineNumber: 1286,
                                                                            columnNumber: 61
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 1283,
                                                                    columnNumber: 57
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("small", {
                                                                    style: {
                                                                        color: '#666',
                                                                        fontSize: '12px',
                                                                        fontStyle: 'italic'
                                                                    },
                                                                    children: "typing..."
                                                                }, void 0, false, {
                                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                                    lineNumber: 1288,
                                                                    columnNumber: 57
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 1282,
                                                            columnNumber: 53
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1271,
                                                        columnNumber: 49
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1270,
                                                    columnNumber: 45
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                    ref: messagesEndRef
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1300,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                            className: "text-center py-5",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                    className: "bi bi-chat-dots",
                                                    style: {
                                                        fontSize: '3rem',
                                                        opacity: 0.5
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1304,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("h4", {
                                                    className: "mt-3",
                                                    children: "No messages yet"
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1305,
                                                    columnNumber: 41
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("p", {
                                                    className: "",
                                                    children: "Start the conversation by sending a message!"
                                                }, void 0, false, {
                                                    fileName: "[project]/pages/Dashboard/Messages.js",
                                                    lineNumber: 1306,
                                                    columnNumber: 41
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 1303,
                                            columnNumber: 37
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1204,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                        className: "border-top p-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("form", {
                                            onSubmit: handleSendMessage,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("div", {
                                                className: "d-flex align-items-center gap-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("input", {
                                                        type: "text",
                                                        className: "form-control",
                                                        placeholder: "Message...",
                                                        value: newMessage,
                                                        onChange: (e)=>handleTyping(e.target.value),
                                                        onKeyDown: (e)=>{
                                                            if (e.key === 'Enter' && !e.shiftKey) {
                                                                e.preventDefault();
                                                                handleSendMessage(e);
                                                            }
                                                        },
                                                        style: {
                                                            borderRadius: '20px',
                                                            padding: '10px 15px',
                                                            border: '1px solid #ddd',
                                                            fontSize: '14px'
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1315,
                                                        columnNumber: 41
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("button", {
                                                        type: "submit",
                                                        className: "btn btn-primary",
                                                        onClick: handleSendMessage,
                                                        style: {
                                                            borderRadius: '50%',
                                                            width: '40px',
                                                            height: '40px',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            padding: '0'
                                                        },
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$externals$5d2f$react$2f$jsx$2d$dev$2d$runtime__$5b$external$5d$__$28$react$2f$jsx$2d$dev$2d$runtime$2c$__cjs$29$__["jsxDEV"])("i", {
                                                            className: "bi bi-send-fill"
                                                        }, void 0, false, {
                                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                                            lineNumber: 1348,
                                                            columnNumber: 45
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                                        lineNumber: 1334,
                                                        columnNumber: 41
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/pages/Dashboard/Messages.js",
                                                lineNumber: 1314,
                                                columnNumber: 37
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/pages/Dashboard/Messages.js",
                                            lineNumber: 1313,
                                            columnNumber: 33
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/pages/Dashboard/Messages.js",
                                        lineNumber: 1312,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/pages/Dashboard/Messages.js",
                                lineNumber: 1203,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/pages/Dashboard/Messages.js",
                        lineNumber: 1159,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/pages/Dashboard/Messages.js",
                    lineNumber: 1158,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/pages/Dashboard/Messages.js",
                lineNumber: 1147,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/pages/Dashboard/Messages.js",
        lineNumber: 592,
        columnNumber: 9
    }, this);
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6eb99c8a._.js.map