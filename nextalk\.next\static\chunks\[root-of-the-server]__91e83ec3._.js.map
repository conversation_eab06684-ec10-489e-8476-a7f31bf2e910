{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/context/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext } from 'react';\r\n\r\nconst ThemeContext = createContext();\r\n\r\nexport const ThemeProvider = ({ children }) => {\r\n    const [theme, setTheme] = useState('homeback');\r\n\r\n    const handleThemeClick = (newTheme) => {\r\n        setTheme(newTheme);\r\n    };\r\n\r\n    return (\r\n        <ThemeContext.Provider value={{ theme, handleThemeClick }}>\r\n            {children}\r\n        </ThemeContext.Provider>\r\n    );\r\n};\r\n\r\nexport const useTheme = () => useContext(ThemeContext);\r\n"], "names": [], "mappings": ";;;;;AAAA;;;;AAEA,MAAM,6BAAe,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;AAE1B,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE;;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,mBAAmB,CAAC;QACtB,SAAS;IACb;IAEA,qBACI,0JAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAiB;kBACnD;;;;;;AAGb;GAZa;KAAA;AAcN,MAAM,WAAW;;IAAM,OAAA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;AAAY;IAAxC", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/public/Images/predefine.webp.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 200, height: 200, blurDataURL: \"data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA2S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/axiosConfig.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create an instance with proper server URL\r\nconst axiosInstance = axios.create({\r\n    baseURL: process.env.REACT_APP_API_URL ||\r\n             process.env.NEXT_PUBLIC_SERVER_URL ||\r\n             (process.env.NODE_ENV === 'production'\r\n               ? 'https://nextalk-u0y1.onrender.com'\r\n               : 'http://localhost:5000'),\r\n    withCredentials: true,           // Crucial for sending cookies\r\n    headers: {\r\n        \"Content-Type\": \"application/json\",\r\n    },\r\n});\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAIa;AAJb;;AAEA,4CAA4C;AAC5C,MAAM,gBAAgB,iIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,IAC7B,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAClC,CAAC,6EAEG,uBAAuB;IACpC,iBAAiB;IACjB,SAAS;QACL,gBAAgB;IACpB;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Components/DashboardLayout.js"], "sourcesContent": ["// DashboardLayout.jsx\r\n'use client'; // if you're using App Router (recommended)\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from 'next/image';\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport axios from '../../utils/axiosConfig';\r\n\r\nexport default function DashboardLayout({ children }) {\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const { theme, handleThemeClick } = useTheme();\r\n    const router = useRouter(); // corrected here\r\n\r\n    const toggleTheme = () => {\r\n        const newTheme = theme === 'dark' ? 'light' : 'dark';\r\n        handleThemeClick(newTheme);\r\n    };\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return { background: '#0f172a', color: '#e2e8f0', chatBackground: '#1e293b', cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)' };\r\n        }\r\n        return { background: '#f1f5f9', color: '#1e293b', chatBackground: '#ffffff' };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyles();\r\n    const [user, setUser] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const storedUser = sessionStorage.getItem(\"user\");\r\n        if (!storedUser) {\r\n            router.push(\"/\");\r\n        } else {\r\n            try {\r\n                const parsed = JSON.parse(storedUser);\r\n                setUser(parsed.user);\r\n            } catch (err) {\r\n                console.error(\"Error parsing sessionStorage user:\", err);\r\n                router.push(\"/\");\r\n            }\r\n        }\r\n    }, [router]);\r\n\r\n    const [profile, setProfile] = useState(null);\r\n\r\n    const [tempProfile, setTempProfile] = useState(profile);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchProfile = async () => {\r\n        setLoading(true);\r\n        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');\r\n\r\n        if (!userData?.user?.id) {\r\n            setError('No user data found. Please log in.');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const response = await axios.get('https://nextalk-u0y1.onrender.com/profile', {\r\n                headers: {\r\n                    Authorization: `Bearer ${userData.user.id}`,\r\n                },\r\n            });\r\n            const fetchedProfile = response.data.user || response.data;\r\n            setProfile(fetchedProfile);\r\n            setTempProfile(fetchedProfile);\r\n            setLoading(false);\r\n        } catch (err) {\r\n            const errorMessage = err.response?.data?.message || 'Failed to load profile.';\r\n            setError(errorMessage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (typeof window !== 'undefined') {\r\n            fetchProfile();\r\n        }\r\n    }, []);\r\n\r\n\r\n    const pathname = usePathname();\r\n    const [brandText, setBrandText] = useState(\"Nextalk\");\r\n    useEffect(() => {\r\n        if (pathname === \"/Dashboard/Profile\") {\r\n            setBrandText(profile?.name || \"User\");\r\n        } else {\r\n            setBrandText(\"Nextalk\");\r\n        }\r\n    }, [pathname, profile]);\r\n\r\n\r\n    const [showConfirm, setShowConfirm] = useState(false);\r\n    const handleLogout = () => {\r\n        setShowConfirm(true);\r\n    };\r\n\r\n    const handleConfirmUpload = async (e) => {\r\n        sessionStorage.removeItem(\"user\");\r\n        router.push(\"/\");\r\n    }\r\n\r\n    const [pendingCount, setPendingCount] = useState(0);\r\n\r\n    useEffect(() => {\r\n        const fetchPendingRequests = async () => {\r\n            try {\r\n                const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n                const sessionId = storedUser?.user?.id;\r\n\r\n                if (!sessionId) {\r\n                    console.log(\"No session ID found, skipping pending requests fetch\");\r\n                    return;\r\n                }\r\n\r\n                // Add timeout and better error handling\r\n                const controller = new AbortController();\r\n                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout\r\n\r\n                const res = await axios.get(\r\n                    `https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`,\r\n                    {\r\n                        signal: controller.signal,\r\n                        timeout: 10000,\r\n                        headers: {\r\n                            'Content-Type': 'application/json'\r\n                        }\r\n                    }\r\n                );\r\n\r\n                clearTimeout(timeoutId);\r\n                setPendingCount(res.data.count || 0);\r\n                console.log(\"✅ Pending requests fetched successfully:\", res.data.count);\r\n\r\n            } catch (err) {\r\n                if (err.name === 'AbortError') {\r\n                    console.log(\"⏰ Request timeout - skipping pending requests\");\r\n                } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error')) {\r\n                    console.log(\"🌐 Network error - will retry later\");\r\n                } else {\r\n                    console.error(\"❌ Failed to fetch pending request count:\", err.message);\r\n                }\r\n                // Set default count on error\r\n                setPendingCount(0);\r\n            }\r\n        };\r\n\r\n        // Initial fetch with delay to avoid immediate network issues\r\n        const initialTimeout = setTimeout(() => {\r\n            fetchPendingRequests();\r\n        }, 2000);\r\n\r\n        // OPTIONAL: Poll every 60s for updates (increased interval to reduce network load)\r\n        const interval = setInterval(fetchPendingRequests, 60000);\r\n\r\n        return () => {\r\n            clearTimeout(initialTimeout);\r\n            clearInterval(interval);\r\n        };\r\n    }, []);\r\n\r\n    const [users, setUsers] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                const response = await axios.post(\r\n                    \"https://nextalk-u0y1.onrender.com/displayusersProfile\",\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true\r\n                    }\r\n                );\r\n\r\n                const allUsers = response.data;\r\n                const filtered = allUsers.filter(user => user._id !== sessionId);\r\n                setUsers(filtered);\r\n            } catch (error) {\r\n                console.error(\"Error fetching users:\", error);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (searchTerm.trim() === '') {\r\n            setSearchResults([]);\r\n            return;\r\n        }\r\n\r\n        const results = users.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setSearchResults(results);\r\n    }, [searchTerm, users]);\r\n\r\n\r\n    return (\r\n        <div className=\"dashboard-wrapper\" style={{ background: currentThemeStyles.background, color: currentThemeStyles.color }}>\r\n            {/* Mobile Navbar */}\r\n            <nav className={`navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`}>\r\n                <div className=\"container-fluid\">\r\n                    <Link className=\"navbar-brand fw-bold sleek-brand\" style={{ textDecoration: \"none\" }} href=\"/dashboard/profile\">{brandText}</Link>\r\n                    <button\r\n                        className={`navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`}\r\n                        type=\"button\"\r\n                        onClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n                    >\r\n                        <span className=\"navbar-toggler-icon\"></span>\r\n                    </button>\r\n                </div>\r\n            </nav>\r\n\r\n            {/* Sidebar */}\r\n            <aside className={`sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`}>\r\n                <div className=\"sidebar-header sleek-header\">\r\n                    <h4 className=\"p-3 fw-bold text-uppercase d-none d-lg-block\">{brandText}</h4>\r\n                </div>\r\n\r\n                <ul className=\"nav flex-column p-3 sleek-nav\">\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard\">\r\n                            <i className=\"bi bi-house-fill me-2\"></i>Home\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <button className=\"nav-link sleek-nav-link w-100\" data-bs-toggle=\"offcanvas\" data-bs-target=\"#Search\" style={{ textDecoration: \"none\" }}>\r\n                            <i className=\"bi bi-search me-2\"></i>Search\r\n                        </button>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-box me-2\"></i>Chat with Random\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-plus-square me-2\"></i>Create\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Messages\">\r\n                            <i className=\"bi bi-chat-fill me-2\"></i>Messages\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-cpu me-2\"></i>Chat with NexTalk\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item ot-but\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Settings\">\r\n                            <i className=\"bi bi-gear-wide-connected me-2\"></i>Settings\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link justify-content-between\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Notification\">\r\n                            <div>\r\n                                <i className=\"bi bi-heart me-2\"></i>Notification\r\n                            </div>\r\n                            {pendingCount > 0 && (\r\n                                <span\r\n                                    style={{\r\n                                        backgroundColor: \"#008080\",\r\n                                        color: \"white\",\r\n                                        fontSize: \"0.7rem\",\r\n                                        padding: \"6px 12px\",\r\n                                        borderRadius: \"50%\",\r\n                                        top: \"10px\",\r\n                                        right: \"10px\",\r\n                                    }}\r\n                                >\r\n                                    {pendingCount}\r\n                                </span>\r\n                            )}\r\n                        </Link>\r\n                    </li><br />\r\n                    <li className='nav-item'>\r\n                        <Link href=\"\"\r\n                            className=\"btn btn-primary w-100 p-2 d-lg-none\" style={{ textDecoration: \"none\" }}\r\n                            type=\"button\"\r\n                            onClick={() => setIsSidebarOpen(false)}\r\n                        >Close </Link></li>\r\n                </ul>\r\n\r\n\r\n                <div className=\"sidebar-footer p-3 sleek-footer\">\r\n                    {loading ? (\r\n                        <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ background: \"darkgray\", textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                            <div\r\n                                className=\"skeleton\"\r\n                                style={{\r\n                                    width: \"45px\",\r\n                                    height: \"45px\",\r\n                                    borderRadius: \"50%\",\r\n                                }}\r\n                            ></div>\r\n                            <div>\r\n                                <div\r\n                                    className=\"skeleton\"\r\n                                    style={{\r\n                                        width: \"120px\",\r\n                                        height: \"16px\",\r\n                                        borderRadius: \"4px\",\r\n                                        marginBottom: \"8px\",\r\n                                    }}\r\n                                ></div>\r\n                                <span className=\"sleek-status online\">Online</span>\r\n                            </div>\r\n                        </Link>\r\n                    ) :\r\n                        (\r\n                            <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                                {profile && (\r\n                                    <Image\r\n                                        key={profile.image}\r\n                                        src={profile.image || \"/Images/predefine.webp\"}\r\n                                        width={45}\r\n                                        height={45}\r\n                                        alt=\"User\"\r\n                                        className=\"rounded-circle sleek-avatar\"\r\n                                    />\r\n                                )}\r\n                                <div>\r\n                                    <span className=\"d-block fw-semibold sleek-username\">{profile.name || \"Guest\"}</span>\r\n                                    <span className=\"sleek-status online\">Online</span>\r\n                                </div>\r\n                            </Link>\r\n                        )\r\n                    }\r\n                    <div className=\"mt-4 sleek-actions\">\r\n                        <button\r\n                            onClick={toggleTheme}\r\n                            className=\"btn sleek-btn theme-toggle w-100 mb-2\"\r\n                        >\r\n                            {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\r\n                        </button>\r\n                        <button\r\n                            onClick={handleLogout}\r\n                            className=\"btn sleek-btn logout-btn w-100\"\r\n                        >\r\n                            Log Out\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </aside>\r\n\r\n            {/* Main Content */}\r\n            <main className=\"main-content sleek-main\" style={{ background: currentThemeStyles.chatBackground }}>\r\n                <div className=\"container-fluid p-1 sleek-container\">\r\n                    {children} {/* NOT Outlet! */}\r\n                </div>\r\n                {showConfirm && (\r\n                    <div className=\"modal-overlay\">\r\n                        <div className=\"modal-content\">\r\n                            <h4 className=\"modal-title\">Log Out?</h4>\r\n                            <p className=\"modal-text\">\r\n                                Are you sure you want to log out? You will be signed out of your account and redirected to the login page.\r\n                            </p>\r\n\r\n                            <div className=\"modal-actions\">\r\n                                <button className=\"modal-btn modal-btn-success\" onClick={handleConfirmUpload}>\r\n                                    ✅ Confirm !\r\n                                </button>\r\n                                <button className=\"modal-btn modal-btn-cancel\" onClick={() => setShowConfirm(false)}>\r\n                                    ❌ Cancel\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n                <div className=\"offcanvas offcanvas-start\" style={{ background: currentThemeStyles.cardBg, color: currentThemeStyles.color }} id=\"Search\">\r\n                    <div className=\"offcanvas-header\">\r\n                        <h3 className=\"offcanvas-title\">Search</h3>\r\n                        <button type=\"button\" className=\"btn-close bg-danger\" data-bs-dismiss=\"offcanvas\"></button>\r\n                    </div>\r\n                    <div className=\"offcanvas-body\">\r\n                        <input\r\n                            type=\"search\"\r\n                            name=\"search\"\r\n                            id=\"search\"\r\n                            className=\"form-control mb-3\"\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            style={{\r\n                                backgroundColor: \"white\",\r\n                                transition: \"background 0.3s\",\r\n                                gap: \"10px\",\r\n                                border: \"1px solid #333\"\r\n                            }}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                            onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                        />\r\n                        {\r\n                            loading ? (\r\n                                <div className='d-flex gap-4'>\r\n                                    <div\r\n                                        className=\"skeleton\"\r\n                                        style={{\r\n                                            width: \"45px\",\r\n                                            height: \"45px\",\r\n                                            borderRadius: \"50%\",\r\n                                        }}\r\n                                    ></div>\r\n                                    <div>\r\n                                        <div\r\n                                            className=\"skeleton\"\r\n                                            style={{\r\n                                                width: \"120px\",\r\n                                                height: \"16px\",\r\n                                                borderRadius: \"4px\",\r\n                                                marginBottom: \"8px\",\r\n                                            }}\r\n                                        ></div>\r\n                                    </div>\r\n                                </div>\r\n                            ) : (\r\n                                <div>\r\n                                    {searchResults.map(user => (\r\n                                        <div\r\n                                            key={user._id}\r\n                                            className=\"d-flex gap-4 align-items-center user-result mb-2 p-2 rounded\"\r\n                                            style={{\r\n                                                cursor: \"pointer\",\r\n                                            }}\r\n                                        >\r\n                                            <Image\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={60}\r\n                                                height={60}\r\n                                                className=\"rounded-circle\"\r\n                                                style={{ objectFit: \"cover\" }}\r\n                                            />\r\n                                            <div>\r\n                                                <strong>{user.username}</strong><br />\r\n                                                <span>{user.name}</span>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                    ))}\r\n\r\n                                    {searchResults.length === 0 && searchTerm && (\r\n                                        <div className=\"text-center mt-4 fade-in\">\r\n                                            <svg\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                width=\"64\"\r\n                                                height=\"64\"\r\n                                                fill=\"gray\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                style={{ opacity: 0.5 }}\r\n                                            >\r\n                                                <path d=\"M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z\" />\r\n                                            </svg>\r\n                                            <p className=\"mt-2\">No users found</p>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            )\r\n                        }\r\n                    </div>\r\n                </div>\r\n\r\n            </main>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA,cAAc,2CAA2C;;;;;;;;AAU1C,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD,KAAK,iBAAiB;IAE7C,MAAM,cAAc;QAChB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,iBAAiB;IACrB;IAEA,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBAAE,YAAY;gBAAW,OAAO;gBAAW,gBAAgB;gBAAW,QAAQ;YAAoD;QAC7I;QACA,OAAO;YAAE,YAAY;YAAW,OAAO;YAAW,gBAAgB;QAAU;IAChF;IAEA,MAAM,qBAAqB;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,MAAM,aAAa,eAAe,OAAO,CAAC;YAC1C,IAAI,CAAC,YAAY;gBACb,OAAO,IAAI,CAAC;YAChB,OAAO;gBACH,IAAI;oBACA,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,QAAQ,OAAO,IAAI;gBACvB,EAAE,OAAO,KAAK;oBACV,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;oCAAG;QAAC;KAAO;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe;QACjB,WAAW;QACX,MAAM,WAAW,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,WAAW;QAE9D,IAAI,CAAC,UAAU,MAAM,IAAI;YACrB,SAAS;YACT,WAAW;YACX;QACJ;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAK,CAAC,GAAG,CAAC,6CAA6C;gBAC1E,SAAS;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE;gBAC/C;YACJ;YACA,MAAM,iBAAiB,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YAC1D,WAAW;YACX,eAAe;YACf,WAAW;QACf,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YACT,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,wCAAmC;gBAC/B;YACJ;QACJ;oCAAG,EAAE;IAGL,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,IAAI,aAAa,sBAAsB;gBACnC,aAAa,SAAS,QAAQ;YAClC,OAAO;gBACH,aAAa;YACjB;QACJ;oCAAG;QAAC;QAAU;KAAQ;IAGtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;QACjB,eAAe;IACnB;IAEA,MAAM,sBAAsB,OAAO;QAC/B,eAAe,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC;IAChB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,MAAM;kEAAuB;oBACzB,IAAI;wBACA,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;wBACrD,MAAM,YAAY,YAAY,MAAM;wBAEpC,IAAI,CAAC,WAAW;4BACZ,QAAQ,GAAG,CAAC;4BACZ;wBACJ;wBAEA,wCAAwC;wBACxC,MAAM,aAAa,IAAI;wBACvB,MAAM,YAAY;wFAAW,IAAM,WAAW,KAAK;uFAAI,QAAQ,oBAAoB;wBAEnF,MAAM,MAAM,MAAM,gHAAA,CAAA,UAAK,CAAC,GAAG,CACvB,CAAC,0DAA0D,EAAE,WAAW,EACxE;4BACI,QAAQ,WAAW,MAAM;4BACzB,SAAS;4BACT,SAAS;gCACL,gBAAgB;4BACpB;wBACJ;wBAGJ,aAAa;wBACb,gBAAgB,IAAI,IAAI,CAAC,KAAK,IAAI;wBAClC,QAAQ,GAAG,CAAC,4CAA4C,IAAI,IAAI,CAAC,KAAK;oBAE1E,EAAE,OAAO,KAAK;wBACV,IAAI,IAAI,IAAI,KAAK,cAAc;4BAC3B,QAAQ,GAAG,CAAC;wBAChB,OAAO,IAAI,IAAI,IAAI,KAAK,mBAAmB,IAAI,OAAO,CAAC,QAAQ,CAAC,kBAAkB;4BAC9E,QAAQ,GAAG,CAAC;wBAChB,OAAO;4BACH,QAAQ,KAAK,CAAC,4CAA4C,IAAI,OAAO;wBACzE;wBACA,6BAA6B;wBAC7B,gBAAgB;oBACpB;gBACJ;;YAEA,6DAA6D;YAC7D,MAAM,iBAAiB;4DAAW;oBAC9B;gBACJ;2DAAG;YAEH,mFAAmF;YACnF,MAAM,WAAW,YAAY,sBAAsB;YAEnD;6CAAO;oBACH,aAAa;oBACb,cAAc;gBAClB;;QACJ;oCAAG,EAAE;IAEL,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,MAAM;wDAAa;oBACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;oBACrD,MAAM,YAAY,YAAY,MAAM;oBACpC,iBAAiB;oBAEjB,IAAI;wBACA,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;4BACI,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,iBAAiB;wBACrB;wBAGJ,MAAM,WAAW,SAAS,IAAI;wBAC9B,MAAM,WAAW,SAAS,MAAM;6EAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;wBACtD,SAAS;oBACb,EAAE,OAAO,OAAO;wBACZ,QAAQ,KAAK,CAAC,yBAAyB;oBAC3C;gBACJ;;YAEA;QACJ;oCAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,IAAI,WAAW,IAAI,OAAO,IAAI;gBAC1B,iBAAiB,EAAE;gBACnB;YACJ;YAEA,MAAM,UAAU,MAAM,MAAM;qDAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAG3D,iBAAiB;QACrB;oCAAG;QAAC;QAAY;KAAM;IAGtB,qBACI,0JAAC;QAAI,WAAU;QAAoB,OAAO;YAAE,YAAY,mBAAmB,UAAU;YAAE,OAAO,mBAAmB,KAAK;QAAC;;0BAEnH,0JAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,UAAU,SAAS,YAAY,WAAW,UAAU,CAAC;0BACxF,cAAA,0JAAC;oBAAI,WAAU;;sCACX,0JAAC,wHAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,OAAO;gCAAE,gBAAgB;4BAAO;4BAAG,MAAK;sCAAsB;;;;;;sCACjH,0JAAC;4BACG,WAAW,CAAC,6BAA6B,EAAE,UAAU,SAAS,iBAAiB,iBAAiB;4BAChG,MAAK;4BACL,SAAS,IAAM,iBAAiB,CAAC;sCAEjC,cAAA,0JAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,0JAAC;gBAAM,WAAW,CAAC,sBAAsB,EAAE,UAAU,SAAS,YAAY,WAAW,CAAC,EAAE,gBAAgB,SAAS,IAAI;;kCACjH,0JAAC;wBAAI,WAAU;kCACX,cAAA,0JAAC;4BAAG,WAAU;sCAAgD;;;;;;;;;;;kCAGlE,0JAAC;wBAAG,WAAU;;0CACV,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAGjD,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC;oCAAO,WAAU;oCAAgC,kBAAe;oCAAY,kBAAe;oCAAU,OAAO;wCAAE,gBAAgB;oCAAO;;sDAClI,0JAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;0CAG7C,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAA6B;;;;;;;;;;;;0CAGlD,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAA2B;;;;;;;;;;;;0CAGhD,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAAqC;;;;;;;;;;;;0CAG1D,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAAkD,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDAC9I,0JAAC;;8DACG,0JAAC;oDAAE,WAAU;;;;;;gDAAuB;;;;;;;wCAEvC,eAAe,mBACZ,0JAAC;4CACG,OAAO;gDACH,iBAAiB;gDACjB,OAAO;gDACP,UAAU;gDACV,SAAS;gDACT,cAAc;gDACd,KAAK;gDACL,OAAO;4CACX;sDAEC;;;;;;;;;;;;;;;;;0CAIZ,0JAAC;;;;;0CACN,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,MAAK;oCACP,WAAU;oCAAsC,OAAO;wCAAE,gBAAgB;oCAAO;oCAChF,MAAK;oCACL,SAAS,IAAM,iBAAiB;8CACnC;;;;;;;;;;;;;;;;;kCAIT,0JAAC;wBAAI,WAAU;;4BACV,wBACG,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,YAAY;oCAAY,gBAAgB;gCAAO;gCAAG,WAAU;;kDACzI,0JAAC;wCACG,WAAU;wCACV,OAAO;4CACH,OAAO;4CACP,QAAQ;4CACR,cAAc;wCAClB;;;;;;kDAEJ,0JAAC;;0DACG,0JAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;oDACd,cAAc;gDAClB;;;;;;0DAEJ,0JAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;qDAK1C,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,gBAAgB;gCAAO;gCAAG,WAAU;;oCAChH,yBACG,0JAAC,yHAAA,CAAA,UAAK;wCAEF,KAAK,QAAQ,KAAK,IAAI;wCACtB,OAAO;wCACP,QAAQ;wCACR,KAAI;wCACJ,WAAU;uCALL,QAAQ,KAAK;;;;;kDAQ1B,0JAAC;;0DACG,0JAAC;gDAAK,WAAU;0DAAsC,QAAQ,IAAI,IAAI;;;;;;0DACtE,0JAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAKtD,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCACG,SAAS;wCACT,WAAU;kDAET,UAAU,SAAS,eAAe;;;;;;kDAEvC,0JAAC;wCACG,SAAS;wCACT,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,0JAAC;gBAAK,WAAU;gBAA0B,OAAO;oBAAE,YAAY,mBAAmB,cAAc;gBAAC;;kCAC7F,0JAAC;wBAAI,WAAU;;4BACV;4BAAS;;;;;;;oBAEb,6BACG,0JAAC;wBAAI,WAAU;kCACX,cAAA,0JAAC;4BAAI,WAAU;;8CACX,0JAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,0JAAC;oCAAE,WAAU;8CAAa;;;;;;8CAI1B,0JAAC;oCAAI,WAAU;;sDACX,0JAAC;4CAAO,WAAU;4CAA8B,SAAS;sDAAqB;;;;;;sDAG9E,0JAAC;4CAAO,WAAU;4CAA6B,SAAS,IAAM,eAAe;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOrG,0JAAC;wBAAI,WAAU;wBAA4B,OAAO;4BAAE,YAAY,mBAAmB,MAAM;4BAAE,OAAO,mBAAmB,KAAK;wBAAC;wBAAG,IAAG;;0CAC7H,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCAAG,WAAU;kDAAkB;;;;;;kDAChC,0JAAC;wCAAO,MAAK;wCAAS,WAAU;wCAAsB,mBAAgB;;;;;;;;;;;;0CAE1E,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCACG,MAAK;wCACL,MAAK;wCACL,IAAG;wCACH,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,OAAO;4CACH,iBAAiB;4CACjB,YAAY;4CACZ,KAAK;4CACL,QAAQ;wCACZ;wCACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;oCAG3D,wBACI,0JAAC;wCAAI,WAAU;;0DACX,0JAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;gDAClB;;;;;;0DAEJ,0JAAC;0DACG,cAAA,0JAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,cAAc;oDAClB;;;;;;;;;;;;;;;;6DAKZ,0JAAC;;4CACI,cAAc,GAAG,CAAC,CAAA,qBACf,0JAAC;oDAEG,WAAU;oDACV,OAAO;wDACH,QAAQ;oDACZ;;sEAEA,0JAAC,yHAAA,CAAA,UAAK;4DACF,KAAK,KAAK,KAAK,IAAI,wRAAA,CAAA,UAAS;4DAC5B,KAAK,KAAK,IAAI;4DACd,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,OAAO;gEAAE,WAAW;4DAAQ;;;;;;sEAEhC,0JAAC;;8EACG,0JAAC;8EAAQ,KAAK,QAAQ;;;;;;8EAAU,0JAAC;;;;;8EACjC,0JAAC;8EAAM,KAAK,IAAI;;;;;;;;;;;;;mDAhBf,KAAK,GAAG;;;;;4CAsBpB,cAAc,MAAM,KAAK,KAAK,4BAC3B,0JAAC;gDAAI,WAAU;;kEACX,0JAAC;wDACG,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,MAAK;wDACL,SAAQ;wDACR,OAAO;4DAAE,SAAS;wDAAI;kEAEtB,cAAA,0JAAC;4DAAK,GAAE;;;;;;;;;;;kEAEZ,0JAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE;GAvdwB;;QAEgB,mHAAA,CAAA,WAAQ;QAC7B,8HAAA,CAAA,YAAS;QAwEP,8HAAA,CAAA,cAAW;;;KA3ER", "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Dashboard/Chats.js"], "sourcesContent": ["// Chats.jsx\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport DashboardLayout from '../Components/DashboardLayout';\r\n\r\nexport default function Chats() {\r\n    const { theme } = useTheme();\r\n    const [messages, setMessages] = useState([\r\n        { id: 1, sender: \"You\", text: \"Hey, how's it going?\", timestamp: \"10:30 AM\" },\r\n        { id: 2, sender: \"Friend\", text: \"Pretty good, thanks! You?\", timestamp: \"10:32 AM\" },\r\n        { id: 3, sender: \"You\", text: \"Hey, how's it going?\", timestamp: \"10:42 AM\" },\r\n        { id: 4, sender: \"Friend\", text: \"Pretty good, thanks! You?\", timestamp: \"10:52 AM\" },\r\n    ]);\r\n    const [newMessage, setNewMessage] = useState(\"\");\r\n    const messagesEndRef = useRef(null);\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return { \r\n                background: '#1e293b', \r\n                color: '#e2e8f0', \r\n                inputBg: '#334155', \r\n                inputColor: '#e2e8f0',\r\n                messageBgYou: '#3b82f6',\r\n                messageBgFriend: '#4b5563'\r\n            };\r\n        }\r\n        // Default to light styles for 'homeback' or any other theme\r\n        return { \r\n            background: '#ffffff', \r\n            color: '#1e293b', \r\n            inputBg: '#f1f5f9', \r\n            inputColor: '#1e293b',\r\n            messageBgYou: '#3b82f6',\r\n            messageBgFriend: '#e5e7eb'\r\n        };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyles();\r\n\r\n    const handleSendMessage = (e) => {\r\n        e.preventDefault();\r\n        if (newMessage.trim()) {\r\n            const newMsg = {\r\n                id: messages.length + 1,\r\n                sender: \"You\",\r\n                text: newMessage,\r\n                timestamp: new Date().toLocaleTimeString([], { \r\n                    hour: '2-digit', \r\n                    minute: '2-digit', \r\n                    hour12: true // Force 12-hour format with AM/PM\r\n                })\r\n            };\r\n            setMessages([...messages, newMsg]);\r\n            setNewMessage(\"\");\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (messagesEndRef.current) {\r\n            messagesEndRef.current.scrollTo({\r\n                top: messagesEndRef.current.scrollHeight,\r\n                behavior: 'smooth'\r\n            });\r\n        }\r\n    }, [messages]);\r\n\r\n    return (\r\n        <DashboardLayout>\r\n        <div \r\n            className=\"chat-wrapper\" \r\n            style={{ \r\n                background: currentThemeStyles.background, \r\n                color: currentThemeStyles.color \r\n            }}\r\n        >\r\n            <div className=\"chat-header\">\r\n                <h2 className=\"chat-title\">Chats</h2>\r\n            </div>\r\n            <div className=\"chat-messages\" ref={messagesEndRef}>\r\n                {messages.map((msg) => (\r\n                    <div \r\n                        key={msg.id} \r\n                        className={`message ${msg.sender === \"You\" ? \"message-you\" : \"message-friend\"}`}\r\n                    >\r\n                        <div \r\n                            className=\"message-bubble\"\r\n                            style={{ \r\n                                background: msg.sender === \"You\" \r\n                                    ? currentThemeStyles.messageBgYou \r\n                                    : currentThemeStyles.messageBgFriend,\r\n                                color: msg.sender === \"You\" ? '#ffffff' : currentThemeStyles.color\r\n                            }}\r\n                        >\r\n                            <span className=\"message-text\">{msg.text}</span>\r\n                            <span className=\"message-timestamp\">{msg.timestamp}</span>\r\n                        </div>\r\n                    </div>\r\n                ))}\r\n            </div>\r\n            <form className=\"chat-input-form\" onSubmit={handleSendMessage}>\r\n                <input\r\n                    type=\"text\"\r\n                    className=\"chat-input\"\r\n                    placeholder=\"Type a message...\"\r\n                    value={newMessage}\r\n                    onChange={(e) => setNewMessage(e.target.value)}\r\n                    style={{ \r\n                        background: currentThemeStyles.inputBg, \r\n                        color: currentThemeStyles.inputColor \r\n                    }}\r\n                />\r\n                <button type=\"submit\" className=\"chat-send-btn\">\r\n                    <i className=\"bi bi-send-fill\"></i>\r\n                </button>\r\n            </form>\r\n        </div>\r\n        </DashboardLayout>\r\n    );\r\n}"], "names": [], "mappings": "AAAA,YAAY;;;;;AACZ;AACA;AACA;;;;;;AAEe,SAAS;;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YAAE,IAAI;YAAG,QAAQ;YAAO,MAAM;YAAwB,WAAW;QAAW;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAU,MAAM;YAA6B,WAAW;QAAW;QACpF;YAAE,IAAI;YAAG,QAAQ;YAAO,MAAM;YAAwB,WAAW;QAAW;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAU,MAAM;YAA6B,WAAW;QAAW;KACvF;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,iBAAiB;YACrB;QACJ;QACA,4DAA4D;QAC5D,OAAO;YACH,YAAY;YACZ,OAAO;YACP,SAAS;YACT,YAAY;YACZ,cAAc;YACd,iBAAiB;QACrB;IACJ;IAEA,MAAM,qBAAqB;IAE3B,MAAM,oBAAoB,CAAC;QACvB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,IAAI;YACnB,MAAM,SAAS;gBACX,IAAI,SAAS,MAAM,GAAG;gBACtB,QAAQ;gBACR,MAAM;gBACN,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;oBACzC,MAAM;oBACN,QAAQ;oBACR,QAAQ,KAAK,kCAAkC;gBACnD;YACJ;YACA,YAAY;mBAAI;gBAAU;aAAO;YACjC,cAAc;QAClB;IACJ;IAEA,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;2BAAE;YACN,IAAI,eAAe,OAAO,EAAE;gBACxB,eAAe,OAAO,CAAC,QAAQ,CAAC;oBAC5B,KAAK,eAAe,OAAO,CAAC,YAAY;oBACxC,UAAU;gBACd;YACJ;QACJ;0BAAG;QAAC;KAAS;IAEb,qBACI,0JAAC,kIAAA,CAAA,UAAe;kBAChB,cAAA,0JAAC;YACG,WAAU;YACV,OAAO;gBACH,YAAY,mBAAmB,UAAU;gBACzC,OAAO,mBAAmB,KAAK;YACnC;;8BAEA,0JAAC;oBAAI,WAAU;8BACX,cAAA,0JAAC;wBAAG,WAAU;kCAAa;;;;;;;;;;;8BAE/B,0JAAC;oBAAI,WAAU;oBAAgB,KAAK;8BAC/B,SAAS,GAAG,CAAC,CAAC,oBACX,0JAAC;4BAEG,WAAW,CAAC,QAAQ,EAAE,IAAI,MAAM,KAAK,QAAQ,gBAAgB,kBAAkB;sCAE/E,cAAA,0JAAC;gCACG,WAAU;gCACV,OAAO;oCACH,YAAY,IAAI,MAAM,KAAK,QACrB,mBAAmB,YAAY,GAC/B,mBAAmB,eAAe;oCACxC,OAAO,IAAI,MAAM,KAAK,QAAQ,YAAY,mBAAmB,KAAK;gCACtE;;kDAEA,0JAAC;wCAAK,WAAU;kDAAgB,IAAI,IAAI;;;;;;kDACxC,0JAAC;wCAAK,WAAU;kDAAqB,IAAI,SAAS;;;;;;;;;;;;2BAbjD,IAAI,EAAE;;;;;;;;;;8BAkBvB,0JAAC;oBAAK,WAAU;oBAAkB,UAAU;;sCACxC,0JAAC;4BACG,MAAK;4BACL,WAAU;4BACV,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,OAAO;gCACH,YAAY,mBAAmB,OAAO;gCACtC,OAAO,mBAAmB,UAAU;4BACxC;;;;;;sCAEJ,0JAAC;4BAAO,MAAK;4BAAS,WAAU;sCAC5B,cAAA,0JAAC;gCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;GAlHwB;;QACF,mHAAA,CAAA,WAAQ;;;KADN", "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/Dashboard/Chats\";\n\n/// <reference types=\"next/client\" />\r\n\r\n// inserted by rust code\r\ndeclare const PAGE_PATH: string\r\n\r\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\r\n;(window.__NEXT_P = window.__NEXT_P || []).push([\r\n  PAGE_PATH,\r\n  () => {\r\n    return require('PAGE')\r\n  },\r\n])\r\n// @ts-expect-error module.hot exists\r\nif (module.hot) {\r\n  // @ts-expect-error module.hot exists\r\n  module.hot.dispose(function () {\r\n    window.__NEXT_P.push([PAGE_PATH])\r\n  })\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}