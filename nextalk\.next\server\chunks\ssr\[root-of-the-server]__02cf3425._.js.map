{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/axiosConfig.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create an instance with smart server URL detection\r\nlet baseURL;\r\n\r\n// Check if we're on localhost (client-side)\r\nif (typeof window !== 'undefined' && window.location.hostname === 'localhost') {\r\n    baseURL = 'http://localhost:5000';\r\n} else if (process.env.NEXT_PUBLIC_SERVER_URL) {\r\n    baseURL = process.env.NEXT_PUBLIC_SERVER_URL;\r\n} else if (process.env.REACT_APP_API_URL) {\r\n    baseURL = process.env.REACT_APP_API_URL;\r\n} else {\r\n    // Default to production server\r\n    baseURL = 'https://nextalk-u0y1.onrender.com';\r\n}\r\n\r\nconsole.log('🌐 Axios baseURL:', baseURL);\r\n\r\nconst axiosInstance = axios.create({\r\n    baseURL: baseURL,\r\n    withCredentials: true,           // Crucial for sending cookies\r\n    headers: {\r\n        \"Content-Type\": \"application/json\",\r\n    },\r\n    timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add request interceptor for debugging\r\naxiosInstance.interceptors.request.use(\r\n    (config) => {\r\n        console.log('📤 Axios request:', config.method?.toUpperCase(), config.url);\r\n        return config;\r\n    },\r\n    (error) => {\r\n        console.error('❌ Axios request error:', error);\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Add response interceptor for debugging\r\naxiosInstance.interceptors.response.use(\r\n    (response) => {\r\n        console.log('✅ Axios response:', response.status, response.config.url);\r\n        return response;\r\n    },\r\n    (error) => {\r\n        console.error('❌ Axios response error:', error.message);\r\n        if (error.code === 'ECONNREFUSED' || error.message === 'Network Error') {\r\n            console.error('🚨 Server connection failed. Is your backend running on', baseURL, '?');\r\n        }\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,qDAAqD;AACrD,IAAI;AAEJ,4CAA4C;AAC5C,uCAA+E;IAC3E,UAAU;AACd,OAAO,IAAI,QAAQ,GAAG,CAAC,sBAAsB,EAAE;IAC3C,UAAU,QAAQ,GAAG,CAAC,sBAAsB;AAChD,OAAO,IAAI,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IACtC,UAAU,QAAQ,GAAG,CAAC,iBAAiB;AAC3C,OAAO;IACH,+BAA+B;IAC/B,UAAU;AACd;AAEA,QAAQ,GAAG,CAAC,qBAAqB;AAEjC,MAAM,gBAAgB,0GAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS;IACT,iBAAiB;IACjB,SAAS;QACL,gBAAgB;IACpB;IACA,SAAS;AACb;AAEA,wCAAwC;AACxC,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;IACG,QAAQ,GAAG,CAAC,qBAAqB,OAAO,MAAM,EAAE,eAAe,OAAO,GAAG;IACzE,OAAO;AACX,GACA,CAAC;IACG,QAAQ,KAAK,CAAC,0BAA0B;IACxC,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGJ,yCAAyC;AACzC,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;IACG,QAAQ,GAAG,CAAC,qBAAqB,SAAS,MAAM,EAAE,SAAS,MAAM,CAAC,GAAG;IACrE,OAAO;AACX,GACA,CAAC;IACG,QAAQ,KAAK,CAAC,2BAA2B,MAAM,OAAO;IACtD,IAAI,MAAM,IAAI,KAAK,kBAAkB,MAAM,OAAO,KAAK,iBAAiB;QACpE,QAAQ,KAAK,CAAC,2DAA2D,SAAS;IACtF;IACA,OAAO,QAAQ,MAAM,CAAC;AAC1B;uCAGW", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/public/Images/predefine.webp.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 200, height: 200, blurDataURL: \"data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA2S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Components/DashboardLayout.js"], "sourcesContent": ["// DashboardLayout.jsx\r\n'use client'; // if you're using App Router (recommended)\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from 'next/image';\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport axios from '../../utils/axiosConfig';\r\n\r\nexport default function DashboardLayout({ children }) {\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const { theme, handleThemeClick } = useTheme();\r\n    const router = useRouter(); // corrected here\r\n\r\n    const toggleTheme = () => {\r\n        const newTheme = theme === 'dark' ? 'light' : 'dark';\r\n        handleThemeClick(newTheme);\r\n    };\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return { background: '#0f172a', color: '#e2e8f0', chatBackground: '#1e293b', cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)' };\r\n        }\r\n        return { background: '#f1f5f9', color: '#1e293b', chatBackground: '#ffffff' };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyles();\r\n    const [user, setUser] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const storedUser = sessionStorage.getItem(\"user\");\r\n        if (!storedUser) {\r\n            router.push(\"/\");\r\n        } else {\r\n            try {\r\n                const parsed = JSON.parse(storedUser);\r\n                setUser(parsed.user);\r\n            } catch (err) {\r\n                console.error(\"Error parsing sessionStorage user:\", err);\r\n                router.push(\"/\");\r\n            }\r\n        }\r\n    }, [router]);\r\n\r\n    const [profile, setProfile] = useState(null);\r\n\r\n    const [tempProfile, setTempProfile] = useState(profile);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchProfile = async () => {\r\n        setLoading(true);\r\n        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');\r\n\r\n        if (!userData?.user?.id) {\r\n            setError('No user data found. Please log in.');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const response = await axios.get('https://nextalk-u0y1.onrender.com/profile', {\r\n                headers: {\r\n                    Authorization: `Bearer ${userData.user.id}`,\r\n                },\r\n            });\r\n            const fetchedProfile = response.data.user || response.data;\r\n            setProfile(fetchedProfile);\r\n            setTempProfile(fetchedProfile);\r\n            setLoading(false);\r\n        } catch (err) {\r\n            const errorMessage = err.response?.data?.message || 'Failed to load profile.';\r\n            setError(errorMessage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (typeof window !== 'undefined') {\r\n            fetchProfile();\r\n        }\r\n    }, []);\r\n\r\n\r\n    const pathname = usePathname();\r\n    const [brandText, setBrandText] = useState(\"Nextalk\");\r\n    useEffect(() => {\r\n        if (pathname === \"/Dashboard/Profile\") {\r\n            setBrandText(profile?.name || \"User\");\r\n        } else {\r\n            setBrandText(\"Nextalk\");\r\n        }\r\n    }, [pathname, profile]);\r\n\r\n\r\n    const [showConfirm, setShowConfirm] = useState(false);\r\n    const handleLogout = () => {\r\n        setShowConfirm(true);\r\n    };\r\n\r\n    const handleConfirmUpload = async (e) => {\r\n        sessionStorage.removeItem(\"user\");\r\n        router.push(\"/\");\r\n    }\r\n\r\n    const [pendingCount, setPendingCount] = useState(0);\r\n\r\n    useEffect(() => {\r\n        const fetchPendingRequests = async () => {\r\n            try {\r\n                const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n                const sessionId = storedUser?.user?.id;\r\n\r\n                if (!sessionId) {\r\n                    console.log(\"No session ID found, skipping pending requests fetch\");\r\n                    return;\r\n                }\r\n\r\n                // Add timeout and better error handling\r\n                const controller = new AbortController();\r\n                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout\r\n\r\n                const res = await axios.get(\r\n                    `https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`,\r\n                    {\r\n                        signal: controller.signal,\r\n                        timeout: 10000,\r\n                        headers: {\r\n                            'Content-Type': 'application/json'\r\n                        }\r\n                    }\r\n                );\r\n\r\n                clearTimeout(timeoutId);\r\n                setPendingCount(res.data.count || 0);\r\n                console.log(\"✅ Pending requests fetched successfully:\", res.data.count);\r\n\r\n            } catch (err) {\r\n                if (err.name === 'AbortError') {\r\n                    console.log(\"⏰ Request timeout - skipping pending requests\");\r\n                } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error')) {\r\n                    console.log(\"🌐 Network error - will retry later\");\r\n                } else {\r\n                    console.error(\"❌ Failed to fetch pending request count:\", err.message);\r\n                }\r\n                // Set default count on error\r\n                setPendingCount(0);\r\n            }\r\n        };\r\n\r\n        // Initial fetch with delay to avoid immediate network issues\r\n        const initialTimeout = setTimeout(() => {\r\n            fetchPendingRequests();\r\n        }, 2000);\r\n\r\n        // OPTIONAL: Poll every 60s for updates (increased interval to reduce network load)\r\n        const interval = setInterval(fetchPendingRequests, 60000);\r\n\r\n        return () => {\r\n            clearTimeout(initialTimeout);\r\n            clearInterval(interval);\r\n        };\r\n    }, []);\r\n\r\n    const [users, setUsers] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                const response = await axios.post(\r\n                    \"https://nextalk-u0y1.onrender.com/displayusersProfile\",\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true\r\n                    }\r\n                );\r\n\r\n                const allUsers = response.data;\r\n                const filtered = allUsers.filter(user => user._id !== sessionId);\r\n                setUsers(filtered);\r\n            } catch (error) {\r\n                console.error(\"Error fetching users:\", error);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (searchTerm.trim() === '') {\r\n            setSearchResults([]);\r\n            return;\r\n        }\r\n\r\n        const results = users.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setSearchResults(results);\r\n    }, [searchTerm, users]);\r\n\r\n\r\n    return (\r\n        <div className=\"dashboard-wrapper\" style={{ background: currentThemeStyles.background, color: currentThemeStyles.color }}>\r\n            {/* Mobile Navbar */}\r\n            <nav className={`navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`}>\r\n                <div className=\"container-fluid\">\r\n                    <Link className=\"navbar-brand fw-bold sleek-brand\" style={{ textDecoration: \"none\" }} href=\"/dashboard/profile\">{brandText}</Link>\r\n                    <button\r\n                        className={`navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`}\r\n                        type=\"button\"\r\n                        onClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n                    >\r\n                        <span className=\"navbar-toggler-icon\"></span>\r\n                    </button>\r\n                </div>\r\n            </nav>\r\n\r\n            {/* Sidebar */}\r\n            <aside className={`sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`}>\r\n                <div className=\"sidebar-header sleek-header\">\r\n                    <h4 className=\"p-3 fw-bold text-uppercase d-none d-lg-block\">{brandText}</h4>\r\n                </div>\r\n\r\n                <ul className=\"nav flex-column p-3 sleek-nav\">\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard\">\r\n                            <i className=\"bi bi-house-fill me-2\"></i>Home\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <button className=\"nav-link sleek-nav-link w-100\" data-bs-toggle=\"offcanvas\" data-bs-target=\"#Search\" style={{ textDecoration: \"none\" }}>\r\n                            <i className=\"bi bi-search me-2\"></i>Search\r\n                        </button>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-box me-2\"></i>Chat with Random\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-plus-square me-2\"></i>Create\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Messages\">\r\n                            <i className=\"bi bi-chat-fill me-2\"></i>Messages\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-cpu me-2\"></i>Chat with NexTalk\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item ot-but\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Settings\">\r\n                            <i className=\"bi bi-gear-wide-connected me-2\"></i>Settings\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link justify-content-between\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Notification\">\r\n                            <div>\r\n                                <i className=\"bi bi-heart me-2\"></i>Notification\r\n                            </div>\r\n                            {pendingCount > 0 && (\r\n                                <span\r\n                                    style={{\r\n                                        backgroundColor: \"#008080\",\r\n                                        color: \"white\",\r\n                                        fontSize: \"0.7rem\",\r\n                                        padding: \"6px 12px\",\r\n                                        borderRadius: \"50%\",\r\n                                        top: \"10px\",\r\n                                        right: \"10px\",\r\n                                    }}\r\n                                >\r\n                                    {pendingCount}\r\n                                </span>\r\n                            )}\r\n                        </Link>\r\n                    </li><br />\r\n                    <li className='nav-item'>\r\n                        <Link href=\"\"\r\n                            className=\"btn btn-primary w-100 p-2 d-lg-none\" style={{ textDecoration: \"none\" }}\r\n                            type=\"button\"\r\n                            onClick={() => setIsSidebarOpen(false)}\r\n                        >Close </Link></li>\r\n                </ul>\r\n\r\n\r\n                <div className=\"sidebar-footer p-3 sleek-footer\">\r\n                    {loading ? (\r\n                        <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ background: \"darkgray\", textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                            <div\r\n                                className=\"skeleton\"\r\n                                style={{\r\n                                    width: \"45px\",\r\n                                    height: \"45px\",\r\n                                    borderRadius: \"50%\",\r\n                                }}\r\n                            ></div>\r\n                            <div>\r\n                                <div\r\n                                    className=\"skeleton\"\r\n                                    style={{\r\n                                        width: \"120px\",\r\n                                        height: \"16px\",\r\n                                        borderRadius: \"4px\",\r\n                                        marginBottom: \"8px\",\r\n                                    }}\r\n                                ></div>\r\n                                <span className=\"sleek-status online\">Online</span>\r\n                            </div>\r\n                        </Link>\r\n                    ) :\r\n                        (\r\n                            <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                                {profile && (\r\n                                    <Image\r\n                                        key={profile.image}\r\n                                        src={profile.image || \"/Images/predefine.webp\"}\r\n                                        width={45}\r\n                                        height={45}\r\n                                        alt=\"User\"\r\n                                        className=\"rounded-circle sleek-avatar\"\r\n                                    />\r\n                                )}\r\n                                <div>\r\n                                    <span className=\"d-block fw-semibold sleek-username\">{profile.name || \"Guest\"}</span>\r\n                                    <span className=\"sleek-status online\">Online</span>\r\n                                </div>\r\n                            </Link>\r\n                        )\r\n                    }\r\n                    <div className=\"mt-4 sleek-actions\">\r\n                        <button\r\n                            onClick={toggleTheme}\r\n                            className=\"btn sleek-btn theme-toggle w-100 mb-2\"\r\n                        >\r\n                            {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\r\n                        </button>\r\n                        <button\r\n                            onClick={handleLogout}\r\n                            className=\"btn sleek-btn logout-btn w-100\"\r\n                        >\r\n                            Log Out\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </aside>\r\n\r\n            {/* Main Content */}\r\n            <main className=\"main-content sleek-main\" style={{ background: currentThemeStyles.chatBackground }}>\r\n                <div className=\"container-fluid p-1 sleek-container\">\r\n                    {children} {/* NOT Outlet! */}\r\n                </div>\r\n                {showConfirm && (\r\n                    <div className=\"modal-overlay\">\r\n                        <div className=\"modal-content\">\r\n                            <h4 className=\"modal-title\">Log Out?</h4>\r\n                            <p className=\"modal-text\">\r\n                                Are you sure you want to log out? You will be signed out of your account and redirected to the login page.\r\n                            </p>\r\n\r\n                            <div className=\"modal-actions\">\r\n                                <button className=\"modal-btn modal-btn-success\" onClick={handleConfirmUpload}>\r\n                                    ✅ Confirm !\r\n                                </button>\r\n                                <button className=\"modal-btn modal-btn-cancel\" onClick={() => setShowConfirm(false)}>\r\n                                    ❌ Cancel\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n                <div className=\"offcanvas offcanvas-start\" style={{ background: currentThemeStyles.cardBg, color: currentThemeStyles.color }} id=\"Search\">\r\n                    <div className=\"offcanvas-header\">\r\n                        <h3 className=\"offcanvas-title\">Search</h3>\r\n                        <button type=\"button\" className=\"btn-close bg-danger\" data-bs-dismiss=\"offcanvas\"></button>\r\n                    </div>\r\n                    <div className=\"offcanvas-body\">\r\n                        <input\r\n                            type=\"search\"\r\n                            name=\"search\"\r\n                            id=\"search\"\r\n                            className=\"form-control mb-3\"\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            style={{\r\n                                backgroundColor: \"white\",\r\n                                transition: \"background 0.3s\",\r\n                                gap: \"10px\",\r\n                                border: \"1px solid #333\"\r\n                            }}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                            onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                        />\r\n                        {\r\n                            loading ? (\r\n                                <div className='d-flex gap-4'>\r\n                                    <div\r\n                                        className=\"skeleton\"\r\n                                        style={{\r\n                                            width: \"45px\",\r\n                                            height: \"45px\",\r\n                                            borderRadius: \"50%\",\r\n                                        }}\r\n                                    ></div>\r\n                                    <div>\r\n                                        <div\r\n                                            className=\"skeleton\"\r\n                                            style={{\r\n                                                width: \"120px\",\r\n                                                height: \"16px\",\r\n                                                borderRadius: \"4px\",\r\n                                                marginBottom: \"8px\",\r\n                                            }}\r\n                                        ></div>\r\n                                    </div>\r\n                                </div>\r\n                            ) : (\r\n                                <div>\r\n                                    {searchResults.map(user => (\r\n                                        <div\r\n                                            key={user._id}\r\n                                            className=\"d-flex gap-4 align-items-center user-result mb-2 p-2 rounded\"\r\n                                            style={{\r\n                                                cursor: \"pointer\",\r\n                                            }}\r\n                                        >\r\n                                            <Image\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={60}\r\n                                                height={60}\r\n                                                className=\"rounded-circle\"\r\n                                                style={{ objectFit: \"cover\" }}\r\n                                            />\r\n                                            <div>\r\n                                                <strong>{user.username}</strong><br />\r\n                                                <span>{user.name}</span>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                    ))}\r\n\r\n                                    {searchResults.length === 0 && searchTerm && (\r\n                                        <div className=\"text-center mt-4 fade-in\">\r\n                                            <svg\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                width=\"64\"\r\n                                                height=\"64\"\r\n                                                fill=\"gray\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                style={{ opacity: 0.5 }}\r\n                                            >\r\n                                                <path d=\"M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z\" />\r\n                                            </svg>\r\n                                            <p className=\"mt-2\">No users found</p>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            )\r\n                        }\r\n                    </div>\r\n                </div>\r\n\r\n            </main>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AARA,cAAc,2CAA2C;;;;;;;;;AAU1C,SAAS,gBAAgB,EAAE,QAAQ,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,YAAS,AAAD,KAAK,iBAAiB;IAE7C,MAAM,cAAc;QAChB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,iBAAiB;IACrB;IAEA,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBAAE,YAAY;gBAAW,OAAO;gBAAW,gBAAgB;gBAAW,QAAQ;YAAoD;QAC7I;QACA,OAAO;YAAE,YAAY;YAAW,OAAO;YAAW,gBAAgB;QAAU;IAChF;IAEA,MAAM,qBAAqB;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa,eAAe,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;YACb,OAAO,IAAI,CAAC;QAChB,OAAO;YACH,IAAI;gBACA,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,QAAQ,OAAO,IAAI;YACvB,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ,GAAG;QAAC;KAAO;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe;QACjB,WAAW;QACX,MAAM,WAAW,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,WAAW;QAE9D,IAAI,CAAC,UAAU,MAAM,IAAI;YACrB,SAAS;YACT,WAAW;YACX;QACJ;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,6CAA6C;gBAC1E,SAAS;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE;gBAC/C;YACJ;YACA,MAAM,iBAAiB,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YAC1D,WAAW;YACX,eAAe;YACf,WAAW;QACf,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YACT,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,uCAAmC;;QAEnC;IACJ,GAAG,EAAE;IAGL,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa,sBAAsB;YACnC,aAAa,SAAS,QAAQ;QAClC,OAAO;YACH,aAAa;QACjB;IACJ,GAAG;QAAC;QAAU;KAAQ;IAGtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;QACjB,eAAe;IACnB;IAEA,MAAM,sBAAsB,OAAO;QAC/B,eAAe,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC;IAChB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,uBAAuB;YACzB,IAAI;gBACA,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;gBACrD,MAAM,YAAY,YAAY,MAAM;gBAEpC,IAAI,CAAC,WAAW;oBACZ,QAAQ,GAAG,CAAC;oBACZ;gBACJ;gBAEA,wCAAwC;gBACxC,MAAM,aAAa,IAAI;gBACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,oBAAoB;gBAEnF,MAAM,MAAM,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CACvB,CAAC,0DAA0D,EAAE,WAAW,EACxE;oBACI,QAAQ,WAAW,MAAM;oBACzB,SAAS;oBACT,SAAS;wBACL,gBAAgB;oBACpB;gBACJ;gBAGJ,aAAa;gBACb,gBAAgB,IAAI,IAAI,CAAC,KAAK,IAAI;gBAClC,QAAQ,GAAG,CAAC,4CAA4C,IAAI,IAAI,CAAC,KAAK;YAE1E,EAAE,OAAO,KAAK;gBACV,IAAI,IAAI,IAAI,KAAK,cAAc;oBAC3B,QAAQ,GAAG,CAAC;gBAChB,OAAO,IAAI,IAAI,IAAI,KAAK,mBAAmB,IAAI,OAAO,CAAC,QAAQ,CAAC,kBAAkB;oBAC9E,QAAQ,GAAG,CAAC;gBAChB,OAAO;oBACH,QAAQ,KAAK,CAAC,4CAA4C,IAAI,OAAO;gBACzE;gBACA,6BAA6B;gBAC7B,gBAAgB;YACpB;QACJ;QAEA,6DAA6D;QAC7D,MAAM,iBAAiB,WAAW;YAC9B;QACJ,GAAG;QAEH,mFAAmF;QACnF,MAAM,WAAW,YAAY,sBAAsB;QAEnD,OAAO;YACH,aAAa;YACb,cAAc;QAClB;IACJ,GAAG,EAAE;IAEL,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa;YACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YACpC,iBAAiB;YAEjB,IAAI;gBACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;oBACI,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,iBAAiB;gBACrB;gBAGJ,MAAM,WAAW,SAAS,IAAI;gBAC9B,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBACtD,SAAS;YACb,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,yBAAyB;YAC3C;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,WAAW,IAAI,OAAO,IAAI;YAC1B,iBAAiB,EAAE;YACnB;QACJ;QAEA,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAG3D,iBAAiB;IACrB,GAAG;QAAC;QAAY;KAAM;IAGtB,qBACI,qKAAC;QAAI,WAAU;QAAoB,OAAO;YAAE,YAAY,mBAAmB,UAAU;YAAE,OAAO,mBAAmB,KAAK;QAAC;;0BAEnH,qKAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,UAAU,SAAS,YAAY,WAAW,UAAU,CAAC;0BACxF,cAAA,qKAAC;oBAAI,WAAU;;sCACX,qKAAC,qHAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,OAAO;gCAAE,gBAAgB;4BAAO;4BAAG,MAAK;sCAAsB;;;;;;sCACjH,qKAAC;4BACG,WAAW,CAAC,6BAA6B,EAAE,UAAU,SAAS,iBAAiB,iBAAiB;4BAChG,MAAK;4BACL,SAAS,IAAM,iBAAiB,CAAC;sCAEjC,cAAA,qKAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,qKAAC;gBAAM,WAAW,CAAC,sBAAsB,EAAE,UAAU,SAAS,YAAY,WAAW,CAAC,EAAE,gBAAgB,SAAS,IAAI;;kCACjH,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAG,WAAU;sCAAgD;;;;;;;;;;;kCAGlE,qKAAC;wBAAG,WAAU;;0CACV,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAGjD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC;oCAAO,WAAU;oCAAgC,kBAAe;oCAAY,kBAAe;oCAAU,OAAO;wCAAE,gBAAgB;oCAAO;;sDAClI,qKAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;0CAG7C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA6B;;;;;;;;;;;;0CAGlD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA2B;;;;;;;;;;;;0CAGhD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqC;;;;;;;;;;;;0CAG1D,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAAkD,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDAC9I,qKAAC;;8DACG,qKAAC;oDAAE,WAAU;;;;;;gDAAuB;;;;;;;wCAEvC,eAAe,mBACZ,qKAAC;4CACG,OAAO;gDACH,iBAAiB;gDACjB,OAAO;gDACP,UAAU;gDACV,SAAS;gDACT,cAAc;gDACd,KAAK;gDACL,OAAO;4CACX;sDAEC;;;;;;;;;;;;;;;;;0CAIZ,qKAAC;;;;;0CACN,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,MAAK;oCACP,WAAU;oCAAsC,OAAO;wCAAE,gBAAgB;oCAAO;oCAChF,MAAK;oCACL,SAAS,IAAM,iBAAiB;8CACnC;;;;;;;;;;;;;;;;;kCAIT,qKAAC;wBAAI,WAAU;;4BACV,wBACG,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,YAAY;oCAAY,gBAAgB;gCAAO;gCAAG,WAAU;;kDACzI,qKAAC;wCACG,WAAU;wCACV,OAAO;4CACH,OAAO;4CACP,QAAQ;4CACR,cAAc;wCAClB;;;;;;kDAEJ,qKAAC;;0DACG,qKAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;oDACd,cAAc;gDAClB;;;;;;0DAEJ,qKAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;qDAK1C,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,gBAAgB;gCAAO;gCAAG,WAAU;;oCAChH,yBACG,qKAAC,sHAAA,CAAA,UAAK;wCAEF,KAAK,QAAQ,KAAK,IAAI;wCACtB,OAAO;wCACP,QAAQ;wCACR,KAAI;wCACJ,WAAU;uCALL,QAAQ,KAAK;;;;;kDAQ1B,qKAAC;;0DACG,qKAAC;gDAAK,WAAU;0DAAsC,QAAQ,IAAI,IAAI;;;;;;0DACtE,qKAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAKtD,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCACG,SAAS;wCACT,WAAU;kDAET,UAAU,SAAS,eAAe;;;;;;kDAEvC,qKAAC;wCACG,SAAS;wCACT,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,qKAAC;gBAAK,WAAU;gBAA0B,OAAO;oBAAE,YAAY,mBAAmB,cAAc;gBAAC;;kCAC7F,qKAAC;wBAAI,WAAU;;4BACV;4BAAS;;;;;;;oBAEb,6BACG,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAI,WAAU;;8CACX,qKAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,qKAAC;oCAAE,WAAU;8CAAa;;;;;;8CAI1B,qKAAC;oCAAI,WAAU;;sDACX,qKAAC;4CAAO,WAAU;4CAA8B,SAAS;sDAAqB;;;;;;sDAG9E,qKAAC;4CAAO,WAAU;4CAA6B,SAAS,IAAM,eAAe;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOrG,qKAAC;wBAAI,WAAU;wBAA4B,OAAO;4BAAE,YAAY,mBAAmB,MAAM;4BAAE,OAAO,mBAAmB,KAAK;wBAAC;wBAAG,IAAG;;0CAC7H,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAG,WAAU;kDAAkB;;;;;;kDAChC,qKAAC;wCAAO,MAAK;wCAAS,WAAU;wCAAsB,mBAAgB;;;;;;;;;;;;0CAE1E,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCACG,MAAK;wCACL,MAAK;wCACL,IAAG;wCACH,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,OAAO;4CACH,iBAAiB;4CACjB,YAAY;4CACZ,KAAK;4CACL,QAAQ;wCACZ;wCACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;oCAG3D,wBACI,qKAAC;wCAAI,WAAU;;0DACX,qKAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;gDAClB;;;;;;0DAEJ,qKAAC;0DACG,cAAA,qKAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,cAAc;oDAClB;;;;;;;;;;;;;;;;6DAKZ,qKAAC;;4CACI,cAAc,GAAG,CAAC,CAAA,qBACf,qKAAC;oDAEG,WAAU;oDACV,OAAO;wDACH,QAAQ;oDACZ;;sEAEA,qKAAC,sHAAA,CAAA,UAAK;4DACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;4DAC5B,KAAK,KAAK,IAAI;4DACd,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,OAAO;gEAAE,WAAW;4DAAQ;;;;;;sEAEhC,qKAAC;;8EACG,qKAAC;8EAAQ,KAAK,QAAQ;;;;;;8EAAU,qKAAC;;;;;8EACjC,qKAAC;8EAAM,KAAK,IAAI;;;;;;;;;;;;;mDAhBf,KAAK,GAAG;;;;;4CAsBpB,cAAc,MAAM,KAAK,KAAK,4BAC3B,qKAAC;gDAAI,WAAU;;kEACX,qKAAC;wDACG,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,MAAK;wDACL,SAAQ;wDACR,OAAO;4DAAE,SAAS;wDAAI;kEAEtB,cAAA,qKAAC;4DAAK,GAAE;;;;;;;;;;;kEAEZ,qKAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/socket.js"], "sourcesContent": ["import { io } from 'socket.io-client';\n\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.userId = null;\n    this.messageCallbacks = [];\n    this.onlineStatusCallbacks = [];\n    this.typingCallbacks = [];\n  }\n\n  connect(userId) {\n    if (this.socket && this.isConnected) {\n      return;\n    }\n\n    this.userId = userId;\n\n    // Connect to the server - Smart URL detection\n    let serverUrl;\n\n    // Check if we're on localhost\n    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {\n      serverUrl = 'http://localhost:5000';\n    } else if (process.env.NEXT_PUBLIC_SERVER_URL) {\n      serverUrl = process.env.NEXT_PUBLIC_SERVER_URL;\n    } else {\n      // Default to production server\n      serverUrl = 'https://nextalk-u0y1.onrender.com';\n    }\n\n    console.log('🔗 Connecting to server:', serverUrl);\n    console.log('🌍 Environment:', process.env.NODE_ENV);\n    console.log('🏠 Hostname:', typeof window !== 'undefined' ? window.location.hostname : 'server');\n\n    try {\n      this.socket = io(serverUrl, {\n        transports: ['polling'], // Start with polling only to avoid WebSocket errors\n        withCredentials: true,\n        forceNew: false,\n        timeout: 20000,\n        autoConnect: true,\n        reconnection: true,\n        reconnectionAttempts: 5,\n        reconnectionDelay: 1000\n      });\n    } catch (error) {\n      console.error('❌ Socket connection error:', error);\n      // Fallback to basic connection\n      this.socket = io(serverUrl);\n    }\n\n    this.socket.on('connect', () => {\n      console.log('✅ Connected to server with socket ID:', this.socket.id);\n      this.isConnected = true;\n\n      // Join with user ID\n      if (this.userId) {\n        console.log('🔗 Joining with user ID:', this.userId);\n        this.socket.emit('join', this.userId);\n      }\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('❌ Connection error:', error);\n      console.error('🚨 Failed to connect to:', serverUrl);\n\n      // Check if it's a localhost connection issue\n      if (serverUrl.includes('localhost')) {\n        console.error('🔧 Make sure your backend server is running on http://localhost:5000');\n        console.error('💡 Try running: npm start or node server.js in your backend directory');\n      }\n\n      // Try to reconnect with different transport\n      if (error.message.includes('websocket')) {\n        console.log('🔄 Retrying with polling transport...');\n        this.socket.io.opts.transports = ['polling'];\n      }\n    });\n\n    this.socket.on('reconnect', (attemptNumber) => {\n      console.log('🔄 Reconnected after', attemptNumber, 'attempts');\n    });\n\n    this.socket.on('reconnect_error', (error) => {\n      console.error('❌ Reconnection error:', error);\n    });\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n      this.isConnected = false;\n    });\n\n    // Handle incoming messages\n    this.socket.on('receiveMessage', (data) => {\n      console.log('📨 Socket received message:', data);\n      this.messageCallbacks.forEach(callback => {\n        console.log('🔄 Calling message callback');\n        callback(data);\n      });\n    });\n\n    // Handle message delivery confirmation\n    this.socket.on('messageDelivered', (data) => {\n      console.log('✅ Message delivered confirmation:', data);\n    });\n\n    // Handle message errors\n    this.socket.on('messageError', (data) => {\n      console.error('Message error:', data);\n    });\n\n    // Handle online users list\n    this.socket.on('onlineUsers', (userIds) => {\n      this.onlineStatusCallbacks.forEach(callback => \n        callback({ type: 'initial', userIds })\n      );\n    });\n\n    // Handle user coming online\n    this.socket.on('userOnline', (userId) => {\n      this.onlineStatusCallbacks.forEach(callback => \n        callback({ type: 'online', userId })\n      );\n    });\n\n    // Handle user going offline\n    this.socket.on('userOffline', (userId) => {\n      this.onlineStatusCallbacks.forEach(callback => \n        callback({ type: 'offline', userId })\n      );\n    });\n\n    // Handle typing indicators\n    this.socket.on('userTyping', (data) => {\n      this.typingCallbacks.forEach(callback => callback(data));\n    });\n\n    // Handle message read status\n    this.socket.on('messageRead', (data) => {\n      console.log('Message read:', data);\n    });\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n      this.userId = null;\n    }\n  }\n\n  sendMessage(receiverId, message, messageType = 'text') {\n    if (!this.socket || !this.isConnected) {\n      console.error('❌ Socket not connected');\n      return false;\n    }\n\n    if (!this.userId || !receiverId || !message) {\n      console.error('❌ Missing required data:', { userId: this.userId, receiverId, message });\n      return false;\n    }\n\n    const messageData = {\n      senderId: this.userId,\n      receiverId,\n      message,\n      messageType\n    };\n\n    console.log('📤 Sending message:', messageData);\n    this.socket.emit('sendMessage', messageData);\n\n    return true;\n  }\n\n  sendTypingIndicator(receiverId, isTyping) {\n    if (!this.socket || !this.isConnected) {\n      return;\n    }\n\n    this.socket.emit('typing', {\n      senderId: this.userId,\n      receiverId,\n      isTyping\n    });\n  }\n\n  markAsRead(chatId, messageId) {\n    if (!this.socket || !this.isConnected) {\n      return;\n    }\n\n    this.socket.emit('markAsRead', {\n      chatId,\n      messageId,\n      userId: this.userId\n    });\n  }\n\n  // Callback management\n  onMessage(callback) {\n    this.messageCallbacks.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.messageCallbacks = this.messageCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  onOnlineStatus(callback) {\n    this.onlineStatusCallbacks.push(callback);\n    \n    return () => {\n      this.onlineStatusCallbacks = this.onlineStatusCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  onTyping(callback) {\n    this.typingCallbacks.push(callback);\n    \n    return () => {\n      this.typingCallbacks = this.typingCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  // Get connection status\n  getConnectionStatus() {\n    return {\n      isConnected: this.isConnected,\n      userId: this.userId,\n      socketId: this.socket?.id\n    };\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\n\nexport default socketService;\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAAC,qBAAqB,GAAG,EAAE;QAC/B,IAAI,CAAC,eAAe,GAAG,EAAE;IAC3B;IAEA,QAAQ,MAAM,EAAE;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC;QACF;QAEA,IAAI,CAAC,MAAM,GAAG;QAEd,8CAA8C;QAC9C,IAAI;QAEJ,8BAA8B;QAC9B,uCAA+E;YAC7E,YAAY;QACd,OAAO,IAAI,QAAQ,GAAG,CAAC,sBAAsB,EAAE;YAC7C,YAAY,QAAQ,GAAG,CAAC,sBAAsB;QAChD,OAAO;YACL,+BAA+B;YAC/B,YAAY;QACd;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gBAAgB,6EAA2D;QAEvF,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;gBAC1B,YAAY;oBAAC;iBAAU;gBACvB,iBAAiB;gBACjB,UAAU;gBACV,SAAS;gBACT,aAAa;gBACb,cAAc;gBACd,sBAAsB;gBACtB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,+BAA+B;YAC/B,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE;QACnB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,GAAG,CAAC,yCAAyC,IAAI,CAAC,MAAM,CAAC,EAAE;YACnE,IAAI,CAAC,WAAW,GAAG;YAEnB,oBAAoB;YACpB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,QAAQ,GAAG,CAAC,4BAA4B,IAAI,CAAC,MAAM;gBACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;YACtC;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC/B,QAAQ,KAAK,CAAC,uBAAuB;YACrC,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,6CAA6C;YAC7C,IAAI,UAAU,QAAQ,CAAC,cAAc;gBACnC,QAAQ,KAAK,CAAC;gBACd,QAAQ,KAAK,CAAC;YAChB;YAEA,4CAA4C;YAC5C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;gBACvC,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG;oBAAC;iBAAU;YAC9C;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;YAC3B,QAAQ,GAAG,CAAC,wBAAwB,eAAe;QACrD;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC;YACjC,QAAQ,KAAK,CAAC,yBAAyB;QACzC;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;YAC3B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG;QACrB;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,CAAC;YAChC,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,SAAS;YACX;QACF;QAEA,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC;YAClC,QAAQ,GAAG,CAAC,qCAAqC;QACnD;QAEA,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC;YAC9B,QAAQ,KAAK,CAAC,kBAAkB;QAClC;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,WACjC,SAAS;oBAAE,MAAM;oBAAW;gBAAQ;QAExC;QAEA,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,WACjC,SAAS;oBAAE,MAAM;oBAAU;gBAAO;QAEtC;QAEA,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,WACjC,SAAS;oBAAE,MAAM;oBAAW;gBAAO;QAEvC;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS;QACpD;QAEA,6BAA6B;QAC7B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YAC7B,QAAQ,GAAG,CAAC,iBAAiB;QAC/B;IACF;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IAEA,YAAY,UAAU,EAAE,OAAO,EAAE,cAAc,MAAM,EAAE;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3C,QAAQ,KAAK,CAAC,4BAA4B;gBAAE,QAAQ,IAAI,CAAC,MAAM;gBAAE;gBAAY;YAAQ;YACrF,OAAO;QACT;QAEA,MAAM,cAAc;YAClB,UAAU,IAAI,CAAC,MAAM;YACrB;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC,uBAAuB;QACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe;QAEhC,OAAO;IACT;IAEA,oBAAoB,UAAU,EAAE,QAAQ,EAAE;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;YACzB,UAAU,IAAI,CAAC,MAAM;YACrB;YACA;QACF;IACF;IAEA,WAAW,MAAM,EAAE,SAAS,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;YAC7B;YACA;YACA,QAAQ,IAAI,CAAC,MAAM;QACrB;IACF;IAEA,sBAAsB;IACtB,UAAU,QAAQ,EAAE;QAClB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAE3B,8BAA8B;QAC9B,OAAO;YACL,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;QACpE;IACF;IAEA,eAAe,QAAQ,EAAE;QACvB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAEhC,OAAO;YACL,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;QAC9E;IACF;IAEA,SAAS,QAAQ,EAAE;QACjB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAE1B,OAAO;YACL,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;QAClE;IACF;IAEA,wBAAwB;IACxB,sBAAsB;QACpB,OAAO;YACL,aAAa,IAAI,CAAC,WAAW;YAC7B,QAAQ,IAAI,CAAC,MAAM;YACnB,UAAU,IAAI,CAAC,MAAM,EAAE;QACzB;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB,IAAI;uCAEX", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/serverCheck.js"], "sourcesContent": ["// Server connectivity check utility\nexport const checkServerConnection = async (serverUrl) => {\n    try {\n        console.log('🔍 Checking server connection to:', serverUrl);\n        \n        const response = await fetch(serverUrl + '/health', {\n            method: 'GET',\n            mode: 'cors',\n            credentials: 'include',\n            headers: {\n                'Content-Type': 'application/json',\n            },\n        });\n        \n        if (response.ok) {\n            console.log('✅ Server is reachable');\n            return true;\n        } else {\n            console.warn('⚠️ Server responded with status:', response.status);\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Server connection failed:', error.message);\n        \n        if (serverUrl.includes('localhost')) {\n            console.error('🚨 Backend server is not running on localhost:5000');\n            console.error('💡 Please start your backend server:');\n            console.error('   1. Navigate to your backend directory');\n            console.error('   2. Run: npm start or node server.js');\n            console.error('   3. Make sure it\\'s running on port 5000');\n        }\n        \n        return false;\n    }\n};\n\nexport const getServerUrl = () => {\n    // Check if we're on localhost (client-side)\n    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {\n        return 'http://localhost:5000';\n    } else if (process.env.NEXT_PUBLIC_SERVER_URL) {\n        return process.env.NEXT_PUBLIC_SERVER_URL;\n    } else if (process.env.REACT_APP_API_URL) {\n        return process.env.REACT_APP_API_URL;\n    } else {\n        // Default to production server\n        return 'https://nextalk-u0y1.onrender.com';\n    }\n};\n\nexport const debugServerConnection = async () => {\n    const serverUrl = getServerUrl();\n    console.log('🌐 Current environment:', process.env.NODE_ENV);\n    console.log('🏠 Current hostname:', typeof window !== 'undefined' ? window.location.hostname : 'server');\n    console.log('🔗 Server URL:', serverUrl);\n    \n    const isConnected = await checkServerConnection(serverUrl);\n    \n    if (!isConnected && serverUrl.includes('localhost')) {\n        console.log('📋 Localhost troubleshooting checklist:');\n        console.log('   ✓ Is your backend server running?');\n        console.log('   ✓ Is it running on port 5000?');\n        console.log('   ✓ Can you access http://localhost:5000 in browser?');\n        console.log('   ✓ Are there any CORS issues?');\n        console.log('   ✓ Check backend console for errors');\n    }\n    \n    return isConnected;\n};\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;AAC7B,MAAM,wBAAwB,OAAO;IACxC,IAAI;QACA,QAAQ,GAAG,CAAC,qCAAqC;QAEjD,MAAM,WAAW,MAAM,MAAM,YAAY,WAAW;YAChD,QAAQ;YACR,MAAM;YACN,aAAa;YACb,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,IAAI,SAAS,EAAE,EAAE;YACb,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,OAAO;YACH,QAAQ,IAAI,CAAC,oCAAoC,SAAS,MAAM;YAChE,OAAO;QACX;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,+BAA+B,MAAM,OAAO;QAE1D,IAAI,UAAU,QAAQ,CAAC,cAAc;YACjC,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;QAClB;QAEA,OAAO;IACX;AACJ;AAEO,MAAM,eAAe;IACxB,4CAA4C;IAC5C,uCAA+E;;IAE/E,OAAO,IAAI,QAAQ,GAAG,CAAC,sBAAsB,EAAE;QAC3C,OAAO,QAAQ,GAAG,CAAC,sBAAsB;IAC7C,OAAO,IAAI,QAAQ,GAAG,CAAC,iBAAiB,EAAE;QACtC,OAAO,QAAQ,GAAG,CAAC,iBAAiB;IACxC,OAAO;QACH,+BAA+B;QAC/B,OAAO;IACX;AACJ;AAEO,MAAM,wBAAwB;IACjC,MAAM,YAAY;IAClB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,wBAAwB,6EAA2D;IAC/F,QAAQ,GAAG,CAAC,kBAAkB;IAE9B,MAAM,cAAc,MAAM,sBAAsB;IAEhD,IAAI,CAAC,eAAe,UAAU,QAAQ,CAAC,cAAc;QACjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IAChB;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Dashboard/Messages.js"], "sourcesContent": ["import { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport axios from '../../utils/axiosConfig';\r\nimport Image from \"next/image\";\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport DashboardLayout from '../Components/DashboardLayout';\r\nimport { useRouter } from 'next/router';\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport socketService from '../../utils/socket';\r\nimport { debugServerConnection } from '../../utils/serverCheck';\r\n\r\nexport default function Messages() {\r\n    const [users, setUsers] = useState([]);\r\n    const [sessionUser, setSessionUser] = useState(null);\r\n    const [following, setFollowing] = useState(new Set());\r\n    const [accepted, setAccepted] = useState(new Set());\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [selectedChat, setSelectedChat] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [displayCount, setDisplayCount] = useState(6);\r\n    const [visibleUsers, setVisibleUsers] = useState([]);\r\n    const [filteredFollowers, setFilteredFollowers] = useState([]);\r\n    const [profile, setProfile] = useState({\r\n        username: 'user123',\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        bio: 'No bio yet.',\r\n        avatar: predefine,\r\n        posts: 15,\r\n        followersCount: 250,\r\n        followingCount: 180,\r\n    });\r\n    const { theme } = useTheme();\r\n    const [showOnlineOnly, setShowOnlineOnly] = useState(false);\r\n    const router = useRouter();\r\n\r\n    // Mock last message data and reactions\r\n    const [lastMessages, setLastMessages] = useState({});\r\n    const [reactions, setReactions] = useState({});\r\n    const [messages, setMessages] = useState([\r\n        { id: 1, sender: \"You\", text: \"Hey, how's it going?\", timestamp: \"10:30 AM\" },\r\n        { id: 2, sender: \"Friend\", text: \"Pretty good, thanks! You?\", timestamp: \"10:32 AM\" },\r\n        { id: 3, sender: \"You\", text: \"Hey, how's it going?\", timestamp: \"10:42 AM\" },\r\n        { id: 4, sender: \"Friend\", text: \"Pretty good, thanks! You?\", timestamp: \"10:52 AM\" },\r\n    ]);\r\n    const [newMessage, setNewMessage] = useState(\"\");\r\n    const messagesEndRef = useRef(null);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n    const [selectedUser, setSelectedUser] = useState(null);\r\n    const [chatMessages, setChatMessages] = useState({});\r\n    const [onlineUsers, setOnlineUsers] = useState(new Set());\r\n    const [typingUsers, setTypingUsers] = useState(new Set());\r\n    const [isTyping, setIsTyping] = useState(false);\r\n    const [unreadCounts, setUnreadCounts] = useState({});\r\n    const [deliveredMessages, setDeliveredMessages] = useState(new Set());\r\n    const [showMobileModal, setShowMobileModal] = useState(false);\r\n    const [chatSizes, setChatSizes] = useState({});\r\n    const [deletedChats, setDeletedChats] = useState(new Set());\r\n    const [userLastSeen, setUserLastSeen] = useState({});\r\n    const typingTimeoutRef = useRef(null);\r\n\r\n    // Chat size and deletion functions\r\n    const checkChatSize = async (userId1, userId2) => {\r\n        try {\r\n            const response = await axios.get(`/chat/${userId1}/${userId2}`);\r\n            const chatData = response.data;\r\n\r\n            setChatSizes(prev => ({\r\n                ...prev,\r\n                [`${userId1}_${userId2}`]: {\r\n                    sizeInBytes: chatData.sizeInBytes,\r\n                    sizeFormatted: chatData.sizeFormatted,\r\n                    exceedsLimit: chatData.exceedsLimit,\r\n                    chatId: chatData.chatId\r\n                }\r\n            }));\r\n\r\n            if (chatData.isDeleted) {\r\n                setDeletedChats(prev => new Set([...prev, `${userId1}_${userId2}`]));\r\n            }\r\n\r\n            return chatData;\r\n        } catch (error) {\r\n            console.error('Error checking chat size:', error);\r\n            return null;\r\n        }\r\n    };\r\n\r\n    const deleteChat = async (chatId, userId1, userId2) => {\r\n        try {\r\n            await axios.delete(`/chat/${chatId}`);\r\n\r\n            // Mark chat as deleted\r\n            setDeletedChats(prev => new Set([...prev, `${userId1}_${userId2}`]));\r\n\r\n            // Clear local messages\r\n            setChatMessages(prev => ({\r\n                ...prev,\r\n                [`${userId1}_${userId2}`]: []\r\n            }));\r\n\r\n            // Clear local storage\r\n            localStorage.removeItem(`chat_${userId1}_${userId2}`);\r\n\r\n            console.log('✅ Chat deleted successfully');\r\n            return true;\r\n        } catch (error) {\r\n            console.error('❌ Error deleting chat:', error);\r\n            return false;\r\n        }\r\n    };\r\n\r\n    const restoreChat = async (chatId, userId1, userId2) => {\r\n        try {\r\n            await axios.post(`/chat/${chatId}/restore`);\r\n\r\n            // Remove from deleted chats\r\n            setDeletedChats(prev => {\r\n                const newSet = new Set(prev);\r\n                newSet.delete(`${userId1}_${userId2}`);\r\n                return newSet;\r\n            });\r\n\r\n            console.log('✅ Chat restored successfully');\r\n            return true;\r\n        } catch (error) {\r\n            console.error('❌ Error restoring chat:', error);\r\n            return false;\r\n        }\r\n    };\r\n\r\n    // Format last seen time (12-hour format)\r\n    const formatLastSeen = (lastSeen, isOnline) => {\r\n        if (isOnline) return 'Online';\r\n\r\n        const now = new Date();\r\n        const lastSeenDate = new Date(lastSeen);\r\n        const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));\r\n\r\n        if (diffInMinutes < 1) return 'Just now';\r\n        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\r\n\r\n        const diffInHours = Math.floor(diffInMinutes / 60);\r\n        if (diffInHours < 24) return `${diffInHours}h ago`;\r\n\r\n        // Format as 12-hour time with AM/PM\r\n        const options = {\r\n            hour: 'numeric',\r\n            minute: 'numeric',\r\n            hour12: true\r\n        };\r\n\r\n        const diffInDays = Math.floor(diffInHours / 24);\r\n        if (diffInDays === 1) {\r\n            return `Yesterday ${lastSeenDate.toLocaleTimeString('en-US', options)}`;\r\n        } else if (diffInDays < 7) {\r\n            return `${diffInDays}d ago`;\r\n        } else {\r\n            return lastSeenDate.toLocaleDateString('en-US', {\r\n                month: 'short',\r\n                day: 'numeric',\r\n                hour: 'numeric',\r\n                minute: 'numeric',\r\n                hour12: true\r\n            });\r\n        }\r\n    };\r\n\r\n    // Enhanced Local Storage utility functions\r\n    const getChatHistory = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return [];\r\n            const chatKey = `chat_${sessionUserId}_${userId}`;\r\n            const stored = localStorage.getItem(chatKey);\r\n            return stored ? JSON.parse(stored) : [];\r\n        } catch (error) {\r\n            console.error('Error loading chat history:', error);\r\n            return [];\r\n        }\r\n    };\r\n\r\n    const saveChatHistory = (userId, messages) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const chatKey = `chat_${sessionUserId}_${userId}`;\r\n            localStorage.setItem(chatKey, JSON.stringify(messages));\r\n            console.log('💾 Chat history saved for user:', userId);\r\n        } catch (error) {\r\n            console.error('Error saving chat history:', error);\r\n        }\r\n    };\r\n\r\n    const getUnreadCount = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return 0;\r\n            const unreadKey = `unread_${sessionUserId}_${userId}`;\r\n            const stored = localStorage.getItem(unreadKey);\r\n            return stored ? parseInt(stored) : 0;\r\n        } catch (error) {\r\n            console.error('Error loading unread count:', error);\r\n            return 0;\r\n        }\r\n    };\r\n\r\n    const saveUnreadCount = (userId, count) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const unreadKey = `unread_${sessionUserId}_${userId}`;\r\n            localStorage.setItem(unreadKey, count.toString());\r\n            setUnreadCounts(prev => ({\r\n                ...prev,\r\n                [userId]: count\r\n            }));\r\n        } catch (error) {\r\n            console.error('Error saving unread count:', error);\r\n        }\r\n    };\r\n\r\n    const getLastMessage = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return '';\r\n            const lastMsgKey = `lastmsg_${sessionUserId}_${userId}`;\r\n            return localStorage.getItem(lastMsgKey) || '';\r\n        } catch (error) {\r\n            console.error('Error loading last message:', error);\r\n            return '';\r\n        }\r\n    };\r\n\r\n    const saveLastMessage = (userId, message) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const lastMsgKey = `lastmsg_${sessionUserId}_${userId}`;\r\n            const truncatedMessage = message.length > 30 ? message.substring(0, 30) + '...' : message;\r\n            localStorage.setItem(lastMsgKey, truncatedMessage);\r\n            setLastMessages(prev => ({\r\n                ...prev,\r\n                [userId]: truncatedMessage\r\n            }));\r\n        } catch (error) {\r\n            console.error('Error saving last message:', error);\r\n        }\r\n    };\r\n\r\n    // Handle back to chat list on mobile\r\n    const handleBackToList = () => {\r\n        console.log('🔙 Going back to chat list');\r\n        setSelectedChat(null);\r\n        setSelectedUser(null);\r\n        setShowMobileModal(false);\r\n        // Update URL only on desktop\r\n        if (window.innerWidth >= 1024) {\r\n            router.replace('/Dashboard/Messages', undefined, { shallow: true });\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem('user'));\r\n            const sessionId = storedUser?.user?.id;\r\n\r\n            if (!sessionId) {\r\n                setError('No session found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            try {\r\n                // Check server connection first\r\n                console.log('🔍 Checking server connection...');\r\n                await debugServerConnection();\r\n\r\n                // Fetch all users\r\n                console.log('📡 Fetching users...');\r\n                const response = await axios.post('/displayusersProfile');\r\n\r\n                const personally = response.data;\r\n                const filteredUsers = personally.filter(user => user._id !== sessionId);\r\n                const sessionUser = personally.find(user => user._id === sessionId);\r\n                setSessionUser(sessionUser);\r\n\r\n                // Fetch follow status\r\n                const followRes = await axios.get(`/follow-status/${sessionId}`);\r\n                const followData = followRes.data;\r\n                setFollowing(new Set(followData.following));\r\n                setAccepted(new Set(followData.accepted));\r\n\r\n                // Mock last messages and reactions\r\n                const mockLastMessages = filteredUsers.reduce((acc, user) => ({\r\n                    ...acc,\r\n                    [user._id]: user.lastMessage || `Hey, what's up? ${new Date().toLocaleTimeString()}`,\r\n                }), {});\r\n                const mockReactions = filteredUsers.reduce((acc, user) => ({\r\n                    ...acc,\r\n                    [user._id]: user.reaction || (Math.random() > 0.5 ? '❤️' : null),\r\n                }), {});\r\n                setLastMessages(mockLastMessages);\r\n                setReactions(mockReactions);\r\n\r\n                setUsers(filteredUsers);\r\n                setLoading(false);\r\n            } catch (err) {\r\n                console.error('❌ Error fetching data:', err);\r\n\r\n                if (err.message === 'Network Error' || err.code === 'ECONNREFUSED') {\r\n                    setError('Cannot connect to server. Please make sure your backend is running on http://localhost:5000');\r\n                    console.error('🚨 Backend server connection failed!');\r\n                    console.error('💡 Make sure to start your backend server:');\r\n                    console.error('   1. Navigate to your backend directory');\r\n                    console.error('   2. Run: npm start or node server.js');\r\n                    console.error('   3. Verify it\\'s running on port 5000');\r\n                } else {\r\n                    setError('Failed to load data: ' + err.message);\r\n                }\r\n\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n    // Load chat history and unread counts when users are loaded\r\n    useEffect(() => {\r\n        if (users.length > 0 && sessionUserId) {\r\n            console.log('📚 Loading chat history for all users...');\r\n            const loadedChats = {};\r\n            const loadedUnreadCounts = {};\r\n            const loadedLastMessages = {};\r\n\r\n            users.forEach(user => {\r\n                if (accepted.has(user._id)) {\r\n                    // Load chat history\r\n                    const history = getChatHistory(user._id);\r\n                    if (history.length > 0) {\r\n                        loadedChats[user._id] = history;\r\n                        console.log(`📖 Loaded ${history.length} messages for user:`, user.name);\r\n                    }\r\n\r\n                    // Load unread count\r\n                    const unreadCount = getUnreadCount(user._id);\r\n                    if (unreadCount > 0) {\r\n                        loadedUnreadCounts[user._id] = unreadCount;\r\n                    }\r\n\r\n                    // Load last message\r\n                    const lastMsg = getLastMessage(user._id);\r\n                    if (lastMsg) {\r\n                        loadedLastMessages[user._id] = lastMsg;\r\n                    }\r\n\r\n                    // Check chat size for each user\r\n                    checkChatSize(sessionUserId, user._id);\r\n                }\r\n            });\r\n\r\n            setChatMessages(loadedChats);\r\n            setUnreadCounts(loadedUnreadCounts);\r\n            setLastMessages(loadedLastMessages);\r\n            console.log('✅ All chat data loaded from cache');\r\n        }\r\n    }, [users, sessionUserId, accepted]);\r\n\r\n    // Socket.IO connection and real-time functionality\r\n    useEffect(() => {\r\n        if (sessionUserId) {\r\n            // Connect to Socket.IO\r\n            socketService.connect(sessionUserId);\r\n\r\n            // Listen for incoming messages\r\n            const unsubscribeMessages = socketService.onMessage((data) => {\r\n                console.log('📨 Received message:', data);\r\n                console.log('🔍 Current sessionUserId:', sessionUserId);\r\n                console.log('🔍 Current selectedChat:', selectedChat);\r\n                const { senderId, receiverId, message, timestamp, messageId } = data;\r\n\r\n                // Add message to chat if it's for current user or current chat\r\n                if (receiverId === sessionUserId || senderId === sessionUserId) {\r\n                    console.log('✅ Message is for current user, adding to chat');\r\n                    const chatPartnerId = receiverId === sessionUserId ? senderId : receiverId;\r\n                    const isIncomingMessage = receiverId === sessionUserId;\r\n\r\n                    const newMessage = {\r\n                        id: messageId,\r\n                        sender: senderId === sessionUserId ? \"You\" : \"Friend\",\r\n                        text: message,\r\n                        timestamp: new Date(timestamp).toLocaleTimeString([], {\r\n                            hour: '2-digit',\r\n                            minute: '2-digit',\r\n                            hour12: true\r\n                        }),\r\n                        delivered: true,\r\n                        read: false\r\n                    };\r\n\r\n                    // Update chat messages\r\n                    setChatMessages(prev => {\r\n                        const updatedMessages = [...(prev[chatPartnerId] || []), newMessage];\r\n                        // Save to local storage\r\n                        saveChatHistory(chatPartnerId, updatedMessages);\r\n                        return {\r\n                            ...prev,\r\n                            [chatPartnerId]: updatedMessages\r\n                        };\r\n                    });\r\n\r\n                    // Update last message\r\n                    saveLastMessage(chatPartnerId, message);\r\n\r\n                    // Handle unread count for incoming messages\r\n                    if (isIncomingMessage) {\r\n                        const currentUnread = getUnreadCount(chatPartnerId);\r\n                        const newUnreadCount = currentUnread + 1;\r\n                        saveUnreadCount(chatPartnerId, newUnreadCount);\r\n                        console.log(`📬 Unread count for ${chatPartnerId}: ${newUnreadCount}`);\r\n                    }\r\n                }\r\n            });\r\n\r\n            // Listen for online status updates\r\n            const unsubscribeOnlineStatus = socketService.onOnlineStatus((data) => {\r\n                console.log('👥 Online status update:', data);\r\n                if (data.type === 'initial') {\r\n                    console.log('📋 Setting initial online users:', data.userIds);\r\n                    setOnlineUsers(new Set(data.userIds));\r\n                } else if (data.type === 'online') {\r\n                    console.log('🟢 User came online:', data.userId);\r\n                    setOnlineUsers(prev => new Set([...prev, data.userId]));\r\n                } else if (data.type === 'offline') {\r\n                    console.log('🔴 User went offline:', data.userId);\r\n                    setOnlineUsers(prev => {\r\n                        const newSet = new Set(prev);\r\n                        newSet.delete(data.userId);\r\n                        return newSet;\r\n                    });\r\n                }\r\n            });\r\n\r\n            // Listen for typing indicators\r\n            const unsubscribeTyping = socketService.onTyping((data) => {\r\n                console.log('⌨️ Typing indicator:', data);\r\n                const { userId, isTyping } = data;\r\n                setTypingUsers(prev => {\r\n                    const newSet = new Set(prev);\r\n                    if (isTyping) {\r\n                        console.log('⌨️ User started typing:', userId);\r\n                        newSet.add(userId);\r\n                    } else {\r\n                        console.log('⌨️ User stopped typing:', userId);\r\n                        newSet.delete(userId);\r\n                    }\r\n                    return newSet;\r\n                });\r\n\r\n                // Clear typing indicator after 3 seconds\r\n                if (isTyping) {\r\n                    setTimeout(() => {\r\n                        setTypingUsers(prev => {\r\n                            const newSet = new Set(prev);\r\n                            newSet.delete(userId);\r\n                            return newSet;\r\n                        });\r\n                    }, 3000);\r\n                }\r\n            });\r\n\r\n            // Cleanup on unmount\r\n            return () => {\r\n                unsubscribeMessages();\r\n                unsubscribeOnlineStatus();\r\n                unsubscribeTyping();\r\n                socketService.disconnect();\r\n            };\r\n        }\r\n    }, [sessionUserId]);\r\n\r\n    // Handle direct URL access\r\n    useEffect(() => {\r\n        if (router.isReady && users.length > 0 && !selectedChat && sessionUserId) {\r\n            const { userId } = router.query;\r\n            if (userId && userId !== 'undefined' && userId !== 'null') {\r\n                const userToSelect = users.find(u => u._id === userId);\r\n                if (userToSelect && accepted.has(userToSelect._id)) {\r\n                    setSelectedChat(userToSelect._id);\r\n                    setSelectedUser(userToSelect);\r\n                    // Load chat history for this user\r\n                    const history = getChatHistory(userToSelect._id);\r\n                    setChatMessages(prev => ({\r\n                        ...prev,\r\n                        [userToSelect._id]: history\r\n                    }));\r\n                }\r\n            }\r\n        }\r\n    }, [router.isReady, users, accepted, selectedChat, sessionUserId]);\r\n\r\n    // Auto-scroll to bottom when new messages are added\r\n    useEffect(() => {\r\n        if (messagesEndRef.current) {\r\n            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [chatMessages, selectedChat]);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                // Check server connection first\r\n                console.log('🔍 Checking server connection for users...');\r\n                await debugServerConnection();\r\n\r\n                console.log('📡 Fetching users list...');\r\n                const response = await axios.post(\"/displayusersProfile\");\r\n\r\n                const allUsers = response.data.filter(user => user._id !== sessionId);\r\n                setUsers(allUsers);\r\n                setVisibleUsers(allUsers.slice(0, 6)); // first 6\r\n                setTimeout(() => setLoading(false), 1000); // optional fake delay\r\n            } catch (error) {\r\n                console.error(\"❌ Error fetching users:\", error);\r\n\r\n                if (error.message === 'Network Error' || error.code === 'ECONNREFUSED') {\r\n                    setError('Cannot connect to server. Please make sure your backend is running on http://localhost:5000');\r\n                    console.error('🚨 Backend server connection failed!');\r\n                } else {\r\n                    setError('Failed to load users: ' + error.message);\r\n                }\r\n\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    // Filter users based on search query and online status\r\n    const filteredUsers = users\r\n        .filter(user => accepted.has(user._id))\r\n        .filter(user => {\r\n            // Use searchTerm for consistency\r\n            const searchValue = searchTerm.toLowerCase();\r\n            return searchValue === '' ||\r\n                user.name.toLowerCase().includes(searchValue) ||\r\n                user.username.toLowerCase().includes(searchValue);\r\n        })\r\n        .filter(user =>\r\n            showOnlineOnly ? user.isOnline : true\r\n        );\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',\r\n                color: '#e2e8f0',\r\n                cardBg: 'rgba(255, 255, 255, 0.1)',\r\n                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',\r\n                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',\r\n                notificationBg: 'rgba(51, 65, 85, 0.9)',\r\n            };\r\n        }\r\n        return {\r\n            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',\r\n            color: '#1e293b',\r\n            cardBg: 'rgba(255, 255, 255, 0.8)',\r\n            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)',\r\n        };\r\n    };\r\n\r\n    const styles = getThemeStyles();\r\n\r\n    const getThemeStyless = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: '#1e293b',\r\n                color: '#e2e8f0',\r\n                inputBg: '#334155',\r\n                inputColor: '#e2e8f0',\r\n                messageBgYou: '#3b82f6',\r\n                messageBgFriend: '#4b5563'\r\n            };\r\n        }\r\n        // Default to light styles for 'homeback' or any other theme\r\n        return {\r\n            background: '#ffffff',\r\n            color: '#1e293b',\r\n            inputBg: '#f1f5f9',\r\n            inputColor: '#1e293b',\r\n            messageBgYou: '#3b82f6',\r\n            messageBgFriend: '#e5e7eb'\r\n        };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyless();\r\n\r\n    const handleSendMessage = (e) => {\r\n        e.preventDefault();\r\n        console.log('🚀 Attempting to send message:', {\r\n            newMessage: newMessage.trim(),\r\n            selectedChat,\r\n            sessionUserId,\r\n            socketConnected: socketService.getConnectionStatus()\r\n        });\r\n\r\n        if (newMessage.trim() && selectedChat && sessionUserId) {\r\n            // Send message via Socket.IO for real-time delivery\r\n            console.log('📤 Sending message to:', selectedChat, 'from:', sessionUserId);\r\n            console.log('📤 Message content:', newMessage.trim());\r\n            const success = socketService.sendMessage(selectedChat, newMessage.trim());\r\n            console.log('📤 Socket send result:', success);\r\n            console.log('📤 Socket connection status:', socketService.getConnectionStatus());\r\n\r\n            if (success) {\r\n                // Add message to local state immediately for instant UI update\r\n                const newMsg = {\r\n                    id: Date.now(),\r\n                    sender: \"You\",\r\n                    text: newMessage.trim(),\r\n                    timestamp: new Date().toLocaleTimeString([], {\r\n                        hour: '2-digit',\r\n                        minute: '2-digit',\r\n                        hour12: true\r\n                    }),\r\n                    delivered: onlineUsers.has(selectedChat), // Mark as delivered if user is online\r\n                    read: false\r\n                };\r\n\r\n                const currentMessages = chatMessages[selectedChat] || [];\r\n                const updatedMessages = [...currentMessages, newMsg];\r\n\r\n                // Update chat messages state and save to local storage\r\n                setChatMessages(prev => {\r\n                    const updated = {\r\n                        ...prev,\r\n                        [selectedChat]: updatedMessages\r\n                    };\r\n                    // Save to local storage\r\n                    saveChatHistory(selectedChat, updatedMessages);\r\n                    return updated;\r\n                });\r\n\r\n                // Update last message\r\n                saveLastMessage(selectedChat, newMsg.text);\r\n\r\n                // Clear input\r\n                setNewMessage(\"\");\r\n\r\n                // Stop typing indicator\r\n                if (isTyping) {\r\n                    setIsTyping(false);\r\n                    socketService.sendTypingIndicator(selectedChat, false);\r\n                }\r\n            } else {\r\n                console.error('Failed to send message - Socket not connected');\r\n            }\r\n        }\r\n    };\r\n\r\n    // Handle typing indicator\r\n    const handleTyping = useCallback((value) => {\r\n        setNewMessage(value);\r\n\r\n        if (selectedChat && sessionUserId) {\r\n            if (value.trim() && !isTyping) {\r\n                setIsTyping(true);\r\n                socketService.sendTypingIndicator(selectedChat, true);\r\n            } else if (!value.trim() && isTyping) {\r\n                setIsTyping(false);\r\n                socketService.sendTypingIndicator(selectedChat, false);\r\n            }\r\n\r\n            // Clear typing timeout\r\n            if (typingTimeoutRef.current) {\r\n                clearTimeout(typingTimeoutRef.current);\r\n            }\r\n\r\n            // Set new timeout to stop typing indicator\r\n            typingTimeoutRef.current = setTimeout(() => {\r\n                if (isTyping) {\r\n                    setIsTyping(false);\r\n                    socketService.sendTypingIndicator(selectedChat, false);\r\n                }\r\n            }, 2000);\r\n        }\r\n    }, [selectedChat, sessionUserId, isTyping]);\r\n\r\n    useEffect(() => {\r\n        if (!profile || !Array.isArray(profile.followers)) return;\r\n\r\n        // Extract follower user IDs from the populated followers array\r\n        const followersArray = profile.followers.map(f => f._id?.toString());\r\n\r\n        // Filter only users who are followers\r\n        const followedUsers = users.filter(user =>\r\n            followersArray.includes(user._id?.toString())\r\n        );\r\n\r\n        // Filter for search\r\n        const filtered = followedUsers.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            user.username.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setFilteredFollowers(filtered); // useful for Load More check\r\n\r\n        // Control visible users based on search or default count\r\n        if (searchTerm.trim() === '') {\r\n            setVisibleUsers(followedUsers.slice(0, displayCount));\r\n        } else {\r\n            setVisibleUsers(filtered.slice(0, displayCount));\r\n        }\r\n\r\n    }, [searchTerm, users, displayCount, profile]);\r\n\r\n\r\n    const handleLoadMore = () => {\r\n        const prevCount = displayCount;\r\n        const newCount = prevCount + 6;\r\n        setDisplayCount(newCount);\r\n\r\n        // Scroll to the previous 6th user (after DOM update)\r\n        setTimeout(() => {\r\n            const userElems = document.querySelectorAll(\".user-result\");\r\n            if (userElems[prevCount]) {\r\n                userElems[prevCount].scrollIntoView({\r\n                    behavior: \"smooth\",\r\n                    block: \"start\"\r\n                });\r\n            }\r\n        }, 100); // wait a moment for new DOM elements to render\r\n    };\r\n    return (\r\n        <DashboardLayout>\r\n            {loading ? (\r\n                <div className=\"custom-loader-overlay\">\r\n                    <svg viewBox=\"0 0 100 100\">\r\n                        <g fill=\"none\" stroke=\"#fff\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"6\">\r\n                            {/* left line */}\r\n                            <path d=\"M 21 40 V 59\">\r\n                                <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 21 59; 180 21 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* right line */}\r\n                            <path d=\"M 79 40 V 59\">\r\n                                <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 79 59; -180 79 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* top line */}\r\n                            <path d=\"M 50 21 V 40\">\r\n                                <animate attributeName=\"d\" values=\"M 50 21 V 40; M 50 59 V 40\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* bottom line */}\r\n                            <path d=\"M 50 60 V 79\">\r\n                                <animate attributeName=\"d\" values=\"M 50 60 V 79; M 50 98 V 79\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* top box */}\r\n                            <path d=\"M 50 21 L 79 40 L 50 60 L 21 40 Z\">\r\n                                <animate attributeName=\"stroke\" values=\"rgba(255,255,255,1); rgba(100,100,100,0)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* mid box */}\r\n                            <path d=\"M 50 40 L 79 59 L 50 79 L 21 59 Z\" />\r\n                            {/* bottom box */}\r\n                            <path d=\"M 50 59 L 79 78 L 50 98 L 21 78 Z\">\r\n                                <animate attributeName=\"stroke\" values=\"rgba(100,100,100,0); rgba(255,255,255,1)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            <animateTransform attributeName=\"transform\" type=\"translate\" values=\"0 0; 0 -19\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </g>\r\n                    </svg>\r\n                </div>\r\n            ) : error ? (\r\n                <div className=\"error\">{error}</div>\r\n            ) : filteredUsers.length === 0 ? (\r\n                <div className=\"chat-container\" style={{ background: styles.background, color: styles.color }}>\r\n                    {/* Left Sidebar - Empty State */}\r\n                    <div className=\"chat-list\">\r\n                        {/* Session User Name - Hidden on mobile */}\r\n                        <h2 className=\"chat-title d-none d-md-block\">\r\n                            {sessionUser?.name || sessionUser?.username || 'User'}\r\n                        </h2>\r\n\r\n                        {/* Search Input - Full width with Bootstrap 5 */}\r\n                        <div className=\"mb-3 px-3\">\r\n                            <div className=\"position-relative\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    className=\"form-control ps-5\"\r\n                                    placeholder=\"Search messages...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    style={{\r\n                                        background: currentThemeStyles.inputBg,\r\n                                        color: currentThemeStyles.inputColor,\r\n                                        border: '1px solid rgba(0,0,0,0.1)',\r\n                                        borderRadius: '25px',\r\n                                        padding: '12px 20px 12px 45px'\r\n                                    }}\r\n                                />\r\n                                <i\r\n                                    className=\"bi bi-search position-absolute\"\r\n                                    style={{\r\n                                        left: '15px',\r\n                                        top: '50%',\r\n                                        transform: 'translateY(-50%)',\r\n                                        color: currentThemeStyles.inputColor,\r\n                                        opacity: 0.6\r\n                                    }}\r\n                                ></i>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* No Friends Message */}\r\n                        <div className=\"text-center py-5\">\r\n                            <i className=\"bi bi-chat-dots\" style={{ fontSize: '3rem', opacity: 0.5 }}></i>\r\n                            <h4 className=\"mt-3\">No friends to message</h4>\r\n                            <p className=\"text-muted\">Add friends to start chatting!</p>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Right Panel - Default Instructions */}\r\n                    <div className=\"chat-panel d-none d-lg-flex\">\r\n                        <div className=\"instructions text-center\">\r\n                            <div className=\"instruction-content\">\r\n                                <i className=\"bi bi-chat-heart\" style={{ fontSize: '4rem', opacity: 0.3, marginBottom: '20px' }}></i>\r\n                                <h3 className=\"instruction-title\">Your messages</h3>\r\n                                <p className=\"instruction-text\">Send a message to start a chat.</p>\r\n                                <button\r\n                                    className=\"btn btn-primary\"\r\n                                    onClick={() => {\r\n                                        // You can add functionality to open friend list or search\r\n                                        console.log('Add friends button clicked');\r\n                                    }}\r\n                                    style={{\r\n                                        borderRadius: '25px',\r\n                                        padding: '10px 30px',\r\n                                        fontWeight: '500'\r\n                                    }}\r\n                                >\r\n                                    <i className=\"bi bi-person-plus me-2\"></i>\r\n                                    Add Friends\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <div className=\"chat-container\" style={{ background: styles.background, color: styles.color }}>\r\n                    {/* Left Sidebar for Chat List */}\r\n                    <div className={`chat-list ${selectedChat ? 'chat-list-with-panel' : ''}`}>\r\n                        {/* Session User Name - Hidden on mobile */}\r\n                        <h2 className=\"chat-title d-none d-md-block\">\r\n                            {sessionUser?.name || sessionUser?.username || 'User'}\r\n                        </h2>\r\n\r\n                        {/* Search Input - Full width with Bootstrap 5 */}\r\n                        <div className=\"mb-3 px-3\">\r\n                            <div className=\"position-relative\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    placeholder=\"Search messages...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    className=\"form-control\"\r\n                                    style={{\r\n                                        borderRadius: '25px',\r\n                                        paddingLeft: '20px',\r\n                                        paddingRight: '50px'\r\n                                    }}\r\n                                />\r\n                                <span className=\"position-absolute\" style={{\r\n                                    right: '15px',\r\n                                    top: '50%',\r\n                                    transform: 'translateY(-50%)',\r\n                                    color: '#6c757d'\r\n                                }}>\r\n                                    <i className=\"bi bi-search\"></i>\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Messages Label */}\r\n                        <span className=\"fw-bold mb-2 d-block px-3\">Messages</span>\r\n                        {filteredUsers.map(user => (\r\n                            <div\r\n                                key={user._id}\r\n                                className=\"chat-item\"\r\n                                onClick={() => {\r\n                                    setSelectedChat(user._id);\r\n                                    setSelectedUser(user);\r\n\r\n                                    // Mark messages as read and clear unread count\r\n                                    saveUnreadCount(user._id, 0);\r\n\r\n                                    // Check if mobile view\r\n                                    const isMobile = window.innerWidth < 1024;\r\n\r\n                                    if (isMobile) {\r\n                                        // Open modal on mobile\r\n                                        setShowMobileModal(true);\r\n                                    } else {\r\n                                        // Update URL on desktop\r\n                                        router.replace(`/Dashboard/Messages?userId=${user._id}`, undefined, { shallow: true });\r\n                                    }\r\n\r\n                                    // Load chat history for this user\r\n                                    const history = getChatHistory(user._id);\r\n                                    if (history.length > 0) {\r\n                                        setChatMessages(prev => ({\r\n                                            ...prev,\r\n                                            [user._id]: history\r\n                                        }));\r\n                                        console.log(`📖 Loaded ${history.length} messages for chat with:`, user.name);\r\n                                    }\r\n                                }}\r\n                            >\r\n                                <div className=\"avatar-container\">\r\n                                    <Image\r\n                                        src={user.image || predefine}\r\n                                        alt={user.name}\r\n                                        width={80}\r\n                                        height={80}\r\n                                        className=\"avatar\"\r\n                                    />\r\n                                    <span\r\n                                        className={`status-indicator ${onlineUsers.has(user._id) ? 'online' : 'offline'}`}\r\n                                        title={onlineUsers.has(user._id) ? 'Online' : 'Offline'}\r\n                                    ></span>\r\n                                </div>\r\n                                <div className=\"chat-details\">\r\n                                    <div className=\"chat-header\">\r\n                                        <span className=\"user-name\">{user.name}</span>\r\n                                        <span className=\"timestamp\">\r\n                                            {onlineUsers.has(user._id)\r\n                                                ? 'Online'\r\n                                                : formatLastSeen(user.lastSeen || new Date(), false)\r\n                                            }\r\n                                        </span>\r\n                                    </div>\r\n                                    <p className=\"last-message\">\r\n                                        {typingUsers.has(user._id) ? (\r\n                                            <span className=\"typing-text\">\r\n                                                <span className=\"typing-indicator\">\r\n                                                    <span></span>\r\n                                                    <span></span>\r\n                                                    <span></span>\r\n                                                </span>\r\n                                                typing...\r\n                                            </span>\r\n                                        ) : (\r\n                                            lastMessages[user._id] || 'No messages yet'\r\n                                        )}\r\n                                    </p>\r\n                                </div>\r\n\r\n                                {/* Unread Message Count Badge */}\r\n                                {unreadCounts[user._id] > 0 && (\r\n                                    <div className=\"unread-badge\">\r\n                                        {unreadCounts[user._id] > 4 ? '4+' : unreadCounts[user._id]}\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Right Panel for Chat or Instructions - Desktop Only */}\r\n                    <div className=\"chat-panel d-none d-lg-flex\">\r\n                        {selectedChat ? (\r\n                            <div className=\"chat-window\">\r\n                                {/* Chat Header with Back Button for Mobile */}\r\n                                <div className=\"chat-header d-flex align-items-center justify-content-between w-100\">\r\n                                    <div className=\"d-flex align-items-center gap-3\">\r\n                                        <Image\r\n                                            src={filteredUsers.find(u => u._id === selectedChat)?.image || predefine}\r\n                                            alt={filteredUsers.find(u => u._id === selectedChat)?.name}\r\n                                            width={50}\r\n                                            height={50}\r\n                                            className=\"avatar\"\r\n                                            style={{ borderRadius: '50%' }}\r\n                                        />\r\n                                        <div>\r\n                                            <h3 className=\"mb-0\" style={{ fontSize: '1.2rem' }}>\r\n                                                {filteredUsers.find(u => u._id === selectedChat)?.name}\r\n                                            </h3>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Back Button - Right side on mobile */}\r\n                                    <button\r\n                                        type=\"button\"\r\n                                        className=\"btn d-md-none\"\r\n                                        onClick={() => {\r\n                                            console.log('🔙 Back button clicked');\r\n                                            setSelectedChat(null);\r\n                                            setSelectedUser(null);\r\n                                        }}\r\n                                        style={{\r\n                                            fontSize: '1.5rem',\r\n                                            color: getThemeStyles().color,\r\n                                            background: 'none',\r\n                                            border: 'none',\r\n                                            padding: '8px',\r\n                                            minWidth: '44px',\r\n                                            height: '44px',\r\n                                            display: 'flex',\r\n                                            alignItems: 'center',\r\n                                            justifyContent: 'center'\r\n                                        }}\r\n                                    >\r\n                                        <i className=\"bi bi-x-lg\"></i>\r\n                                    </button>\r\n                                </div>\r\n                                <div\r\n                                    className=\"\"\r\n                                    style={{\r\n                                        background: currentThemeStyles.background,\r\n                                        color: currentThemeStyles.color\r\n                                    }}\r\n                                >\r\n                                    {/* Chat Messages Container with Fixed Height and Scrolling */}\r\n                                    <div\r\n                                        className=\"chat-messages\"\r\n                                        style={{\r\n                                            height: 'calc(100vh - 300px)',\r\n                                            overflowY: 'auto',\r\n                                            padding: '20px',\r\n                                            display: 'flex',\r\n                                            flexDirection: 'column',\r\n                                            gap: '10px'\r\n                                        }}\r\n                                    >\r\n                                        {chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (\r\n                                            <>\r\n                                                {chatMessages[selectedChat].map((msg) => (\r\n                                                    <div\r\n                                                        key={msg.id}\r\n                                                        className={`message ${msg.sender === \"You\" ? \"message-you\" : \"message-friend\"}`}\r\n                                                    >\r\n                                                        <div\r\n                                                            className=\"message-bubble\"\r\n                                                            style={{\r\n                                                                background: msg.sender === \"You\"\r\n                                                                    ? currentThemeStyles.messageBgYou || '#3b82f6'\r\n                                                                    : 'rgba(255, 255, 255, 0.9)',\r\n                                                                color: msg.sender === \"You\" ? '#ffffff' : '#333'\r\n                                                            }}\r\n                                                        >\r\n                                                            <span className=\"message-text\">{msg.text}</span>\r\n                                                            <div className=\"message-footer\">\r\n                                                                <span className=\"message-timestamp\">{msg.timestamp}</span>\r\n                                                                {msg.sender === \"You\" && (\r\n                                                                    <span className=\"delivery-status\">\r\n                                                                        {msg.delivered ? (\r\n                                                                            <span>Seen</span>\r\n                                                                        ) : (\r\n                                                                            <span>Sent</span>\r\n                                                                        )}\r\n                                                                    </span>\r\n                                                                )}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                ))}\r\n\r\n                                                {/* Typing Indicator */}\r\n                                                {typingUsers.has(selectedChat) && (\r\n                                                    <div className=\"message message-friend\">\r\n                                                        <div\r\n                                                            className=\"message-bubble\"\r\n                                                            style={{\r\n                                                                background: 'rgba(255, 255, 255, 0.9)',\r\n                                                                color: '#333'\r\n                                                            }}\r\n                                                        >\r\n                                                            <span className=\"typing-indicator\">\r\n                                                                <span></span>\r\n                                                                <span></span>\r\n                                                                <span></span>\r\n                                                            </span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                )}\r\n\r\n                                                {/* Auto-scroll anchor */}\r\n                                                <div ref={messagesEndRef} />\r\n                                            </>\r\n                                        ) : (\r\n                                            <div className=\"text-center p-4\" style={{\r\n                                                display: 'flex',\r\n                                                flexDirection: 'column',\r\n                                                alignItems: 'center',\r\n                                                justifyContent: 'center',\r\n                                                height: '100%'\r\n                                            }}>\r\n                                                <i className=\"bi bi-chat-dots\" style={{ fontSize: '3rem', opacity: 0.5 }}></i>\r\n                                                <h4 className=\"mt-3\">No messages yet</h4>\r\n                                                <p className=\"\">Start the conversation by sending a message!</p>\r\n                                            </div>\r\n                                        )}\r\n                                    </div>\r\n                                    <form className=\"chat-input-form\" onSubmit={handleSendMessage}>\r\n                                        <div className=\"input-group\">\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                className=\"form-control\"\r\n                                                placeholder=\"Type a message...\"\r\n                                                value={newMessage}\r\n                                                onChange={(e) => handleTyping(e.target.value)}\r\n                                                onKeyDown={(e) => {\r\n                                                    if (e.key === 'Enter' && !e.shiftKey) {\r\n                                                        e.preventDefault();\r\n                                                        handleSendMessage(e);\r\n                                                    }\r\n                                                }}\r\n                                                style={{\r\n                                                    background: currentThemeStyles.inputBg,\r\n                                                    color: currentThemeStyles.inputColor,\r\n                                                    border: 'none',\r\n                                                    borderRadius: '25px',\r\n                                                    padding: '12px 20px'\r\n                                                }}\r\n                                            />\r\n                                            <button\r\n                                                type=\"submit\"\r\n                                                className=\"chat-send-btn btn btn-primary\"\r\n                                                onClick={handleSendMessage}\r\n                                                style={{\r\n                                                    borderRadius: '50%',\r\n                                                    width: '45px',\r\n                                                    height: '45px',\r\n                                                    marginLeft: '10px',\r\n                                                    display: 'flex',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center'\r\n                                                }}\r\n                                            >\r\n                                                <i className=\"bi bi-send-fill\"></i>\r\n                                            </button>\r\n                                        </div>\r\n                                    </form>\r\n                                </div>\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"instructions text-center\">\r\n                                <div className=\"instruction-content\">\r\n                                    <i className=\"bi bi-chat-heart\" style={{ fontSize: '4rem', opacity: 0.3, marginBottom: '20px' }}></i>\r\n                                    <h3 className=\"instruction-title\">Your messages</h3>\r\n                                    <p className=\"instruction-text\">Send a message to start a chat.</p>\r\n                                    <button\r\n                                        className=\"btn btn-primary\"\r\n                                        data-bs-toggle=\"modal\"\r\n                                        data-bs-target=\"#followers\"\r\n                                        style={{\r\n                                            borderRadius: '25px',\r\n                                            padding: '10px 30px',\r\n                                            fontWeight: '500'\r\n                                        }}\r\n                                    >\r\n                                        <i className=\"bi bi-person-plus me-2\"></i>\r\n                                        Send Message\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            )}\r\n            <div className=\"modal\" id=\"followers\">\r\n                <div className=\"modal-dialog\">\r\n                    <div className=\"modal-content\">\r\n                        <div className='d-flex justify-content-between'>\r\n                            <div>\r\n                                <h5>Followers</h5>\r\n                            </div>\r\n                            <div>\r\n                                <button type=\"button\" className=\"btn-close bg-primary\" data-bs-dismiss=\"modal\"></button>\r\n                            </div>\r\n                        </div><hr />\r\n                        <div className=\"\">\r\n                            <div>\r\n                                <input\r\n                                    type=\"search\"\r\n                                    name=\"search\"\r\n                                    id=\"search\"\r\n                                    className=\"form-control mb-3\"\r\n                                    placeholder=\"Search users...\"\r\n                                    value={searchTerm}\r\n                                    style={{\r\n                                        backgroundColor: \"white\",\r\n                                        transition: \"background 0.3s\",\r\n                                        gap: \"10px\",\r\n                                        border: \"1px solid #333\"\r\n                                    }}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                                    onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                                />\r\n\r\n                                {\r\n                                    loading ? (\r\n                                        <div className='d-flex gap-4'>\r\n                                            <div\r\n                                                className=\"skeleton\"\r\n                                                style={{\r\n                                                    width: \"45px\",\r\n                                                    height: \"45px\",\r\n                                                    borderRadius: \"50%\",\r\n                                                }}\r\n                                            ></div>\r\n                                            <div>\r\n                                                <div\r\n                                                    className=\"skeleton\"\r\n                                                    style={{\r\n                                                        width: \"120px\",\r\n                                                        height: \"16px\",\r\n                                                        borderRadius: \"4px\",\r\n                                                        marginBottom: \"8px\",\r\n                                                    }}\r\n                                                ></div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <>\r\n                                            {visibleUsers.length > 0 ? (\r\n                                                visibleUsers.map(user => (\r\n                                                    <div key={user._id} className='d-flex align-items-center mb-2 p-2 rounded user-result' style={{ justifyContent: \"space-between\" }}>\r\n                                                        <div\r\n                                                            className=\"d-flex gap-4 align-items-center\"\r\n                                                            style={{\r\n                                                                cursor: \"pointer\",\r\n                                                            }}\r\n                                                        >\r\n                                                            <Image\r\n                                                                src={user.image || predefine}\r\n                                                                alt={user.name}\r\n                                                                width={60}\r\n                                                                height={60}\r\n                                                                className=\"rounded-circle\"\r\n                                                                style={{ objectFit: \"cover\" }}\r\n                                                            />\r\n                                                            <div>\r\n                                                                <strong>{user.username}</strong><br />\r\n                                                                <span>{user.name}</span>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div>\r\n                                                            <button\r\n                                                                className='btn btn-primary btn-sm'\r\n                                                                onClick={() => handleRemoveFollower(user._id)}\r\n                                                            >\r\n                                                                Message\r\n                                                            </button>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                ))\r\n                                            ) : (\r\n                                                <div className=\"no-followers-container text-center mt-5\">\r\n                                                    <div className=\"icon-wrapper mb-3\">\r\n                                                        <i className=\"bi bi-person-x\" style={{ fontSize: \"3rem\", color: \"#6c757d\" }}></i>\r\n                                                    </div>\r\n                                                    <h5 style={{ color: \"#6c757d\" }}>No Followers Found</h5>\r\n                                                </div>\r\n\r\n                                            )}\r\n                                            {/* Show \"Load More\" button only if there are more followed users to show */}\r\n                                            {visibleUsers.length < filteredFollowers.length && (\r\n                                                searchTerm.trim() === ''\r\n                                                    ? (profile.followers.length || 0)\r\n                                                    : users.filter(user =>\r\n                                                        profile.followers.includes(user._id) &&\r\n                                                        (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                                                            user.username.toLowerCase().includes(searchTerm.toLowerCase()))\r\n                                                    ).length\r\n                                            ) && (\r\n                                                    <div className=\"text-center mt-3\">\r\n                                                        <button className=\"btn w-100 btn-outline-primary\" onClick={handleLoadMore}>\r\n                                                            Load More\r\n                                                        </button>\r\n                                                    </div>\r\n                                                )}\r\n\r\n\r\n                                        </>\r\n\r\n\r\n                                    )\r\n                                }\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Bootstrap 5 Modal for Mobile Chat */}\r\n            <div\r\n                className={`modal fade ${showMobileModal ? 'show' : ''}`}\r\n                id=\"mobileChat\"\r\n                tabIndex=\"-1\"\r\n                aria-labelledby=\"mobileChatLabel\"\r\n                aria-hidden={!showMobileModal}\r\n                style={{\r\n                    display: showMobileModal ? 'block' : 'none',\r\n                    backgroundColor: showMobileModal ? 'rgba(0,0,0,0.5)' : 'transparent'\r\n                }}\r\n            >\r\n                <div className=\"modal-dialog modal-fullscreen\">\r\n                    <div className=\"modal-content\" style={{ background: styles.background, color: styles.color }}>\r\n                        {/* Modal Header - Default Styling */}\r\n                        <div className=\"modal-header border-bottom d-flex justify-content-between\">\r\n                            <div className=\"d-flex align-items-center gap-3\">\r\n                                <div className=\"position-relative\">\r\n                                    <Image\r\n                                        src={selectedUser?.image || predefine}\r\n                                        alt={selectedUser?.name || 'User'}\r\n                                        width={40}\r\n                                        height={40}\r\n                                        className=\"rounded-circle\"\r\n                                    />\r\n                                    {/* Online Status Indicator */}\r\n                                    <span\r\n                                        className=\"position-absolute\"\r\n                                        style={{\r\n                                            bottom: '1px',\r\n                                            right: '1px',\r\n                                            width: '12px',\r\n                                            height: '12px',\r\n                                            backgroundColor: onlineUsers.has(selectedChat) ? '#E81C1C' : '#0FC953',\r\n                                            borderRadius: '50%',\r\n                                            boxShadow: '0 2 0 2px rgba(9, 50, 11, 0.27)'\r\n                                        }}\r\n                                    ></span>\r\n                                </div>\r\n                                <div>\r\n                                    <h6 className=\"mb-0 fw-bold\">\r\n                                        {selectedUser?.name || 'User'}\r\n                                    </h6>\r\n                                    <small>\r\n                                        {onlineUsers.has(selectedChat)\r\n                                            ? 'Online'\r\n                                            : formatLastSeen(selectedUser?.lastSeen || new Date(), false)\r\n                                        }\r\n                                    </small>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                                {/* Chat Size Display Only */}\r\n                                {selectedUser && chatSizes[`${sessionUserId}_${selectedUser._id}`] && (\r\n                                    <div className=\"me-2 text-center\">\r\n                                        <small style={{\r\n                                            fontSize: '11px',\r\n                                            color: chatSizes[`${sessionUserId}_${selectedUser._id}`]?.exceedsLimit ? '#dc3545' : '#6c757d'\r\n                                        }}>\r\n                                            {chatSizes[`${sessionUserId}_${selectedUser._id}`]?.sizeFormatted}\r\n                                        </small>\r\n                                    </div>\r\n                                )}\r\n                                <button\r\n                                    type=\"button\"\r\n                                    className=\"btn btn-close bg-warning\"\r\n                                    onClick={handleBackToList}\r\n                                    aria-label=\"Close\"\r\n                                ></button>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Modal Body - Chat Messages */}\r\n                        <div className=\"modal-body d-flex flex-column\" style={{ height: 'calc(100vh - 120px)' }}>\r\n                            <div\r\n                                className=\"flex-grow-1 overflow-auto\"\r\n                                style={{\r\n                                    maxHeight: 'calc(100vh - 200px)',\r\n                                    padding: '12px 6px'\r\n                                }}\r\n                            >\r\n                                {selectedChat && chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (\r\n                                    <>\r\n                                        {chatMessages[selectedChat].map((msg) => (\r\n                                            <div key={msg.id} className=\"mb-2\">\r\n                                                {/* Swapped: Receiver RIGHT, Sender LEFT */}\r\n                                                <div\r\n                                                    className={`d-flex ${msg.sender === \"You\" ? \"justify-content-start\" : \"justify-content-end\"}`}\r\n                                                    style={{ width: '100%' }}\r\n                                                >\r\n                                                    <div\r\n                                                        style={{\r\n                                                            backgroundColor: msg.sender === \"You\"\r\n                                                                ? '#f1f1f1'\r\n                                                                : '#007bff',\r\n                                                            color: msg.sender === \"You\" ? '#333' : 'white',\r\n                                                            padding: '10px 14px',\r\n                                                            borderRadius: msg.sender === \"You\"\r\n                                                                ? '18px 18px 18px 4px'\r\n                                                                : '18px 18px 4px 18px',\r\n                                                            maxWidth: '80%',\r\n                                                            wordWrap: 'break-word',\r\n                                                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\r\n                                                            position: 'relative'\r\n                                                        }}\r\n                                                    >\r\n                                                        <div style={{\r\n                                                            fontSize: '15px',\r\n                                                            lineHeight: '1.4',\r\n                                                            marginBottom: '4px',\r\n                                                            textAlign: msg.sender === \"You\" ? 'left' : 'right'\r\n                                                        }}>\r\n                                                            {msg.text}\r\n                                                        </div>\r\n                                                        <div style={{\r\n                                                            fontSize: '11px',\r\n                                                            opacity: 0.7,\r\n                                                            textAlign: msg.sender === \"You\" ? 'left' : 'right',\r\n                                                            marginTop: '2px'\r\n                                                        }}>\r\n                                                            {msg.timestamp}\r\n                                                            {msg.sender !== \"You\" && (\r\n                                                                <span style={{ marginLeft: '5px' }}>\r\n                                                                    {msg.delivered ? \"Seen\" : \"Sent\"}\r\n                                                                </span>\r\n                                                            )}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        ))}\r\n\r\n                                        {/* Chat Size Warning and Delete Option */}\r\n                                        {selectedUser && chatSizes[`${sessionUserId}_${selectedUser._id}`] &&\r\n                                         chatSizes[`${sessionUserId}_${selectedUser._id}`]?.exceedsLimit && (\r\n                                            <div className=\"mb-3\">\r\n                                                <div className=\"d-flex justify-content-center\">\r\n                                                    <div\r\n                                                        style={{\r\n                                                            backgroundColor: '#fff3cd',\r\n                                                            color: '#856404',\r\n                                                            padding: '12px 6px',\r\n                                                            borderRadius: '15px',\r\n                                                            border: '1px solid #ffeaa7',\r\n                                                            textAlign: 'center',\r\n                                                            maxWidth: '90%'\r\n                                                        }}\r\n                                                    >\r\n                                                        <button\r\n                                                            className=\"btn btn-sm btn-danger\"\r\n                                                            style={{\r\n                                                                fontSize: '12px',\r\n                                                                padding: '6px 12px',\r\n                                                                borderRadius: '20px'\r\n                                                            }}\r\n                                                            onClick={() => {\r\n                                                                const chatData = chatSizes[`${sessionUserId}_${selectedUser._id}`];\r\n                                                                if (confirm('⚠️ This will permanently delete all messages in this chat. Are you sure?')) {\r\n                                                                    deleteChat(chatData.chatId, sessionUserId, selectedUser._id);\r\n                                                                }\r\n                                                            }}\r\n                                                        >\r\n                                                            🗑️ Delete Chat\r\n                                                        </button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Typing Indicator - Now on right (receiver side) */}\r\n                                        {typingUsers.has(selectedChat) && (\r\n                                            <div className=\"mb-2\">\r\n                                                <div className=\"d-flex justify-content-end\" style={{ width: '100%' }}>\r\n                                                    <div\r\n                                                        style={{\r\n                                                            backgroundColor: '#007bff',\r\n                                                            color: 'white',\r\n                                                            padding: '10px 14px',\r\n                                                            borderRadius: '18px 18px 4px 18px',\r\n                                                            maxWidth: '80%',\r\n                                                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\r\n                                                        }}\r\n                                                    >\r\n                                                        <div className=\"d-flex align-items-center justify-content-end\">\r\n                                                            <small style={{\r\n                                                                color: 'rgba(255,255,255,0.8)',\r\n                                                                fontSize: '12px',\r\n                                                                fontStyle: 'italic',\r\n                                                                marginRight: '8px'\r\n                                                            }}>\r\n                                                                typing...\r\n                                                            </small>\r\n                                                            <span className=\"typing-indicator\">\r\n                                                                <span></span>\r\n                                                                <span></span>\r\n                                                                <span></span>\r\n                                                            </span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        <div ref={messagesEndRef} />\r\n                                    </>\r\n                                ) : (\r\n                                    <div className=\"text-center py-5\">\r\n                                        <i className=\"bi bi-chat-dots\" style={{ fontSize: '3rem', opacity: 0.5 }}></i>\r\n                                        <h4 className=\"mt-3\">No messages yet</h4>\r\n                                        <p className=\"\">Start the conversation by sending a message!</p>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n\r\n                            {/* Chat Input - Full Width */}\r\n                            <div className=\"w-100\" style={{ padding: '12px 2px'}}>\r\n                                {deletedChats.has(`${sessionUserId}_${selectedUser?._id}`) ? (\r\n                                    <div className=\"text-center py-3\">\r\n                                        <p className=\"text-muted mb-2\">This chat has been deleted</p>\r\n                                        <button\r\n                                            className=\"btn btn-sm btn-outline-primary\"\r\n                                            onClick={() => {\r\n                                                const chatData = chatSizes[`${sessionUserId}_${selectedUser._id}`];\r\n                                                if (chatData) {\r\n                                                    restoreChat(chatData.chatId, sessionUserId, selectedUser._id);\r\n                                                }\r\n                                            }}\r\n                                        >\r\n                                            Restore Chat\r\n                                        </button>\r\n                                    </div>\r\n                                ) : (\r\n                                    <form onSubmit={handleSendMessage}>\r\n                                        <div className=\"d-flex align-items-center\" style={{ gap: '8px' }}>\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                className=\"form-control\"\r\n                                                placeholder=\"Message...\"\r\n                                                value={newMessage}\r\n                                                onChange={(e) => handleTyping(e.target.value)}\r\n                                                onKeyDown={(e) => {\r\n                                                    if (e.key === 'Enter' && !e.shiftKey) {\r\n                                                        e.preventDefault();\r\n                                                        handleSendMessage(e);\r\n                                                    }\r\n                                                }}\r\n                                                style={{\r\n                                                    borderRadius: '25px',\r\n                                                    padding: '12px 16px',\r\n                                                    border: '1px solid #e0e0e0',\r\n                                                    fontSize: '15px',\r\n                                                    backgroundColor: '#f8f9fa',\r\n                                                    flex: '1'\r\n                                                }}\r\n                                            />\r\n                                            <button\r\n                                                type=\"submit\"\r\n                                                className=\"btn btn-primary\"\r\n                                                onClick={handleSendMessage}\r\n                                                style={{\r\n                                                    borderRadius: '50%',\r\n                                                    width: '44px',\r\n                                                    height: '44px',\r\n                                                    display: 'flex',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center',\r\n                                                    padding: '0',\r\n                                                    backgroundColor: '#007bff',\r\n                                                    border: 'none'\r\n                                                }}\r\n                                            >\r\n                                                <i className=\"bi bi-send-fill\" style={{ fontSize: '16px' }}></i>\r\n                                            </button>\r\n                                        </div>\r\n                                    </form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </DashboardLayout>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAEe,SAAS;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;QACV,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ,qRAAA,CAAA,UAAS;QACjB,OAAO;QACP,gBAAgB;QAChB,gBAAgB;IACpB;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YAAE,IAAI;YAAG,QAAQ;YAAO,MAAM;YAAwB,WAAW;QAAW;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAU,MAAM;YAA6B,WAAW;QAAW;QACpF;YAAE,IAAI;YAAG,QAAQ;YAAO,MAAM;YAAwB,WAAW;QAAW;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAU,MAAM;YAA6B,WAAW;QAAW;KACvF;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,mBAAmB,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,mCAAmC;IACnC,MAAM,gBAAgB,OAAO,SAAS;QAClC,IAAI;YACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,SAAS;YAC9D,MAAM,WAAW,SAAS,IAAI;YAE9B,aAAa,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;wBACvB,aAAa,SAAS,WAAW;wBACjC,eAAe,SAAS,aAAa;wBACrC,cAAc,SAAS,YAAY;wBACnC,QAAQ,SAAS,MAAM;oBAC3B;gBACJ,CAAC;YAED,IAAI,SAAS,SAAS,EAAE;gBACpB,gBAAgB,CAAA,OAAQ,IAAI,IAAI;2BAAI;wBAAM,GAAG,QAAQ,CAAC,EAAE,SAAS;qBAAC;YACtE;YAEA,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACX;IACJ;IAEA,MAAM,aAAa,OAAO,QAAQ,SAAS;QACvC,IAAI;YACA,MAAM,6GAAA,CAAA,UAAK,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,QAAQ;YAEpC,uBAAuB;YACvB,gBAAgB,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM,GAAG,QAAQ,CAAC,EAAE,SAAS;iBAAC;YAElE,uBAAuB;YACvB,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE;gBACjC,CAAC;YAED,sBAAsB;YACtB,aAAa,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,SAAS;YAEpD,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACX;IACJ;IAEA,MAAM,cAAc,OAAO,QAAQ,SAAS;QACxC,IAAI;YACA,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,QAAQ,CAAC;YAE1C,4BAA4B;YAC5B,gBAAgB,CAAA;gBACZ,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;gBACrC,OAAO;YACX;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACX;IACJ;IAEA,yCAAyC;IACzC,MAAM,iBAAiB,CAAC,UAAU;QAC9B,IAAI,UAAU,OAAO;QAErB,MAAM,MAAM,IAAI;QAChB,MAAM,eAAe,IAAI,KAAK;QAC9B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,YAAY,IAAI,CAAC,OAAO,EAAE;QAElE,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;QAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;QAElD,oCAAoC;QACpC,MAAM,UAAU;YACZ,MAAM;YACN,QAAQ;YACR,QAAQ;QACZ;QAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,IAAI,eAAe,GAAG;YAClB,OAAO,CAAC,UAAU,EAAE,aAAa,kBAAkB,CAAC,SAAS,UAAU;QAC3E,OAAO,IAAI,aAAa,GAAG;YACvB,OAAO,GAAG,WAAW,KAAK,CAAC;QAC/B,OAAO;YACH,OAAO,aAAa,kBAAkB,CAAC,SAAS;gBAC5C,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACZ;QACJ;IACJ;IAEA,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO,EAAE;YACxC,MAAM,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ;YACjD,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;QAC3C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACb;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ;YACjD,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;YAC7C,QAAQ,GAAG,CAAC,mCAAmC;QACnD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO;YACtC,MAAM,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,QAAQ;YACrD,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,SAAS,UAAU;QACvC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACX;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,QAAQ;YACrD,aAAa,OAAO,CAAC,WAAW,MAAM,QAAQ;YAC9C,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;gBACd,CAAC;QACL,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO;YACtC,MAAM,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,QAAQ;YACvD,OAAO,aAAa,OAAO,CAAC,eAAe;QAC/C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACX;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,QAAQ;YACvD,MAAM,mBAAmB,QAAQ,MAAM,GAAG,KAAK,QAAQ,SAAS,CAAC,GAAG,MAAM,QAAQ;YAClF,aAAa,OAAO,CAAC,YAAY;YACjC,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;gBACd,CAAC;QACL,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACrB,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,6BAA6B;QAC7B,IAAI,OAAO,UAAU,IAAI,MAAM;YAC3B,OAAO,OAAO,CAAC,uBAAuB,WAAW;gBAAE,SAAS;YAAK;QACrE;IACJ;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,YAAY;YACd,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YAEpC,IAAI,CAAC,WAAW;gBACZ,SAAS;gBACT,WAAW;gBACX;YACJ;YAEA,IAAI;gBACA,gCAAgC;gBAChC,QAAQ,GAAG,CAAC;gBACZ,MAAM,CAAA,GAAA,6GAAA,CAAA,wBAAqB,AAAD;gBAE1B,kBAAkB;gBAClB,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAAC;gBAElC,MAAM,aAAa,SAAS,IAAI;gBAChC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBAC7D,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBACzD,eAAe;gBAEf,sBAAsB;gBACtB,MAAM,YAAY,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;gBAC/D,MAAM,aAAa,UAAU,IAAI;gBACjC,aAAa,IAAI,IAAI,WAAW,SAAS;gBACzC,YAAY,IAAI,IAAI,WAAW,QAAQ;gBAEvC,mCAAmC;gBACnC,MAAM,mBAAmB,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;wBAC1D,GAAG,GAAG;wBACN,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,gBAAgB,EAAE,IAAI,OAAO,kBAAkB,IAAI;oBACxF,CAAC,GAAG,CAAC;gBACL,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;wBACvD,GAAG,GAAG;wBACN,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,OAAO,IAAI;oBACnE,CAAC,GAAG,CAAC;gBACL,gBAAgB;gBAChB,aAAa;gBAEb,SAAS;gBACT,WAAW;YACf,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,0BAA0B;gBAExC,IAAI,IAAI,OAAO,KAAK,mBAAmB,IAAI,IAAI,KAAK,gBAAgB;oBAChE,SAAS;oBACT,QAAQ,KAAK,CAAC;oBACd,QAAQ,KAAK,CAAC;oBACd,QAAQ,KAAK,CAAC;oBACd,QAAQ,KAAK,CAAC;oBACd,QAAQ,KAAK,CAAC;gBAClB,OAAO;oBACH,SAAS,0BAA0B,IAAI,OAAO;gBAClD;gBAEA,WAAW;YACf;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,MAAM,MAAM,GAAG,KAAK,eAAe;YACnC,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc,CAAC;YACrB,MAAM,qBAAqB,CAAC;YAC5B,MAAM,qBAAqB,CAAC;YAE5B,MAAM,OAAO,CAAC,CAAA;gBACV,IAAI,SAAS,GAAG,CAAC,KAAK,GAAG,GAAG;oBACxB,oBAAoB;oBACpB,MAAM,UAAU,eAAe,KAAK,GAAG;oBACvC,IAAI,QAAQ,MAAM,GAAG,GAAG;wBACpB,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG;wBACxB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,MAAM,CAAC,mBAAmB,CAAC,EAAE,KAAK,IAAI;oBAC3E;oBAEA,oBAAoB;oBACpB,MAAM,cAAc,eAAe,KAAK,GAAG;oBAC3C,IAAI,cAAc,GAAG;wBACjB,kBAAkB,CAAC,KAAK,GAAG,CAAC,GAAG;oBACnC;oBAEA,oBAAoB;oBACpB,MAAM,UAAU,eAAe,KAAK,GAAG;oBACvC,IAAI,SAAS;wBACT,kBAAkB,CAAC,KAAK,GAAG,CAAC,GAAG;oBACnC;oBAEA,gCAAgC;oBAChC,cAAc,eAAe,KAAK,GAAG;gBACzC;YACJ;YAEA,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,QAAQ,GAAG,CAAC;QAChB;IACJ,GAAG;QAAC;QAAO;QAAe;KAAS;IAEnC,mDAAmD;IACnD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,eAAe;YACf,uBAAuB;YACvB,wGAAA,CAAA,UAAa,CAAC,OAAO,CAAC;YAEtB,+BAA+B;YAC/B,MAAM,sBAAsB,wGAAA,CAAA,UAAa,CAAC,SAAS,CAAC,CAAC;gBACjD,QAAQ,GAAG,CAAC,wBAAwB;gBACpC,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,QAAQ,GAAG,CAAC,4BAA4B;gBACxC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;gBAEhE,+DAA+D;gBAC/D,IAAI,eAAe,iBAAiB,aAAa,eAAe;oBAC5D,QAAQ,GAAG,CAAC;oBACZ,MAAM,gBAAgB,eAAe,gBAAgB,WAAW;oBAChE,MAAM,oBAAoB,eAAe;oBAEzC,MAAM,aAAa;wBACf,IAAI;wBACJ,QAAQ,aAAa,gBAAgB,QAAQ;wBAC7C,MAAM;wBACN,WAAW,IAAI,KAAK,WAAW,kBAAkB,CAAC,EAAE,EAAE;4BAClD,MAAM;4BACN,QAAQ;4BACR,QAAQ;wBACZ;wBACA,WAAW;wBACX,MAAM;oBACV;oBAEA,uBAAuB;oBACvB,gBAAgB,CAAA;wBACZ,MAAM,kBAAkB;+BAAK,IAAI,CAAC,cAAc,IAAI,EAAE;4BAAG;yBAAW;wBACpE,wBAAwB;wBACxB,gBAAgB,eAAe;wBAC/B,OAAO;4BACH,GAAG,IAAI;4BACP,CAAC,cAAc,EAAE;wBACrB;oBACJ;oBAEA,sBAAsB;oBACtB,gBAAgB,eAAe;oBAE/B,4CAA4C;oBAC5C,IAAI,mBAAmB;wBACnB,MAAM,gBAAgB,eAAe;wBACrC,MAAM,iBAAiB,gBAAgB;wBACvC,gBAAgB,eAAe;wBAC/B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,cAAc,EAAE,EAAE,gBAAgB;oBACzE;gBACJ;YACJ;YAEA,mCAAmC;YACnC,MAAM,0BAA0B,wGAAA,CAAA,UAAa,CAAC,cAAc,CAAC,CAAC;gBAC1D,QAAQ,GAAG,CAAC,4BAA4B;gBACxC,IAAI,KAAK,IAAI,KAAK,WAAW;oBACzB,QAAQ,GAAG,CAAC,oCAAoC,KAAK,OAAO;oBAC5D,eAAe,IAAI,IAAI,KAAK,OAAO;gBACvC,OAAO,IAAI,KAAK,IAAI,KAAK,UAAU;oBAC/B,QAAQ,GAAG,CAAC,wBAAwB,KAAK,MAAM;oBAC/C,eAAe,CAAA,OAAQ,IAAI,IAAI;+BAAI;4BAAM,KAAK,MAAM;yBAAC;gBACzD,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;oBAChC,QAAQ,GAAG,CAAC,yBAAyB,KAAK,MAAM;oBAChD,eAAe,CAAA;wBACX,MAAM,SAAS,IAAI,IAAI;wBACvB,OAAO,MAAM,CAAC,KAAK,MAAM;wBACzB,OAAO;oBACX;gBACJ;YACJ;YAEA,+BAA+B;YAC/B,MAAM,oBAAoB,wGAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,CAAC;gBAC9C,QAAQ,GAAG,CAAC,wBAAwB;gBACpC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;gBAC7B,eAAe,CAAA;oBACX,MAAM,SAAS,IAAI,IAAI;oBACvB,IAAI,UAAU;wBACV,QAAQ,GAAG,CAAC,2BAA2B;wBACvC,OAAO,GAAG,CAAC;oBACf,OAAO;wBACH,QAAQ,GAAG,CAAC,2BAA2B;wBACvC,OAAO,MAAM,CAAC;oBAClB;oBACA,OAAO;gBACX;gBAEA,yCAAyC;gBACzC,IAAI,UAAU;oBACV,WAAW;wBACP,eAAe,CAAA;4BACX,MAAM,SAAS,IAAI,IAAI;4BACvB,OAAO,MAAM,CAAC;4BACd,OAAO;wBACX;oBACJ,GAAG;gBACP;YACJ;YAEA,qBAAqB;YACrB,OAAO;gBACH;gBACA;gBACA;gBACA,wGAAA,CAAA,UAAa,CAAC,UAAU;YAC5B;QACJ;IACJ,GAAG;QAAC;KAAc;IAElB,2BAA2B;IAC3B,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,OAAO,OAAO,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,gBAAgB,eAAe;YACtE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,KAAK;YAC/B,IAAI,UAAU,WAAW,eAAe,WAAW,QAAQ;gBACvD,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;gBAC/C,IAAI,gBAAgB,SAAS,GAAG,CAAC,aAAa,GAAG,GAAG;oBAChD,gBAAgB,aAAa,GAAG;oBAChC,gBAAgB;oBAChB,kCAAkC;oBAClC,MAAM,UAAU,eAAe,aAAa,GAAG;oBAC/C,gBAAgB,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,CAAC,aAAa,GAAG,CAAC,EAAE;wBACxB,CAAC;gBACL;YACJ;QACJ;IACJ,GAAG;QAAC,OAAO,OAAO;QAAE;QAAO;QAAU;QAAc;KAAc;IAEjE,oDAAoD;IACpD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,eAAe,OAAO,EAAE;YACxB,eAAe,OAAO,CAAC,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC/D;IACJ,GAAG;QAAC;QAAc;KAAa;IAE/B,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa;YACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YACpC,iBAAiB;YAEjB,IAAI;gBACA,gCAAgC;gBAChC,QAAQ,GAAG,CAAC;gBACZ,MAAM,CAAA,GAAA,6GAAA,CAAA,wBAAqB,AAAD;gBAE1B,QAAQ,GAAG,CAAC;gBACZ,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAAC;gBAElC,MAAM,WAAW,SAAS,IAAI,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBAC3D,SAAS;gBACT,gBAAgB,SAAS,KAAK,CAAC,GAAG,KAAK,UAAU;gBACjD,WAAW,IAAM,WAAW,QAAQ,OAAO,sBAAsB;YACrE,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBAEzC,IAAI,MAAM,OAAO,KAAK,mBAAmB,MAAM,IAAI,KAAK,gBAAgB;oBACpE,SAAS;oBACT,QAAQ,KAAK,CAAC;gBAClB,OAAO;oBACH,SAAS,2BAA2B,MAAM,OAAO;gBACrD;gBAEA,WAAW;YACf;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,gBAAgB,MACjB,MAAM,CAAC,CAAA,OAAQ,SAAS,GAAG,CAAC,KAAK,GAAG,GACpC,MAAM,CAAC,CAAA;QACJ,iCAAiC;QACjC,MAAM,cAAc,WAAW,WAAW;QAC1C,OAAO,gBAAgB,MACnB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;IAC7C,GACC,MAAM,CAAC,CAAA,OACJ,iBAAiB,KAAK,QAAQ,GAAG;IAGzC,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,aAAa;gBACb,gBAAgB;YACpB;QACJ;QACA,OAAO;YACH,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,aAAa;QACjB;IACJ;IAEA,MAAM,SAAS;IAEf,MAAM,kBAAkB;QACpB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,iBAAiB;YACrB;QACJ;QACA,4DAA4D;QAC5D,OAAO;YACH,YAAY;YACZ,OAAO;YACP,SAAS;YACT,YAAY;YACZ,cAAc;YACd,iBAAiB;QACrB;IACJ;IAEA,MAAM,qBAAqB;IAE3B,MAAM,oBAAoB,CAAC;QACvB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,kCAAkC;YAC1C,YAAY,WAAW,IAAI;YAC3B;YACA;YACA,iBAAiB,wGAAA,CAAA,UAAa,CAAC,mBAAmB;QACtD;QAEA,IAAI,WAAW,IAAI,MAAM,gBAAgB,eAAe;YACpD,oDAAoD;YACpD,QAAQ,GAAG,CAAC,0BAA0B,cAAc,SAAS;YAC7D,QAAQ,GAAG,CAAC,uBAAuB,WAAW,IAAI;YAClD,MAAM,UAAU,wGAAA,CAAA,UAAa,CAAC,WAAW,CAAC,cAAc,WAAW,IAAI;YACvE,QAAQ,GAAG,CAAC,0BAA0B;YACtC,QAAQ,GAAG,CAAC,gCAAgC,wGAAA,CAAA,UAAa,CAAC,mBAAmB;YAE7E,IAAI,SAAS;gBACT,+DAA+D;gBAC/D,MAAM,SAAS;oBACX,IAAI,KAAK,GAAG;oBACZ,QAAQ;oBACR,MAAM,WAAW,IAAI;oBACrB,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;wBACzC,MAAM;wBACN,QAAQ;wBACR,QAAQ;oBACZ;oBACA,WAAW,YAAY,GAAG,CAAC;oBAC3B,MAAM;gBACV;gBAEA,MAAM,kBAAkB,YAAY,CAAC,aAAa,IAAI,EAAE;gBACxD,MAAM,kBAAkB;uBAAI;oBAAiB;iBAAO;gBAEpD,uDAAuD;gBACvD,gBAAgB,CAAA;oBACZ,MAAM,UAAU;wBACZ,GAAG,IAAI;wBACP,CAAC,aAAa,EAAE;oBACpB;oBACA,wBAAwB;oBACxB,gBAAgB,cAAc;oBAC9B,OAAO;gBACX;gBAEA,sBAAsB;gBACtB,gBAAgB,cAAc,OAAO,IAAI;gBAEzC,cAAc;gBACd,cAAc;gBAEd,wBAAwB;gBACxB,IAAI,UAAU;oBACV,YAAY;oBACZ,wGAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;gBACpD;YACJ,OAAO;gBACH,QAAQ,KAAK,CAAC;YAClB;QACJ;IACJ;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAA,GAAA,mGAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,cAAc;QAEd,IAAI,gBAAgB,eAAe;YAC/B,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU;gBAC3B,YAAY;gBACZ,wGAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;YACpD,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,UAAU;gBAClC,YAAY;gBACZ,wGAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;YACpD;YAEA,uBAAuB;YACvB,IAAI,iBAAiB,OAAO,EAAE;gBAC1B,aAAa,iBAAiB,OAAO;YACzC;YAEA,2CAA2C;YAC3C,iBAAiB,OAAO,GAAG,WAAW;gBAClC,IAAI,UAAU;oBACV,YAAY;oBACZ,wGAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;gBACpD;YACJ,GAAG;QACP;IACJ,GAAG;QAAC;QAAc;QAAe;KAAS;IAE1C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,QAAQ,SAAS,GAAG;QAEnD,+DAA+D;QAC/D,MAAM,iBAAiB,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,EAAE;QAEzD,sCAAsC;QACtC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAC/B,eAAe,QAAQ,CAAC,KAAK,GAAG,EAAE;QAGtC,oBAAoB;QACpB,MAAM,WAAW,cAAc,MAAM,CAAC,CAAA,OAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAG/D,qBAAqB,WAAW,6BAA6B;QAE7D,yDAAyD;QACzD,IAAI,WAAW,IAAI,OAAO,IAAI;YAC1B,gBAAgB,cAAc,KAAK,CAAC,GAAG;QAC3C,OAAO;YACH,gBAAgB,SAAS,KAAK,CAAC,GAAG;QACtC;IAEJ,GAAG;QAAC;QAAY;QAAO;QAAc;KAAQ;IAG7C,MAAM,iBAAiB;QACnB,MAAM,YAAY;QAClB,MAAM,WAAW,YAAY;QAC7B,gBAAgB;QAEhB,qDAAqD;QACrD,WAAW;YACP,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAC5C,IAAI,SAAS,CAAC,UAAU,EAAE;gBACtB,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC;oBAChC,UAAU;oBACV,OAAO;gBACX;YACJ;QACJ,GAAG,MAAM,+CAA+C;IAC5D;IACA,qBACI,qKAAC,+HAAA,CAAA,UAAe;;YACX,wBACG,qKAAC;gBAAI,WAAU;0BACX,cAAA,qKAAC;oBAAI,SAAQ;8BACT,cAAA,qKAAC;wBAAE,MAAK;wBAAO,QAAO;wBAAO,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;;0CAElF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAqB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG/G,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAsB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGhH,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG3G,qKAAC;gCAAK,GAAE;;;;;;0CAER,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAE3G,qKAAC;gCAAiB,eAAc;gCAAY,MAAK;gCAAY,QAAO;gCAAa,KAAI;gCAAK,aAAY;;;;;;;;;;;;;;;;;;;;;uBAIlH,sBACA,qKAAC;gBAAI,WAAU;0BAAS;;;;;uBACxB,cAAc,MAAM,KAAK,kBACzB,qKAAC;gBAAI,WAAU;gBAAiB,OAAO;oBAAE,YAAY,OAAO,UAAU;oBAAE,OAAO,OAAO,KAAK;gBAAC;;kCAExF,qKAAC;wBAAI,WAAU;;0CAEX,qKAAC;gCAAG,WAAU;0CACT,aAAa,QAAQ,aAAa,YAAY;;;;;;0CAInD,qKAAC;gCAAI,WAAU;0CACX,cAAA,qKAAC;oCAAI,WAAU;;sDACX,qKAAC;4CACG,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,OAAO;gDACH,YAAY,mBAAmB,OAAO;gDACtC,OAAO,mBAAmB,UAAU;gDACpC,QAAQ;gDACR,cAAc;gDACd,SAAS;4CACb;;;;;;sDAEJ,qKAAC;4CACG,WAAU;4CACV,OAAO;gDACH,MAAM;gDACN,KAAK;gDACL,WAAW;gDACX,OAAO,mBAAmB,UAAU;gDACpC,SAAS;4CACb;;;;;;;;;;;;;;;;;0CAMZ,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAE,WAAU;wCAAkB,OAAO;4CAAE,UAAU;4CAAQ,SAAS;wCAAI;;;;;;kDACvE,qKAAC;wCAAG,WAAU;kDAAO;;;;;;kDACrB,qKAAC;wCAAE,WAAU;kDAAa;;;;;;;;;;;;;;;;;;kCAKlC,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAI,WAAU;sCACX,cAAA,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAE,WAAU;wCAAmB,OAAO;4CAAE,UAAU;4CAAQ,SAAS;4CAAK,cAAc;wCAAO;;;;;;kDAC9F,qKAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,qKAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,qKAAC;wCACG,WAAU;wCACV,SAAS;4CACL,0DAA0D;4CAC1D,QAAQ,GAAG,CAAC;wCAChB;wCACA,OAAO;4CACH,cAAc;4CACd,SAAS;4CACT,YAAY;wCAChB;;0DAEA,qKAAC;gDAAE,WAAU;;;;;;4CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAQ9D,qKAAC;gBAAI,WAAU;gBAAiB,OAAO;oBAAE,YAAY,OAAO,UAAU;oBAAE,OAAO,OAAO,KAAK;gBAAC;;kCAExF,qKAAC;wBAAI,WAAW,CAAC,UAAU,EAAE,eAAe,yBAAyB,IAAI;;0CAErE,qKAAC;gCAAG,WAAU;0CACT,aAAa,QAAQ,aAAa,YAAY;;;;;;0CAInD,qKAAC;gCAAI,WAAU;0CACX,cAAA,qKAAC;oCAAI,WAAU;;sDACX,qKAAC;4CACG,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;4CACV,OAAO;gDACH,cAAc;gDACd,aAAa;gDACb,cAAc;4CAClB;;;;;;sDAEJ,qKAAC;4CAAK,WAAU;4CAAoB,OAAO;gDACvC,OAAO;gDACP,KAAK;gDACL,WAAW;gDACX,OAAO;4CACX;sDACI,cAAA,qKAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAMzB,qKAAC;gCAAK,WAAU;0CAA4B;;;;;;4BAC3C,cAAc,GAAG,CAAC,CAAA,qBACf,qKAAC;oCAEG,WAAU;oCACV,SAAS;wCACL,gBAAgB,KAAK,GAAG;wCACxB,gBAAgB;wCAEhB,+CAA+C;wCAC/C,gBAAgB,KAAK,GAAG,EAAE;wCAE1B,uBAAuB;wCACvB,MAAM,WAAW,OAAO,UAAU,GAAG;wCAErC,IAAI,UAAU;4CACV,uBAAuB;4CACvB,mBAAmB;wCACvB,OAAO;4CACH,wBAAwB;4CACxB,OAAO,OAAO,CAAC,CAAC,2BAA2B,EAAE,KAAK,GAAG,EAAE,EAAE,WAAW;gDAAE,SAAS;4CAAK;wCACxF;wCAEA,kCAAkC;wCAClC,MAAM,UAAU,eAAe,KAAK,GAAG;wCACvC,IAAI,QAAQ,MAAM,GAAG,GAAG;4CACpB,gBAAgB,CAAA,OAAQ,CAAC;oDACrB,GAAG,IAAI;oDACP,CAAC,KAAK,GAAG,CAAC,EAAE;gDAChB,CAAC;4CACD,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,MAAM,CAAC,wBAAwB,CAAC,EAAE,KAAK,IAAI;wCAChF;oCACJ;;sDAEA,qKAAC;4CAAI,WAAU;;8DACX,qKAAC,sHAAA,CAAA,UAAK;oDACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;oDAC5B,KAAK,KAAK,IAAI;oDACd,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEd,qKAAC;oDACG,WAAW,CAAC,iBAAiB,EAAE,YAAY,GAAG,CAAC,KAAK,GAAG,IAAI,WAAW,WAAW;oDACjF,OAAO,YAAY,GAAG,CAAC,KAAK,GAAG,IAAI,WAAW;;;;;;;;;;;;sDAGtD,qKAAC;4CAAI,WAAU;;8DACX,qKAAC;oDAAI,WAAU;;sEACX,qKAAC;4DAAK,WAAU;sEAAa,KAAK,IAAI;;;;;;sEACtC,qKAAC;4DAAK,WAAU;sEACX,YAAY,GAAG,CAAC,KAAK,GAAG,IACnB,WACA,eAAe,KAAK,QAAQ,IAAI,IAAI,QAAQ;;;;;;;;;;;;8DAI1D,qKAAC;oDAAE,WAAU;8DACR,YAAY,GAAG,CAAC,KAAK,GAAG,kBACrB,qKAAC;wDAAK,WAAU;;0EACZ,qKAAC;gEAAK,WAAU;;kFACZ,qKAAC;;;;;kFACD,qKAAC;;;;;kFACD,qKAAC;;;;;;;;;;;4DACE;;;;;;+DAIX,YAAY,CAAC,KAAK,GAAG,CAAC,IAAI;;;;;;;;;;;;wCAMrC,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,mBACtB,qKAAC;4CAAI,WAAU;sDACV,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,OAAO,YAAY,CAAC,KAAK,GAAG,CAAC;;;;;;;mCAzE9D,KAAK,GAAG;;;;;;;;;;;kCAiFzB,qKAAC;wBAAI,WAAU;kCACV,6BACG,qKAAC;4BAAI,WAAU;;8CAEX,qKAAC;oCAAI,WAAU;;sDACX,qKAAC;4CAAI,WAAU;;8DACX,qKAAC,sHAAA,CAAA,UAAK;oDACF,KAAK,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe,SAAS,qRAAA,CAAA,UAAS;oDACxE,KAAK,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe;oDACtD,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,OAAO;wDAAE,cAAc;oDAAM;;;;;;8DAEjC,qKAAC;8DACG,cAAA,qKAAC;wDAAG,WAAU;wDAAO,OAAO;4DAAE,UAAU;wDAAS;kEAC5C,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe;;;;;;;;;;;;;;;;;sDAM9D,qKAAC;4CACG,MAAK;4CACL,WAAU;4CACV,SAAS;gDACL,QAAQ,GAAG,CAAC;gDACZ,gBAAgB;gDAChB,gBAAgB;4CACpB;4CACA,OAAO;gDACH,UAAU;gDACV,OAAO,iBAAiB,KAAK;gDAC7B,YAAY;gDACZ,QAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,QAAQ;gDACR,SAAS;gDACT,YAAY;gDACZ,gBAAgB;4CACpB;sDAEA,cAAA,qKAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAGrB,qKAAC;oCACG,WAAU;oCACV,OAAO;wCACH,YAAY,mBAAmB,UAAU;wCACzC,OAAO,mBAAmB,KAAK;oCACnC;;sDAGA,qKAAC;4CACG,WAAU;4CACV,OAAO;gDACH,QAAQ;gDACR,WAAW;gDACX,SAAS;gDACT,SAAS;gDACT,eAAe;gDACf,KAAK;4CACT;sDAEC,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,kBAC/D;;oDACK,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAC7B,qKAAC;4DAEG,WAAW,CAAC,QAAQ,EAAE,IAAI,MAAM,KAAK,QAAQ,gBAAgB,kBAAkB;sEAE/E,cAAA,qKAAC;gEACG,WAAU;gEACV,OAAO;oEACH,YAAY,IAAI,MAAM,KAAK,QACrB,mBAAmB,YAAY,IAAI,YACnC;oEACN,OAAO,IAAI,MAAM,KAAK,QAAQ,YAAY;gEAC9C;;kFAEA,qKAAC;wEAAK,WAAU;kFAAgB,IAAI,IAAI;;;;;;kFACxC,qKAAC;wEAAI,WAAU;;0FACX,qKAAC;gFAAK,WAAU;0FAAqB,IAAI,SAAS;;;;;;4EACjD,IAAI,MAAM,KAAK,uBACZ,qKAAC;gFAAK,WAAU;0FACX,IAAI,SAAS,iBACV,qKAAC;8FAAK;;;;;yGAEN,qKAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;2DApBrB,IAAI,EAAE;;;;;oDA8BlB,YAAY,GAAG,CAAC,+BACb,qKAAC;wDAAI,WAAU;kEACX,cAAA,qKAAC;4DACG,WAAU;4DACV,OAAO;gEACH,YAAY;gEACZ,OAAO;4DACX;sEAEA,cAAA,qKAAC;gEAAK,WAAU;;kFACZ,qKAAC;;;;;kFACD,qKAAC;;;;;kFACD,qKAAC;;;;;;;;;;;;;;;;;;;;;kEAOjB,qKAAC;wDAAI,KAAK;;;;;;;6EAGd,qKAAC;gDAAI,WAAU;gDAAkB,OAAO;oDACpC,SAAS;oDACT,eAAe;oDACf,YAAY;oDACZ,gBAAgB;oDAChB,QAAQ;gDACZ;;kEACI,qKAAC;wDAAE,WAAU;wDAAkB,OAAO;4DAAE,UAAU;4DAAQ,SAAS;wDAAI;;;;;;kEACvE,qKAAC;wDAAG,WAAU;kEAAO;;;;;;kEACrB,qKAAC;wDAAE,WAAU;kEAAG;;;;;;;;;;;;;;;;;sDAI5B,qKAAC;4CAAK,WAAU;4CAAkB,UAAU;sDACxC,cAAA,qKAAC;gDAAI,WAAU;;kEACX,qKAAC;wDACG,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,WAAW,CAAC;4DACR,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;gEAClC,EAAE,cAAc;gEAChB,kBAAkB;4DACtB;wDACJ;wDACA,OAAO;4DACH,YAAY,mBAAmB,OAAO;4DACtC,OAAO,mBAAmB,UAAU;4DACpC,QAAQ;4DACR,cAAc;4DACd,SAAS;wDACb;;;;;;kEAEJ,qKAAC;wDACG,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,OAAO;4DACH,cAAc;4DACd,OAAO;4DACP,QAAQ;4DACR,YAAY;4DACZ,SAAS;4DACT,YAAY;4DACZ,gBAAgB;wDACpB;kEAEA,cAAA,qKAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOjC,qKAAC;4BAAI,WAAU;sCACX,cAAA,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAE,WAAU;wCAAmB,OAAO;4CAAE,UAAU;4CAAQ,SAAS;4CAAK,cAAc;wCAAO;;;;;;kDAC9F,qKAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,qKAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,qKAAC;wCACG,WAAU;wCACV,kBAAe;wCACf,kBAAe;wCACf,OAAO;4CACH,cAAc;4CACd,SAAS;4CACT,YAAY;wCAChB;;0DAEA,qKAAC;gDAAE,WAAU;;;;;;4CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStE,qKAAC;gBAAI,WAAU;gBAAQ,IAAG;0BACtB,cAAA,qKAAC;oBAAI,WAAU;8BACX,cAAA,qKAAC;wBAAI,WAAU;;0CACX,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;kDACG,cAAA,qKAAC;sDAAG;;;;;;;;;;;kDAER,qKAAC;kDACG,cAAA,qKAAC;4CAAO,MAAK;4CAAS,WAAU;4CAAuB,mBAAgB;;;;;;;;;;;;;;;;;0CAEzE,qKAAC;;;;;0CACP,qKAAC;gCAAI,WAAU;0CACX,cAAA,qKAAC;;sDACG,qKAAC;4CACG,MAAK;4CACL,MAAK;4CACL,IAAG;4CACH,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,OAAO;gDACH,iBAAiB;gDACjB,YAAY;gDACZ,KAAK;gDACL,QAAQ;4CACZ;4CACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;wCAI3D,wBACI,qKAAC;4CAAI,WAAU;;8DACX,qKAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;oDAClB;;;;;;8DAEJ,qKAAC;8DACG,cAAA,qKAAC;wDACG,WAAU;wDACV,OAAO;4DACH,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,cAAc;wDAClB;;;;;;;;;;;;;;;;iEAKZ;;gDACK,aAAa,MAAM,GAAG,IACnB,aAAa,GAAG,CAAC,CAAA,qBACb,qKAAC;wDAAmB,WAAU;wDAAyD,OAAO;4DAAE,gBAAgB;wDAAgB;;0EAC5H,qKAAC;gEACG,WAAU;gEACV,OAAO;oEACH,QAAQ;gEACZ;;kFAEA,qKAAC,sHAAA,CAAA,UAAK;wEACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;wEAC5B,KAAK,KAAK,IAAI;wEACd,OAAO;wEACP,QAAQ;wEACR,WAAU;wEACV,OAAO;4EAAE,WAAW;wEAAQ;;;;;;kFAEhC,qKAAC;;0FACG,qKAAC;0FAAQ,KAAK,QAAQ;;;;;;0FAAU,qKAAC;;;;;0FACjC,qKAAC;0FAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;;0EAGxB,qKAAC;0EACG,cAAA,qKAAC;oEACG,WAAU;oEACV,SAAS,IAAM,qBAAqB,KAAK,GAAG;8EAC/C;;;;;;;;;;;;uDAxBC,KAAK,GAAG;;;;8EA+BtB,qKAAC;oDAAI,WAAU;;sEACX,qKAAC;4DAAI,WAAU;sEACX,cAAA,qKAAC;gEAAE,WAAU;gEAAiB,OAAO;oEAAE,UAAU;oEAAQ,OAAO;gEAAU;;;;;;;;;;;sEAE9E,qKAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAU;sEAAG;;;;;;;;;;;;gDAKxC,aAAa,MAAM,GAAG,kBAAkB,MAAM,IAAI,CAC/C,WAAW,IAAI,OAAO,KACf,QAAQ,SAAS,CAAC,MAAM,IAAI,IAC7B,MAAM,MAAM,CAAC,CAAA,OACX,QAAQ,SAAS,CAAC,QAAQ,CAAC,KAAK,GAAG,KACnC,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,GAAG,GACpE,MAAM,AAChB,mBACQ,qKAAC;oDAAI,WAAU;8DACX,cAAA,qKAAC;wDAAO,WAAU;wDAAgC,SAAS;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmBvH,qKAAC;gBACG,WAAW,CAAC,WAAW,EAAE,kBAAkB,SAAS,IAAI;gBACxD,IAAG;gBACH,UAAS;gBACT,mBAAgB;gBAChB,eAAa,CAAC;gBACd,OAAO;oBACH,SAAS,kBAAkB,UAAU;oBACrC,iBAAiB,kBAAkB,oBAAoB;gBAC3D;0BAEA,cAAA,qKAAC;oBAAI,WAAU;8BACX,cAAA,qKAAC;wBAAI,WAAU;wBAAgB,OAAO;4BAAE,YAAY,OAAO,UAAU;4BAAE,OAAO,OAAO,KAAK;wBAAC;;0CAEvF,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAI,WAAU;;0DACX,qKAAC;gDAAI,WAAU;;kEACX,qKAAC,sHAAA,CAAA,UAAK;wDACF,KAAK,cAAc,SAAS,qRAAA,CAAA,UAAS;wDACrC,KAAK,cAAc,QAAQ;wDAC3B,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAGd,qKAAC;wDACG,WAAU;wDACV,OAAO;4DACH,QAAQ;4DACR,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,iBAAiB,YAAY,GAAG,CAAC,gBAAgB,YAAY;4DAC7D,cAAc;4DACd,WAAW;wDACf;;;;;;;;;;;;0DAGR,qKAAC;;kEACG,qKAAC;wDAAG,WAAU;kEACT,cAAc,QAAQ;;;;;;kEAE3B,qKAAC;kEACI,YAAY,GAAG,CAAC,gBACX,WACA,eAAe,cAAc,YAAY,IAAI,QAAQ;;;;;;;;;;;;;;;;;;kDAKvE,qKAAC;wCAAI,WAAU;;4CAEV,gBAAgB,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,kBAC9D,qKAAC;gDAAI,WAAU;0DACX,cAAA,qKAAC;oDAAM,OAAO;wDACV,UAAU;wDACV,OAAO,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,EAAE,eAAe,YAAY;oDACzF;8DACK,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,EAAE;;;;;;;;;;;0DAIhE,qKAAC;gDACG,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,cAAW;;;;;;;;;;;;;;;;;;0CAMvB,qKAAC;gCAAI,WAAU;gCAAgC,OAAO;oCAAE,QAAQ;gCAAsB;;kDAClF,qKAAC;wCACG,WAAU;wCACV,OAAO;4CACH,WAAW;4CACX,SAAS;wCACb;kDAEC,gBAAgB,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,kBAC/E;;gDACK,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAC7B,qKAAC;wDAAiB,WAAU;kEAExB,cAAA,qKAAC;4DACG,WAAW,CAAC,OAAO,EAAE,IAAI,MAAM,KAAK,QAAQ,0BAA0B,uBAAuB;4DAC7F,OAAO;gEAAE,OAAO;4DAAO;sEAEvB,cAAA,qKAAC;gEACG,OAAO;oEACH,iBAAiB,IAAI,MAAM,KAAK,QAC1B,YACA;oEACN,OAAO,IAAI,MAAM,KAAK,QAAQ,SAAS;oEACvC,SAAS;oEACT,cAAc,IAAI,MAAM,KAAK,QACvB,uBACA;oEACN,UAAU;oEACV,UAAU;oEACV,WAAW;oEACX,UAAU;gEACd;;kFAEA,qKAAC;wEAAI,OAAO;4EACR,UAAU;4EACV,YAAY;4EACZ,cAAc;4EACd,WAAW,IAAI,MAAM,KAAK,QAAQ,SAAS;wEAC/C;kFACK,IAAI,IAAI;;;;;;kFAEb,qKAAC;wEAAI,OAAO;4EACR,UAAU;4EACV,SAAS;4EACT,WAAW,IAAI,MAAM,KAAK,QAAQ,SAAS;4EAC3C,WAAW;wEACf;;4EACK,IAAI,SAAS;4EACb,IAAI,MAAM,KAAK,uBACZ,qKAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAM;0FAC5B,IAAI,SAAS,GAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;;uDAvCxC,IAAI,EAAE;;;;;gDAiDnB,gBAAgB,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,IACjE,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,EAAE,8BAChD,qKAAC;oDAAI,WAAU;8DACX,cAAA,qKAAC;wDAAI,WAAU;kEACX,cAAA,qKAAC;4DACG,OAAO;gEACH,iBAAiB;gEACjB,OAAO;gEACP,SAAS;gEACT,cAAc;gEACd,QAAQ;gEACR,WAAW;gEACX,UAAU;4DACd;sEAEA,cAAA,qKAAC;gEACG,WAAU;gEACV,OAAO;oEACH,UAAU;oEACV,SAAS;oEACT,cAAc;gEAClB;gEACA,SAAS;oEACL,MAAM,WAAW,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC;oEAClE,IAAI,QAAQ,6EAA6E;wEACrF,WAAW,SAAS,MAAM,EAAE,eAAe,aAAa,GAAG;oEAC/D;gEACJ;0EACH;;;;;;;;;;;;;;;;;;;;;gDAShB,YAAY,GAAG,CAAC,+BACb,qKAAC;oDAAI,WAAU;8DACX,cAAA,qKAAC;wDAAI,WAAU;wDAA6B,OAAO;4DAAE,OAAO;wDAAO;kEAC/D,cAAA,qKAAC;4DACG,OAAO;gEACH,iBAAiB;gEACjB,OAAO;gEACP,SAAS;gEACT,cAAc;gEACd,UAAU;gEACV,WAAW;4DACf;sEAEA,cAAA,qKAAC;gEAAI,WAAU;;kFACX,qKAAC;wEAAM,OAAO;4EACV,OAAO;4EACP,UAAU;4EACV,WAAW;4EACX,aAAa;wEACjB;kFAAG;;;;;;kFAGH,qKAAC;wEAAK,WAAU;;0FACZ,qKAAC;;;;;0FACD,qKAAC;;;;;0FACD,qKAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAQzB,qKAAC;oDAAI,KAAK;;;;;;;yEAGd,qKAAC;4CAAI,WAAU;;8DACX,qKAAC;oDAAE,WAAU;oDAAkB,OAAO;wDAAE,UAAU;wDAAQ,SAAS;oDAAI;;;;;;8DACvE,qKAAC;oDAAG,WAAU;8DAAO;;;;;;8DACrB,qKAAC;oDAAE,WAAU;8DAAG;;;;;;;;;;;;;;;;;kDAM5B,qKAAC;wCAAI,WAAU;wCAAQ,OAAO;4CAAE,SAAS;wCAAU;kDAC9C,aAAa,GAAG,CAAC,GAAG,cAAc,CAAC,EAAE,cAAc,KAAK,kBACrD,qKAAC;4CAAI,WAAU;;8DACX,qKAAC;oDAAE,WAAU;8DAAkB;;;;;;8DAC/B,qKAAC;oDACG,WAAU;oDACV,SAAS;wDACL,MAAM,WAAW,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC;wDAClE,IAAI,UAAU;4DACV,YAAY,SAAS,MAAM,EAAE,eAAe,aAAa,GAAG;wDAChE;oDACJ;8DACH;;;;;;;;;;;iEAKL,qKAAC;4CAAK,UAAU;sDACZ,cAAA,qKAAC;gDAAI,WAAU;gDAA4B,OAAO;oDAAE,KAAK;gDAAM;;kEAC3D,qKAAC;wDACG,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,WAAW,CAAC;4DACR,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;gEAClC,EAAE,cAAc;gEAChB,kBAAkB;4DACtB;wDACJ;wDACA,OAAO;4DACH,cAAc;4DACd,SAAS;4DACT,QAAQ;4DACR,UAAU;4DACV,iBAAiB;4DACjB,MAAM;wDACV;;;;;;kEAEJ,qKAAC;wDACG,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,OAAO;4DACH,cAAc;4DACd,OAAO;4DACP,QAAQ;4DACR,SAAS;4DACT,YAAY;4DACZ,gBAAgB;4DAChB,SAAS;4DACT,iBAAiB;4DACjB,QAAQ;wDACZ;kEAEA,cAAA,qKAAC;4DAAE,WAAU;4DAAkB,OAAO;gEAAE,UAAU;4DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzG", "debugId": null}}]}