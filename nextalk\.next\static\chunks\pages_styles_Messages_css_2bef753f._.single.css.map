{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/pages/styles/Messages.css"], "sourcesContent": [".chat-container {\r\n  display: flex;\r\n  height: 100%;\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.chat-list {\r\n  width: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  border-right: 1px solid rgba(160, 174, 192, 0.2);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.chat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  border-radius: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-item:hover {\r\n  background: rgba(124, 135, 151, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n}\r\n\r\n.avatar {\r\n  border-radius: 50%;\r\n  border: 2px solid #a0aec0;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-item:hover .avatar {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: 15px;\r\n  height: 15px;\r\n  border-radius: 50%;\r\n  border: 2px solid #2d3748;\r\n  box-shadow: 0 0 5px #48bb78;\r\n}\r\n\r\n.chat-details {\r\n  margin-left: 15px;\r\n  flex: 1;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.user-name {\r\n  font-weight: bold;\r\n  font-size: 0.875rem;\r\n  text-transform: capitalize;\r\n}\r\n\r\n.timestamp {\r\n  font-size: 0.75rem;\r\n  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.last-message {\r\n  font-size: 0.75rem;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.reaction {\r\n  color: #f56565;\r\n  animation: pulse 1.5s infinite;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.chat-panel {\r\n  display: none;\r\n  width: 66.666667%;\r\n  padding: 24px;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* Desktop - Show both list and panel */\r\n@media (min-width: 1024px) {\r\n  .chat-list {\r\n    width: 33.333333%;\r\n    display: block;\r\n  }\r\n  .chat-panel {\r\n    display: flex;\r\n    width: 66.666667%;\r\n  }\r\n}\r\n\r\n/* Mobile - Bootstrap Modal Approach */\r\n@media (max-width: 1023px) {\r\n  /* Main container stays normal on mobile */\r\n  .chat-container {\r\n    position: relative;\r\n  }\r\n\r\n  /* Chat list takes full width on mobile */\r\n  .chat-list {\r\n    width: 100% !important;\r\n    display: block;\r\n  }\r\n\r\n  /* Hide desktop chat panel on mobile - use modal instead */\r\n  .chat-panel {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n/* Bootstrap Modal Styling for Mobile Chat */\r\n.modal-fullscreen .modal-content {\r\n  border-radius: 0;\r\n  border: none;\r\n}\r\n\r\n.modal-fullscreen .modal-header {\r\n  padding: 1rem;\r\n  min-height: 60px;\r\n}\r\n\r\n.modal-fullscreen .modal-body {\r\n  padding: 0;\r\n}\r\n\r\n/* Mobile Chat Message Styling in Modal */\r\n.modal .message {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.modal .rounded-3 {\r\n  border-radius: 1rem !important;\r\n  max-width: 70%;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.modal .bg-primary {\r\n  background-color: #3b82f6 !important;\r\n}\r\n\r\n.modal .bg-light {\r\n  background-color: rgba(255, 255, 255, 0.9) !important;\r\n  color: #333 !important;\r\n}\r\n\r\n/* Message alignment in modal */\r\n.modal .justify-content-end {\r\n  justify-content: flex-end !important;\r\n}\r\n\r\n.modal .justify-content-start {\r\n  justify-content: flex-start !important;\r\n}\r\n\r\n/* Input section styling */\r\n.modal .border-top {\r\n  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;\r\n}\r\n\r\n.modal .form-control {\r\n  border: 1px solid rgba(0, 0, 0, 0.1) !important;\r\n  border-radius: 25px !important;\r\n}\r\n\r\n.modal .btn-primary {\r\n  background-color: #3b82f6 !important;\r\n  border-color: #3b82f6 !important;\r\n}\r\n\r\n.chat-window, .instructions {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);\r\n  animation: fadeIn 0.5s ease-in;\r\n}\r\n\r\n.chat-header {\r\n  font-size: 1.125rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.chat-content, .instruction-text {\r\n  margin-top: 1rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.icon-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chat-icon {\r\n  font-size: 3rem;\r\n  display: inline-block;\r\n  padding: 4px;\r\n  width: 80px;\r\n  height: 80px;\r\n  line-height: 80px;\r\n  border: 2px solid #fff;\r\n  border-radius: 50%;\r\n  text-align: center;\r\n}\r\n\r\n.instruction-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: inherit;\r\n}\r\n\r\n.instruction-text {\r\n  font-size: 1rem;\r\n  opacity: 0.7;\r\n  margin-bottom: 25px;\r\n  color: inherit;\r\n}\r\n\r\n.instruction-content {\r\n  padding: 40px 20px;\r\n}\r\n\r\n.instructions {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.send-button {\r\n  background-color: #6366f1;\r\n  color: #fff;\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.send-button:hover {\r\n  background-color: #4f46e5;\r\n}\r\n\r\n.instructions{\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n@keyframes pulse {\r\n  0% { transform: scale(1); }\r\n  50% { transform: scale(1.2); }\r\n  100% { transform: scale(1); }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}\r\n\r\n/* Instagram-Style Typing Indicator Animation */\r\n.typing-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 3px;\r\n}\r\n\r\n.typing-indicator span {\r\n    width: 6px;\r\n    height: 6px;\r\n    border-radius: 50%;\r\n    background-color: #667eea;\r\n    animation: instagramTyping 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-indicator span:nth-child(1) {\r\n    animation-delay: -0.32s;\r\n}\r\n\r\n.typing-indicator span:nth-child(2) {\r\n    animation-delay: -0.16s;\r\n}\r\n\r\n.typing-indicator span:nth-child(3) {\r\n    animation-delay: 0s;\r\n}\r\n\r\n@keyframes instagramTyping {\r\n    0%, 80%, 100% {\r\n        transform: scale(0.6);\r\n        opacity: 0.4;\r\n    }\r\n    40% {\r\n        transform: scale(1);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Instagram-Style Message Bubbles */\r\n.message-bubble {\r\n    position: relative;\r\n    animation: messageSlideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes messageSlideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(10px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Instagram-Style Modal */\r\n.modal-fullscreen .modal-content {\r\n    border-radius: 0;\r\n    border: none;\r\n    overflow: hidden;\r\n}\r\n\r\n.modal-fullscreen .modal-header {\r\n    border: none;\r\n}\r\n\r\n.modal-fullscreen .modal-body {\r\n    padding: 0;\r\n}\r\n\r\n/* Smooth Scrolling */\r\n.modal-body .overflow-auto {\r\n    scroll-behavior: smooth;\r\n    -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n.modal-body .overflow-auto::-webkit-scrollbar {\r\n    width: 4px;\r\n}\r\n\r\n.modal-body .overflow-auto::-webkit-scrollbar-track {\r\n    background: transparent;\r\n}\r\n\r\n.modal-body .overflow-auto::-webkit-scrollbar-thumb {\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 2px;\r\n}\r\n\r\n/* Input Focus Effects */\r\n.form-control:focus {\r\n    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;\r\n    border-color: transparent !important;\r\n}\r\n\r\n/* Mobile Chat Modal Styles */\r\n.mobile-chat-modal .modal-dialog {\r\n    margin: 0;\r\n    max-width: 100%;\r\n    height: 100vh;\r\n}\r\n\r\n.mobile-chat-modal .modal-content {\r\n    height: 100vh;\r\n    border-radius: 0;\r\n    border: none;\r\n}\r\n\r\n.mobile-chat-modal .modal-header {\r\n    border-bottom: 1px solid #e0e0e0;\r\n    padding: 12px 16px;\r\n    background: #fff;\r\n}\r\n\r\n.mobile-chat-modal .modal-body {\r\n    padding: 0 !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: calc(100vh - 120px);\r\n    background: #f8f9fa;\r\n}\r\n\r\n/* Remove any default Bootstrap gaps */\r\n.mobile-chat-modal .container-fluid,\r\n.mobile-chat-modal .row,\r\n.mobile-chat-modal .col {\r\n    padding: 0 !important;\r\n    margin: 0 !important;\r\n}\r\n\r\n/* Ensure full width for chat input */\r\n.mobile-chat-modal .form-control {\r\n    width: 100%;\r\n    box-sizing: border-box;\r\n}\r\n\r\n/* Typing Indicator Animation */\r\n.typing-indicator {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.typing-indicator span {\r\n    width: 4px;\r\n    height: 4px;\r\n    border-radius: 50%;\r\n    background-color: #28a745;\r\n    animation: typing 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-indicator span:nth-child(1) {\r\n    animation-delay: 0s;\r\n}\r\n\r\n.typing-indicator span:nth-child(2) {\r\n    animation-delay: 0.2s;\r\n}\r\n\r\n.typing-indicator span:nth-child(3) {\r\n    animation-delay: 0.4s;\r\n}\r\n\r\n@keyframes typing {\r\n    0%, 60%, 100% {\r\n        transform: translateY(0);\r\n        opacity: 0.5;\r\n    }\r\n    30% {\r\n        transform: translateY(-8px);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Scroll to Bottom Button Animation */\r\n.scroll-to-bottom {\r\n    animation: fadeInUp 0.3s ease-out;\r\n}\r\n\r\n@keyframes fadeInUp {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Mobile message alignment */\r\n@media (max-width: 768px) {\r\n    .mobile-chat-modal .d-flex {\r\n        width: 100% !important;\r\n    }\r\n\r\n    .mobile-chat-modal .justify-content-end {\r\n        padding-right: 0 !important;\r\n    }\r\n\r\n    .mobile-chat-modal .justify-content-start {\r\n        padding-left: 0 !important;\r\n    }\r\n\r\n    .chat-container {\r\n        padding: 0;\r\n    }\r\n\r\n    .user-list {\r\n        border-right: none;\r\n    }\r\n\r\n    .chat-area {\r\n        border-radius: 0;\r\n    }\r\n\r\n    .message-input {\r\n        padding: 8px;\r\n    }\r\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAQA;EACE;;;;;EAIA;;;;;;AAOF;EAEE;;;;EAKA;;;;;EAMA;;;;;AAMF;;;;;AAKA;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;AAMA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;;;AAMA;;;;;;;;;;AAgCA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAMA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AASA;;;;;AAQA;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;;;;;;;;AAYA;EACI;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA"}}]}