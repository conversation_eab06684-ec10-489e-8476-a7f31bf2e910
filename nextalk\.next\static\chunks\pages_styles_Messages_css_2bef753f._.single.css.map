{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/pages/styles/Messages.css"], "sourcesContent": [".chat-container {\r\n  display: flex;\r\n  height: 100%;\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.chat-list {\r\n  width: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  border-right: 1px solid rgba(160, 174, 192, 0.2);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.chat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  border-radius: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-item:hover {\r\n  background: rgba(124, 135, 151, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n}\r\n\r\n.avatar {\r\n  border-radius: 50%;\r\n  border: 2px solid #a0aec0;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-item:hover .avatar {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: 15px;\r\n  height: 15px;\r\n  border-radius: 50%;\r\n  border: 2px solid #2d3748;\r\n  box-shadow: 0 0 5px #48bb78;\r\n}\r\n\r\n.chat-details {\r\n  margin-left: 15px;\r\n  flex: 1;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.user-name {\r\n  font-weight: bold;\r\n  font-size: 0.875rem;\r\n  text-transform: capitalize;\r\n}\r\n\r\n.timestamp {\r\n  font-size: 0.75rem;\r\n  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.last-message {\r\n  font-size: 0.75rem;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.reaction {\r\n  color: #f56565;\r\n  animation: pulse 1.5s infinite;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.chat-panel {\r\n  display: none;\r\n  width: 66.666667%;\r\n  padding: 24px;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* Desktop - Show both list and panel */\r\n@media (min-width: 1024px) {\r\n  .chat-list {\r\n    width: 33.333333%;\r\n    display: block;\r\n  }\r\n  .chat-panel {\r\n    display: flex;\r\n    width: 66.666667%;\r\n  }\r\n}\r\n\r\n/* Mobile - Responsive behavior */\r\n@media (max-width: 1023px) {\r\n  .chat-container {\r\n    position: relative;\r\n    padding: 0; /* Remove padding for full width */\r\n    margin: 0; /* Remove margin for full width */\r\n  }\r\n\r\n  /* When no chat is selected, show only chat list */\r\n  .chat-list {\r\n    width: 100%;\r\n    display: block;\r\n    padding: 0; /* Remove padding for full width */\r\n    margin: 0; /* Remove margin for full width */\r\n  }\r\n\r\n  .chat-panel {\r\n    display: none;\r\n  }\r\n\r\n  /* When chat is selected, hide list and show panel */\r\n  .chat-list.chat-list-with-panel {\r\n    display: none;\r\n  }\r\n\r\n  .chat-panel.chat-panel-active {\r\n    display: flex;\r\n    width: 100vw; /* Full viewport width */\r\n    height: 100vh; /* Full viewport height */\r\n    position: fixed; /* Fixed positioning for full screen */\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: inherit;\r\n    z-index: 1000; /* Higher z-index */\r\n    padding: 0; /* No padding */\r\n    margin: 0; /* No margin */\r\n  }\r\n\r\n  /* Mobile chat input styling - Full width */\r\n  .chat-input-form {\r\n    padding: 10px 15px; /* Minimal padding */\r\n    background: inherit;\r\n    border-top: 1px solid rgba(0, 0, 0, 0.1);\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    width: 100%;\r\n    z-index: 1001;\r\n  }\r\n\r\n  .chat-input-form .input-group {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .chat-input {\r\n    flex: 1;\r\n    border-radius: 20px !important;\r\n    padding: 10px 16px !important;\r\n    border: 1px solid rgba(0, 0, 0, 0.1) !important;\r\n  }\r\n\r\n  .chat-send-btn {\r\n    width: 40px !important;\r\n    height: 40px !important;\r\n    border-radius: 50% !important;\r\n    padding: 0 !important;\r\n    display: flex !important;\r\n    align-items: center !important;\r\n    justify-content: center !important;\r\n  }\r\n\r\n  /* Mobile chat messages - Full width */\r\n  .chat-messages {\r\n    padding: 0 15px !important; /* Minimal side padding */\r\n    height: calc(100vh - 140px) !important; /* Account for header and input */\r\n    width: 100%;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  /* Mobile chat header - Full width */\r\n  .chat-header {\r\n    padding: 15px;\r\n    border-bottom: 1px solid rgba(0, 0, 0, 0.1);\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    width: 100%;\r\n    background: inherit;\r\n    z-index: 1001;\r\n  }\r\n\r\n  /* Mobile chat window - Full width */\r\n  .chat-window {\r\n    width: 100% !important;\r\n    height: 100vh !important;\r\n    padding: 0 !important;\r\n    margin: 0 !important;\r\n    padding-top: 80px !important; /* Space for fixed header */\r\n    padding-bottom: 80px !important; /* Space for fixed input */\r\n  }\r\n\r\n  /* Mobile user list items */\r\n  .chat-item {\r\n    padding: 12px 15px;\r\n    margin: 0;\r\n    border-radius: 0; /* Remove border radius for full width feel */\r\n    border-bottom: 1px solid rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n\r\n.chat-window, .instructions {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);\r\n  animation: fadeIn 0.5s ease-in;\r\n}\r\n\r\n.chat-header {\r\n  font-size: 1.125rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.chat-content, .instruction-text {\r\n  margin-top: 1rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.icon-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chat-icon {\r\n  font-size: 3rem;\r\n  display: inline-block;\r\n  padding: 4px;\r\n  width: 80px;\r\n  height: 80px;\r\n  line-height: 80px;\r\n  border: 2px solid #fff;\r\n  border-radius: 50%;\r\n  text-align: center;\r\n}\r\n\r\n.instruction-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.instruction-text {\r\n  font-size: 1rem;\r\n  color: #ccc;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.send-button {\r\n  background-color: #6366f1;\r\n  color: #fff;\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.send-button:hover {\r\n  background-color: #4f46e5;\r\n}\r\n\r\n.instructions{\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n@keyframes pulse {\r\n  0% { transform: scale(1); }\r\n  50% { transform: scale(1.2); }\r\n  100% { transform: scale(1); }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAQA;EACE;;;;;EAIA;;;;;;AAOF;EACE;;;;;;EAOA;;;;;;;EAOA;;;;EASA;;;;;;;;;;;;EAgBA;;;;;;;;;;;;EAYA;;;;;;;EAOA;;;;;;;EAOA;;;;;;;;;;EAWA;;;;;;;EAQA;;;;;;;;;;;;EAaA;;;;;;;EAUA;;;;;;;;AAQF;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;;;AAMA"}}]}