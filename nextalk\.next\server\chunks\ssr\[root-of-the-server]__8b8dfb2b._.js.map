{"version": 3, "sources": [], "sections": [{"offset": {"line": 17, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/axiosConfig.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create an instance\r\nconst axiosInstance = axios.create({\r\n    baseURL: process.env.REACT_APP_API_URL, // Backend URL\r\n    withCredentials: true,           // Crucial for sending cookies\r\n    headers: {\r\n        \"Content-Type\": \"application/json\",\r\n    },\r\n});\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,qBAAqB;AACrB,MAAM,gBAAgB,0GAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,QAAQ,GAAG,CAAC,iBAAiB;IACtC,iBAAiB;IACjB,SAAS;QACL,gBAAgB;IACpB;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/public/Images/predefine.webp.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 200, height: 200, blurDataURL: \"data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA2S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Components/DashboardLayout.js"], "sourcesContent": ["// DashboardLayout.jsx\r\n'use client'; // if you're using App Router (recommended)\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from 'next/image';\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport axios from '../../utils/axiosConfig';\r\n\r\nexport default function DashboardLayout({ children }) {\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const { theme, handleThemeClick } = useTheme();\r\n    const router = useRouter(); // corrected here\r\n\r\n    const toggleTheme = () => {\r\n        const newTheme = theme === 'dark' ? 'light' : 'dark';\r\n        handleThemeClick(newTheme);\r\n    };\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return { background: '#0f172a', color: '#e2e8f0', chatBackground: '#1e293b', cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)' };\r\n        }\r\n        return { background: '#f1f5f9', color: '#1e293b', chatBackground: '#ffffff' };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyles();\r\n    const [user, setUser] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const storedUser = sessionStorage.getItem(\"user\");\r\n        if (!storedUser) {\r\n            router.push(\"/\");\r\n        } else {\r\n            try {\r\n                const parsed = JSON.parse(storedUser);\r\n                setUser(parsed.user);\r\n            } catch (err) {\r\n                console.error(\"Error parsing sessionStorage user:\", err);\r\n                router.push(\"/\");\r\n            }\r\n        }\r\n    }, [router]);\r\n\r\n    const [profile, setProfile] = useState(null);\r\n\r\n    const [tempProfile, setTempProfile] = useState(profile);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchProfile = async () => {\r\n        setLoading(true);\r\n        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');\r\n\r\n        if (!userData?.user?.id) {\r\n            setError('No user data found. Please log in.');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const response = await axios.get('https://nextalk-u0y1.onrender.com/profile', {\r\n                headers: {\r\n                    Authorization: `Bearer ${userData.user.id}`,\r\n                },\r\n            });\r\n            const fetchedProfile = response.data.user || response.data;\r\n            setProfile(fetchedProfile);\r\n            setTempProfile(fetchedProfile);\r\n            setLoading(false);\r\n        } catch (err) {\r\n            const errorMessage = err.response?.data?.message || 'Failed to load profile.';\r\n            setError(errorMessage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (typeof window !== 'undefined') {\r\n            fetchProfile();\r\n        }\r\n    }, []);\r\n\r\n\r\n    const pathname = usePathname();\r\n    const [brandText, setBrandText] = useState(\"Nextalk\");\r\n    useEffect(() => {\r\n        if (pathname === \"/Dashboard/Profile\") {\r\n            setBrandText(profile?.name || \"User\");\r\n        } else {\r\n            setBrandText(\"Nextalk\");\r\n        }\r\n    }, [pathname, profile]);\r\n\r\n\r\n    const [showConfirm, setShowConfirm] = useState(false);\r\n    const handleLogout = () => {\r\n        setShowConfirm(true);\r\n    };\r\n\r\n    const handleConfirmUpload = async (e) => {\r\n        sessionStorage.removeItem(\"user\");\r\n        router.push(\"/\");\r\n    }\r\n\r\n    const [pendingCount, setPendingCount] = useState(0);\r\n\r\n    useEffect(() => {\r\n        const fetchPendingRequests = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n\r\n            if (!sessionId) return;\r\n\r\n            try {\r\n                const res = await axios.get(`https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`);\r\n                setPendingCount(res.data.count);\r\n            } catch (err) {\r\n                console.error(\"❌ Failed to fetch pending request count:\", err);\r\n            }\r\n        };\r\n\r\n        fetchPendingRequests();\r\n\r\n        // OPTIONAL: Poll every 30s for updates\r\n        const interval = setInterval(fetchPendingRequests, 30000);\r\n\r\n        return () => clearInterval(interval); // cleanup on unmount\r\n    }, []);\r\n\r\n    const [users, setUsers] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                const response = await axios.post(\r\n                    \"https://nextalk-u0y1.onrender.com/displayusersProfile\",\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true\r\n                    }\r\n                );\r\n\r\n                const allUsers = response.data;\r\n                const filtered = allUsers.filter(user => user._id !== sessionId);\r\n                setUsers(filtered);\r\n            } catch (error) {\r\n                console.error(\"Error fetching users:\", error);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (searchTerm.trim() === '') {\r\n            setSearchResults([]);\r\n            return;\r\n        }\r\n\r\n        const results = users.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setSearchResults(results);\r\n    }, [searchTerm, users]);\r\n\r\n\r\n    return (\r\n        <div className=\"dashboard-wrapper\" style={{ background: currentThemeStyles.background, color: currentThemeStyles.color }}>\r\n            {/* Mobile Navbar */}\r\n            <nav className={`navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`}>\r\n                <div className=\"container-fluid\">\r\n                    <Link className=\"navbar-brand fw-bold sleek-brand\" style={{ textDecoration: \"none\" }} href=\"/dashboard/profile\">{brandText}</Link>\r\n                    <button\r\n                        className={`navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`}\r\n                        type=\"button\"\r\n                        onClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n                    >\r\n                        <span className=\"navbar-toggler-icon\"></span>\r\n                    </button>\r\n                </div>\r\n            </nav>\r\n\r\n            {/* Sidebar */}\r\n            <aside className={`sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`}>\r\n                <div className=\"sidebar-header sleek-header\">\r\n                    <h4 className=\"p-3 fw-bold text-uppercase d-none d-lg-block\">{brandText}</h4>\r\n                </div>\r\n\r\n                <ul className=\"nav flex-column p-3 sleek-nav\">\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard\">\r\n                            <i className=\"bi bi-house-fill me-2\"></i>Home\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <button className=\"nav-link sleek-nav-link w-100\" data-bs-toggle=\"offcanvas\" data-bs-target=\"#Search\" style={{ textDecoration: \"none\" }}>\r\n                            <i className=\"bi bi-search me-2\"></i>Search\r\n                        </button>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-box me-2\"></i>Chat with Random\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-plus-square me-2\"></i>Create\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Messages\">\r\n                            <i className=\"bi bi-chat-fill me-2\"></i>Messages\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-cpu me-2\"></i>Chat with NexTalk\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item ot-but\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Settings\">\r\n                            <i className=\"bi bi-gear-wide-connected me-2\"></i>Settings\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link justify-content-between\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Notification\">\r\n                            <div>\r\n                                <i className=\"bi bi-heart me-2\"></i>Notification\r\n                            </div>\r\n                            {pendingCount > 0 && (\r\n                                <span\r\n                                    style={{\r\n                                        backgroundColor: \"#008080\",\r\n                                        color: \"white\",\r\n                                        fontSize: \"0.7rem\",\r\n                                        padding: \"6px 12px\",\r\n                                        borderRadius: \"50%\",\r\n                                        top: \"10px\",\r\n                                        right: \"10px\",\r\n                                    }}\r\n                                >\r\n                                    {pendingCount}\r\n                                </span>\r\n                            )}\r\n                        </Link>\r\n                    </li><br />\r\n                    <li className='nav-item'>\r\n                        <Link href=\"\"\r\n                            className=\"btn btn-primary w-100 p-2 d-lg-none\" style={{ textDecoration: \"none\" }}\r\n                            type=\"button\"\r\n                            onClick={() => setIsSidebarOpen(false)}\r\n                        >Close </Link></li>\r\n                </ul>\r\n\r\n\r\n                <div className=\"sidebar-footer p-3 sleek-footer\">\r\n                    {loading ? (\r\n                        <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ background: \"darkgray\", textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                            <div\r\n                                className=\"skeleton\"\r\n                                style={{\r\n                                    width: \"45px\",\r\n                                    height: \"45px\",\r\n                                    borderRadius: \"50%\",\r\n                                }}\r\n                            ></div>\r\n                            <div>\r\n                                <div\r\n                                    className=\"skeleton\"\r\n                                    style={{\r\n                                        width: \"120px\",\r\n                                        height: \"16px\",\r\n                                        borderRadius: \"4px\",\r\n                                        marginBottom: \"8px\",\r\n                                    }}\r\n                                ></div>\r\n                                <span className=\"sleek-status online\">Online</span>\r\n                            </div>\r\n                        </Link>\r\n                    ) :\r\n                        (\r\n                            <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                                {profile && (\r\n                                    <Image\r\n                                        key={profile.image}\r\n                                        src={profile.image || \"/Images/predefine.webp\"}\r\n                                        width={45}\r\n                                        height={45}\r\n                                        alt=\"User\"\r\n                                        className=\"rounded-circle sleek-avatar\"\r\n                                    />\r\n                                )}\r\n                                <div>\r\n                                    <span className=\"d-block fw-semibold sleek-username\">{profile.name || \"Guest\"}</span>\r\n                                    <span className=\"sleek-status online\">Online</span>\r\n                                </div>\r\n                            </Link>\r\n                        )\r\n                    }\r\n                    <div className=\"mt-4 sleek-actions\">\r\n                        <button\r\n                            onClick={toggleTheme}\r\n                            className=\"btn sleek-btn theme-toggle w-100 mb-2\"\r\n                        >\r\n                            {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\r\n                        </button>\r\n                        <button\r\n                            onClick={handleLogout}\r\n                            className=\"btn sleek-btn logout-btn w-100\"\r\n                        >\r\n                            Log Out\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </aside>\r\n\r\n            {/* Main Content */}\r\n            <main className=\"main-content sleek-main\" style={{ background: currentThemeStyles.chatBackground }}>\r\n                <div className=\"container-fluid p-1 sleek-container\">\r\n                    {children} {/* NOT Outlet! */}\r\n                </div>\r\n                {showConfirm && (\r\n                    <div className=\"modal-overlay\">\r\n                        <div className=\"modal-content\">\r\n                            <h4 className=\"modal-title\">Log Out?</h4>\r\n                            <p className=\"modal-text\">\r\n                                Are you sure you want to log out? You will be signed out of your account and redirected to the login page.\r\n                            </p>\r\n\r\n                            <div className=\"modal-actions\">\r\n                                <button className=\"modal-btn modal-btn-success\" onClick={handleConfirmUpload}>\r\n                                    ✅ Confirm !\r\n                                </button>\r\n                                <button className=\"modal-btn modal-btn-cancel\" onClick={() => setShowConfirm(false)}>\r\n                                    ❌ Cancel\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n                <div className=\"offcanvas offcanvas-start\" style={{ background: currentThemeStyles.cardBg, color: currentThemeStyles.color }} id=\"Search\">\r\n                    <div className=\"offcanvas-header\">\r\n                        <h3 className=\"offcanvas-title\">Search</h3>\r\n                        <button type=\"button\" className=\"btn-close bg-danger\" data-bs-dismiss=\"offcanvas\"></button>\r\n                    </div>\r\n                    <div className=\"offcanvas-body\">\r\n                        <input\r\n                            type=\"search\"\r\n                            name=\"search\"\r\n                            id=\"search\"\r\n                            className=\"form-control mb-3\"\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            style={{\r\n                                backgroundColor: \"white\",\r\n                                transition: \"background 0.3s\",\r\n                                gap: \"10px\",\r\n                                border: \"1px solid #333\"\r\n                            }}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                            onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                        />\r\n                        {\r\n                            loading ? (\r\n                                <div className='d-flex gap-4'>\r\n                                    <div\r\n                                        className=\"skeleton\"\r\n                                        style={{\r\n                                            width: \"45px\",\r\n                                            height: \"45px\",\r\n                                            borderRadius: \"50%\",\r\n                                        }}\r\n                                    ></div>\r\n                                    <div>\r\n                                        <div\r\n                                            className=\"skeleton\"\r\n                                            style={{\r\n                                                width: \"120px\",\r\n                                                height: \"16px\",\r\n                                                borderRadius: \"4px\",\r\n                                                marginBottom: \"8px\",\r\n                                            }}\r\n                                        ></div>\r\n                                    </div>\r\n                                </div>\r\n                            ) : (\r\n                                <div>\r\n                                    {searchResults.map(user => (\r\n                                        <div\r\n                                            key={user._id}\r\n                                            className=\"d-flex gap-4 align-items-center user-result mb-2 p-2 rounded\"\r\n                                            style={{\r\n                                                cursor: \"pointer\",\r\n                                            }}\r\n                                        >\r\n                                            <Image\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={60}\r\n                                                height={60}\r\n                                                className=\"rounded-circle\"\r\n                                                style={{ objectFit: \"cover\" }}\r\n                                            />\r\n                                            <div>\r\n                                                <strong>{user.username}</strong><br />\r\n                                                <span>{user.name}</span>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                    ))}\r\n\r\n                                    {searchResults.length === 0 && searchTerm && (\r\n                                        <div className=\"text-center mt-4 fade-in\">\r\n                                            <svg\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                width=\"64\"\r\n                                                height=\"64\"\r\n                                                fill=\"gray\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                style={{ opacity: 0.5 }}\r\n                                            >\r\n                                                <path d=\"M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z\" />\r\n                                            </svg>\r\n                                            <p className=\"mt-2\">No users found</p>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            )\r\n                        }\r\n                    </div>\r\n                </div>\r\n\r\n            </main>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AARA,cAAc,2CAA2C;;;;;;;;;AAU1C,SAAS,gBAAgB,EAAE,QAAQ,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,2HAAA,CAAA,YAAS,AAAD,KAAK,iBAAiB;IAE7C,MAAM,cAAc;QAChB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,iBAAiB;IACrB;IAEA,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBAAE,YAAY;gBAAW,OAAO;gBAAW,gBAAgB;gBAAW,QAAQ;YAAoD;QAC7I;QACA,OAAO;YAAE,YAAY;YAAW,OAAO;YAAW,gBAAgB;QAAU;IAChF;IAEA,MAAM,qBAAqB;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa,eAAe,OAAO,CAAC;QAC1C,IAAI,CAAC,YAAY;YACb,OAAO,IAAI,CAAC;QAChB,OAAO;YACH,IAAI;gBACA,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,QAAQ,OAAO,IAAI;YACvB,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,IAAI,CAAC;YAChB;QACJ;IACJ,GAAG;QAAC;KAAO;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe;QACjB,WAAW;QACX,MAAM,WAAW,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,WAAW;QAE9D,IAAI,CAAC,UAAU,MAAM,IAAI;YACrB,SAAS;YACT,WAAW;YACX;QACJ;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,6CAA6C;gBAC1E,SAAS;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE;gBAC/C;YACJ;YACA,MAAM,iBAAiB,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YAC1D,WAAW;YACX,eAAe;YACf,WAAW;QACf,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YACT,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,uCAAmC;;QAEnC;IACJ,GAAG,EAAE;IAGL,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,aAAa,sBAAsB;YACnC,aAAa,SAAS,QAAQ;QAClC,OAAO;YACH,aAAa;QACjB;IACJ,GAAG;QAAC;QAAU;KAAQ;IAGtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;QACjB,eAAe;IACnB;IAEA,MAAM,sBAAsB,OAAO;QAC/B,eAAe,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC;IAChB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,uBAAuB;YACzB,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YAEpC,IAAI,CAAC,WAAW;YAEhB,IAAI;gBACA,MAAM,MAAM,MAAM,6GAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,0DAA0D,EAAE,WAAW;gBACpG,gBAAgB,IAAI,IAAI,CAAC,KAAK;YAClC,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,4CAA4C;YAC9D;QACJ;QAEA;QAEA,uCAAuC;QACvC,MAAM,WAAW,YAAY,sBAAsB;QAEnD,OAAO,IAAM,cAAc,WAAW,qBAAqB;IAC/D,GAAG,EAAE;IAEL,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,aAAa;YACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YACpC,iBAAiB;YAEjB,IAAI;gBACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;oBACI,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,iBAAiB;gBACrB;gBAGJ,MAAM,WAAW,SAAS,IAAI;gBAC9B,MAAM,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBACtD,SAAS;YACb,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,yBAAyB;YAC3C;QACJ;QAEA;IACJ,GAAG,EAAE;IAEL,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,WAAW,IAAI,OAAO,IAAI;YAC1B,iBAAiB,EAAE;YACnB;QACJ;QAEA,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAG3D,iBAAiB;IACrB,GAAG;QAAC;QAAY;KAAM;IAGtB,qBACI,qKAAC;QAAI,WAAU;QAAoB,OAAO;YAAE,YAAY,mBAAmB,UAAU;YAAE,OAAO,mBAAmB,KAAK;QAAC;;0BAEnH,qKAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,UAAU,SAAS,YAAY,WAAW,UAAU,CAAC;0BACxF,cAAA,qKAAC;oBAAI,WAAU;;sCACX,qKAAC,qHAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,OAAO;gCAAE,gBAAgB;4BAAO;4BAAG,MAAK;sCAAsB;;;;;;sCACjH,qKAAC;4BACG,WAAW,CAAC,6BAA6B,EAAE,UAAU,SAAS,iBAAiB,iBAAiB;4BAChG,MAAK;4BACL,SAAS,IAAM,iBAAiB,CAAC;sCAEjC,cAAA,qKAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,qKAAC;gBAAM,WAAW,CAAC,sBAAsB,EAAE,UAAU,SAAS,YAAY,WAAW,CAAC,EAAE,gBAAgB,SAAS,IAAI;;kCACjH,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAG,WAAU;sCAAgD;;;;;;;;;;;kCAGlE,qKAAC;wBAAG,WAAU;;0CACV,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAGjD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC;oCAAO,WAAU;oCAAgC,kBAAe;oCAAY,kBAAe;oCAAU,OAAO;wCAAE,gBAAgB;oCAAO;;sDAClI,qKAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;0CAG7C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA6B;;;;;;;;;;;;0CAGlD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAA2B;;;;;;;;;;;;0CAGhD,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,qKAAC;4CAAE,WAAU;;;;;;wCAAqC;;;;;;;;;;;;0CAG1D,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,WAAU;oCAAkD,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDAC9I,qKAAC;;8DACG,qKAAC;oDAAE,WAAU;;;;;;gDAAuB;;;;;;;wCAEvC,eAAe,mBACZ,qKAAC;4CACG,OAAO;gDACH,iBAAiB;gDACjB,OAAO;gDACP,UAAU;gDACV,SAAS;gDACT,cAAc;gDACd,KAAK;gDACL,OAAO;4CACX;sDAEC;;;;;;;;;;;;;;;;;0CAIZ,qKAAC;;;;;0CACN,qKAAC;gCAAG,WAAU;0CACV,cAAA,qKAAC,qHAAA,CAAA,UAAI;oCAAC,MAAK;oCACP,WAAU;oCAAsC,OAAO;wCAAE,gBAAgB;oCAAO;oCAChF,MAAK;oCACL,SAAS,IAAM,iBAAiB;8CACnC;;;;;;;;;;;;;;;;;kCAIT,qKAAC;wBAAI,WAAU;;4BACV,wBACG,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,YAAY;oCAAY,gBAAgB;gCAAO;gCAAG,WAAU;;kDACzI,qKAAC;wCACG,WAAU;wCACV,OAAO;4CACH,OAAO;4CACP,QAAQ;4CACR,cAAc;wCAClB;;;;;;kDAEJ,qKAAC;;0DACG,qKAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;oDACd,cAAc;gDAClB;;;;;;0DAEJ,qKAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;qDAK1C,qKAAC,qHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,gBAAgB;gCAAO;gCAAG,WAAU;;oCAChH,yBACG,qKAAC,sHAAA,CAAA,UAAK;wCAEF,KAAK,QAAQ,KAAK,IAAI;wCACtB,OAAO;wCACP,QAAQ;wCACR,KAAI;wCACJ,WAAU;uCALL,QAAQ,KAAK;;;;;kDAQ1B,qKAAC;;0DACG,qKAAC;gDAAK,WAAU;0DAAsC,QAAQ,IAAI,IAAI;;;;;;0DACtE,qKAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAKtD,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCACG,SAAS;wCACT,WAAU;kDAET,UAAU,SAAS,eAAe;;;;;;kDAEvC,qKAAC;wCACG,SAAS;wCACT,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,qKAAC;gBAAK,WAAU;gBAA0B,OAAO;oBAAE,YAAY,mBAAmB,cAAc;gBAAC;;kCAC7F,qKAAC;wBAAI,WAAU;;4BACV;4BAAS;;;;;;;oBAEb,6BACG,qKAAC;wBAAI,WAAU;kCACX,cAAA,qKAAC;4BAAI,WAAU;;8CACX,qKAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,qKAAC;oCAAE,WAAU;8CAAa;;;;;;8CAI1B,qKAAC;oCAAI,WAAU;;sDACX,qKAAC;4CAAO,WAAU;4CAA8B,SAAS;sDAAqB;;;;;;sDAG9E,qKAAC;4CAAO,WAAU;4CAA6B,SAAS,IAAM,eAAe;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOrG,qKAAC;wBAAI,WAAU;wBAA4B,OAAO;4BAAE,YAAY,mBAAmB,MAAM;4BAAE,OAAO,mBAAmB,KAAK;wBAAC;wBAAG,IAAG;;0CAC7H,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCAAG,WAAU;kDAAkB;;;;;;kDAChC,qKAAC;wCAAO,MAAK;wCAAS,WAAU;wCAAsB,mBAAgB;;;;;;;;;;;;0CAE1E,qKAAC;gCAAI,WAAU;;kDACX,qKAAC;wCACG,MAAK;wCACL,MAAK;wCACL,IAAG;wCACH,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,OAAO;4CACH,iBAAiB;4CACjB,YAAY;4CACZ,KAAK;4CACL,QAAQ;wCACZ;wCACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;oCAG3D,wBACI,qKAAC;wCAAI,WAAU;;0DACX,qKAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;gDAClB;;;;;;0DAEJ,qKAAC;0DACG,cAAA,qKAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,cAAc;oDAClB;;;;;;;;;;;;;;;;6DAKZ,qKAAC;;4CACI,cAAc,GAAG,CAAC,CAAA,qBACf,qKAAC;oDAEG,WAAU;oDACV,OAAO;wDACH,QAAQ;oDACZ;;sEAEA,qKAAC,sHAAA,CAAA,UAAK;4DACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;4DAC5B,KAAK,KAAK,IAAI;4DACd,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,OAAO;gEAAE,WAAW;4DAAQ;;;;;;sEAEhC,qKAAC;;8EACG,qKAAC;8EAAQ,KAAK,QAAQ;;;;;;8EAAU,qKAAC;;;;;8EACjC,qKAAC;8EAAM,KAAK,IAAI;;;;;;;;;;;;;mDAhBf,KAAK,GAAG;;;;;4CAsBpB,cAAc,MAAM,KAAK,KAAK,4BAC3B,qKAAC;gDAAI,WAAU;;kEACX,qKAAC;wDACG,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,MAAK;wDACL,SAAQ;wDACR,OAAO;4DAAE,SAAS;wDAAI;kEAEtB,cAAA,qKAAC;4DAAK,GAAE;;;;;;;;;;;kEAEZ,qKAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Dashboard/Messages.js"], "sourcesContent": ["import { useState, useRef, useEffect } from \"react\";\r\nimport axios from '../../utils/axiosConfig';\r\nimport Image from \"next/image\";\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport DashboardLayout from '../Components/DashboardLayout';\r\nimport { useRouter } from 'next/router';\r\nimport { useTheme } from '../../context/ThemeContext';\r\n\r\nexport default function Messages() {\r\n    const [users, setUsers] = useState([]);\r\n    const [sessionUser, setSessionUser] = useState(null);\r\n    const [following, setFollowing] = useState(new Set());\r\n    const [accepted, setAccepted] = useState(new Set());\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [searchQuery, setSearchQuery] = useState('');\r\n    const [selectedChat, setSelectedChat] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [displayCount, setDisplayCount] = useState(6);\r\n    const [visibleUsers, setVisibleUsers] = useState([]);\r\n    const [filteredFollowers, setFilteredFollowers] = useState([]);\r\n    const [profile, setProfile] = useState({\r\n        username: 'user123',\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        bio: 'No bio yet.',\r\n        avatar: predefine,\r\n        posts: 15,\r\n        followersCount: 250,\r\n        followingCount: 180,\r\n    });\r\n    const { theme } = useTheme();\r\n    const [showOnlineOnly, setShowOnlineOnly] = useState(false);\r\n    const router = useRouter();\r\n\r\n    // Chat functionality states\r\n    const [chatMessages, setChatMessages] = useState({});\r\n    const [newMessage, setNewMessage] = useState(\"\");\r\n    const messagesEndRef = useRef(null);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n    const [selectedUser, setSelectedUser] = useState(null);\r\n\r\n    // Mock last message data and reactions\r\n    const [lastMessages, setLastMessages] = useState({});\r\n    const [reactions, setReactions] = useState({});\r\n\r\n    // Local Storage utility functions\r\n    const getChatHistory = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return [];\r\n            const chatKey = `chat_${sessionUserId}_${userId}`;\r\n            const stored = localStorage.getItem(chatKey);\r\n            return stored ? JSON.parse(stored) : [];\r\n        } catch (error) {\r\n            console.error('Error loading chat history:', error);\r\n            return [];\r\n        }\r\n    };\r\n\r\n    const saveChatHistory = (userId, messages) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const chatKey = `chat_${sessionUserId}_${userId}`;\r\n            // Limit messages to last 1000 to prevent localStorage overflow\r\n            const limitedMessages = messages.slice(-1000);\r\n            localStorage.setItem(chatKey, JSON.stringify(limitedMessages));\r\n        } catch (error) {\r\n            console.error('Error saving chat history:', error);\r\n            // If localStorage is full, try to clear old chats\r\n            if (error.name === 'QuotaExceededError') {\r\n                console.warn('localStorage quota exceeded, clearing old chats...');\r\n                clearOldChats();\r\n                // Try saving again\r\n                try {\r\n                    localStorage.setItem(chatKey, JSON.stringify(messages.slice(-500)));\r\n                } catch (retryError) {\r\n                    console.error('Failed to save even after cleanup:', retryError);\r\n                }\r\n            }\r\n        }\r\n    };\r\n\r\n    const clearOldChats = () => {\r\n        try {\r\n            const keys = Object.keys(localStorage);\r\n            const chatKeys = keys.filter(key => key.startsWith('chat_'));\r\n            // Remove oldest chats (simple cleanup strategy)\r\n            chatKeys.slice(0, Math.floor(chatKeys.length / 2)).forEach(key => {\r\n                localStorage.removeItem(key);\r\n            });\r\n        } catch (error) {\r\n            console.error('Error clearing old chats:', error);\r\n        }\r\n    };\r\n\r\n    const getLastMessageForUser = (userId) => {\r\n        const messages = getChatHistory(userId);\r\n        if (messages.length === 0) return 'No messages yet';\r\n        const lastMsg = messages[messages.length - 1];\r\n        const preview = lastMsg.text.length > 30 ? lastMsg.text.substring(0, 30) + '...' : lastMsg.text;\r\n        return lastMsg.sender === 'You' ? `You: ${preview}` : preview;\r\n    };\r\n\r\n    const formatMessageTime = (timestamp) => {\r\n        const now = new Date();\r\n        const msgTime = new Date(timestamp);\r\n        const diffInHours = (now - msgTime) / (1000 * 60 * 60);\r\n\r\n        if (diffInHours < 24) {\r\n            return msgTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n        } else if (diffInHours < 168) { // Less than a week\r\n            return msgTime.toLocaleDateString([], { weekday: 'short' });\r\n        } else {\r\n            return msgTime.toLocaleDateString([], { month: 'short', day: 'numeric' });\r\n        }\r\n    };\r\n\r\n    // Handle chat selection with URL update\r\n    const handleChatSelect = (user) => {\r\n        setSelectedChat(user._id);\r\n        setSelectedUser(user);\r\n\r\n        // Update URL with user ID\r\n        router.push(`/Dashboard/Messages?userId=${user._id}`, undefined, { shallow: true });\r\n\r\n        // Load chat history for this user\r\n        const history = getChatHistory(user._id);\r\n        setChatMessages(prev => ({\r\n            ...prev,\r\n            [user._id]: history\r\n        }));\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem('user'));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            if (!sessionId) {\r\n                setError('No session found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            try {\r\n                // Fetch all users\r\n                const response = await axios.post(\r\n                    'https://nextalk-u0y1.onrender.com/displayusersProfile',\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true,\r\n                    }\r\n                );\r\n\r\n                const personally = response.data;\r\n                const filteredUsers = personally.filter(user => user._id !== sessionId);\r\n                const sessionUser = personally.find(user => user._id === sessionId);\r\n                setSessionUser(sessionUser);\r\n\r\n                // Fetch follow status\r\n                const followRes = await fetch(`https://nextalk-u0y1.onrender.com/follow-status/${sessionId}`);\r\n                const followData = await followRes.json();\r\n                setFollowing(new Set(followData.following));\r\n                setAccepted(new Set(followData.accepted));\r\n\r\n                // Generate last messages from local storage\r\n                const mockLastMessages = filteredUsers.reduce((acc, user) => ({\r\n                    ...acc,\r\n                    [user._id]: getLastMessageForUser(user._id),\r\n                }), {});\r\n                setLastMessages(mockLastMessages);\r\n\r\n                setUsers(filteredUsers);\r\n                setLoading(false);\r\n\r\n                // Check if there's a userId in URL and auto-select that chat only if explicitly requested\r\n                const { userId } = router.query;\r\n                if (userId && router.asPath.includes('userId=')) {\r\n                    const userToSelect = filteredUsers.find(u => u._id === userId);\r\n                    if (userToSelect) {\r\n                        handleChatSelect(userToSelect);\r\n                    }\r\n                }\r\n            } catch (err) {\r\n                console.error('❌ Error fetching data:', err);\r\n                setError('Failed to load data.');\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, [router.query]);\r\n\r\n    // Auto-scroll to bottom when new messages are added\r\n    useEffect(() => {\r\n        if (messagesEndRef.current) {\r\n            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [chatMessages, selectedChat]);\r\n\r\n    // Filter users based on search query and online status\r\n    const filteredUsers = users\r\n        .filter(user => accepted.has(user._id))\r\n        .filter(user =>\r\n            user.name.toLowerCase().includes(searchQuery.toLowerCase())\r\n        )\r\n        .filter(user =>\r\n            showOnlineOnly ? user.isOnline : true\r\n        );\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',\r\n                color: '#e2e8f0',\r\n                cardBg: 'rgba(255, 255, 255, 0.1)',\r\n                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',\r\n                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',\r\n                notificationBg: 'rgba(51, 65, 85, 0.9)',\r\n            };\r\n        }\r\n        return {\r\n            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',\r\n            color: '#1e293b',\r\n            cardBg: 'rgba(255, 255, 255, 0.8)',\r\n            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)',\r\n        };\r\n    };\r\n\r\n    const styles = getThemeStyles();\r\n\r\n    const getThemeStyless = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: '#1e293b',\r\n                color: '#e2e8f0',\r\n                inputBg: '#334155',\r\n                inputColor: '#e2e8f0',\r\n                messageBgYou: '#3b82f6',\r\n                messageBgFriend: '#4b5563'\r\n            };\r\n        }\r\n        // Default to light styles for 'homeback' or any other theme\r\n        return {\r\n            background: '#ffffff',\r\n            color: '#1e293b',\r\n            inputBg: '#f1f5f9',\r\n            inputColor: '#1e293b',\r\n            messageBgYou: '#3b82f6',\r\n            messageBgFriend: '#e5e7eb'\r\n        };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyless();\r\n\r\n    const handleSendMessage = (e) => {\r\n        e.preventDefault();\r\n        if (newMessage.trim() && selectedChat) {\r\n            const currentMessages = chatMessages[selectedChat] || [];\r\n            const newMsg = {\r\n                id: Date.now(), // Use timestamp as unique ID\r\n                sender: \"You\",\r\n                text: newMessage,\r\n                timestamp: new Date().toLocaleTimeString([], {\r\n                    hour: '2-digit',\r\n                    minute: '2-digit',\r\n                    hour12: true\r\n                })\r\n            };\r\n\r\n            const updatedMessages = [...currentMessages, newMsg];\r\n\r\n            // Update chat messages state\r\n            setChatMessages(prev => ({\r\n                ...prev,\r\n                [selectedChat]: updatedMessages\r\n            }));\r\n\r\n            // Save to local storage\r\n            saveChatHistory(selectedChat, updatedMessages);\r\n\r\n            // Update last messages for the chat list\r\n            setLastMessages(prev => ({\r\n                ...prev,\r\n                [selectedChat]: newMsg.text.length > 30 ? newMsg.text.substring(0, 30) + '...' : newMsg.text\r\n            }));\r\n\r\n            setNewMessage(\"\");\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (!profile || !Array.isArray(profile.followers)) return;\r\n\r\n        // Extract follower user IDs from the populated followers array\r\n        const followersArray = profile.followers.map(f => f._id?.toString());\r\n\r\n        // Filter only users who are followers\r\n        const followedUsers = users.filter(user =>\r\n            followersArray.includes(user._id?.toString())\r\n        );\r\n\r\n        // Filter for search\r\n        const filtered = followedUsers.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            user.username.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setFilteredFollowers(filtered); // useful for Load More check\r\n\r\n        // Control visible users based on search or default count\r\n        if (searchTerm.trim() === '') {\r\n            setVisibleUsers(followedUsers.slice(0, displayCount));\r\n        } else {\r\n            setVisibleUsers(filtered.slice(0, displayCount));\r\n        }\r\n\r\n    }, [searchTerm, users, displayCount, profile]);\r\n\r\n\r\n    const handleLoadMore = () => {\r\n        const prevCount = displayCount;\r\n        const newCount = prevCount + 6;\r\n        setDisplayCount(newCount);\r\n\r\n        // Scroll to the previous 6th user (after DOM update)\r\n        setTimeout(() => {\r\n            const userElems = document.querySelectorAll(\".user-result\");\r\n            if (userElems[prevCount]) {\r\n                userElems[prevCount].scrollIntoView({\r\n                    behavior: \"smooth\",\r\n                    block: \"start\"\r\n                });\r\n            }\r\n        }, 100); // wait a moment for new DOM elements to render\r\n    };\r\n    return (\r\n        <DashboardLayout>\r\n            {loading ? (\r\n                <div className=\"custom-loader-overlay\">\r\n                    <svg viewBox=\"0 0 100 100\">\r\n                        <g fill=\"none\" stroke=\"#fff\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"6\">\r\n                            {/* left line */}\r\n                            <path d=\"M 21 40 V 59\">\r\n                                <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 21 59; 180 21 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* right line */}\r\n                            <path d=\"M 79 40 V 59\">\r\n                                <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 79 59; -180 79 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* top line */}\r\n                            <path d=\"M 50 21 V 40\">\r\n                                <animate attributeName=\"d\" values=\"M 50 21 V 40; M 50 59 V 40\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* bottom line */}\r\n                            <path d=\"M 50 60 V 79\">\r\n                                <animate attributeName=\"d\" values=\"M 50 60 V 79; M 50 98 V 79\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* top box */}\r\n                            <path d=\"M 50 21 L 79 40 L 50 60 L 21 40 Z\">\r\n                                <animate attributeName=\"stroke\" values=\"rgba(255,255,255,1); rgba(100,100,100,0)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* mid box */}\r\n                            <path d=\"M 50 40 L 79 59 L 50 79 L 21 59 Z\" />\r\n                            {/* bottom box */}\r\n                            <path d=\"M 50 59 L 79 78 L 50 98 L 21 78 Z\">\r\n                                <animate attributeName=\"stroke\" values=\"rgba(100,100,100,0); rgba(255,255,255,1)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            <animateTransform attributeName=\"transform\" type=\"translate\" values=\"0 0; 0 -19\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </g>\r\n                    </svg>\r\n                </div>\r\n            ) : error ? (\r\n                <div className=\"error\">{error}</div>\r\n            ) : filteredUsers.length === 0 ? (\r\n                <div className=\"no-friends\">No friends to message.</div>\r\n            ) : (\r\n                <div className=\"chat-container-instagram\" style={{\r\n                    background: getThemeStyles().background,\r\n                    color: getThemeStyles().color,\r\n                    display: 'flex',\r\n                    height: '85vh',\r\n                    borderRadius: '12px',\r\n                    overflow: 'hidden',\r\n                    boxShadow: theme === 'dark' ? '0 8px 32px rgba(0,0,0,0.3)' : '0 8px 32px rgba(0,0,0,0.1)',\r\n                    border: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)'\r\n                }}>\r\n                    {/* Left Sidebar for Chat List */}\r\n                    <div className=\"chat-list-instagram\" style={{\r\n                        width: selectedChat ? '350px' : '100%',\r\n                        borderRight: selectedChat ? (theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)') : 'none',\r\n                        background: theme === 'dark' ? 'rgba(255,255,255,0.02)' : 'rgba(0,0,0,0.02)',\r\n                        display: 'flex',\r\n                        flexDirection: 'column',\r\n                        transition: 'all 0.3s ease'\r\n                    }}>\r\n                        <div style={{\r\n                            padding: '20px',\r\n                            borderBottom: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',\r\n                            background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)'\r\n                        }}>\r\n                            <h2 style={{\r\n                                margin: '0 0 15px 0',\r\n                                fontSize: '1.5rem',\r\n                                fontWeight: '600',\r\n                                textAlign: 'center'\r\n                            }}>Messages</h2>\r\n                            <div style={{ position: 'relative' }}>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    placeholder=\"Search messages...\"\r\n                                    value={searchQuery}\r\n                                    onChange={(e) => setSearchQuery(e.target.value)}\r\n                                    style={{\r\n                                        width: '100%',\r\n                                        padding: '12px 40px 12px 16px',\r\n                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',\r\n                                        borderRadius: '25px',\r\n                                        background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',\r\n                                        color: getThemeStyles().color,\r\n                                        outline: 'none',\r\n                                        fontSize: '0.9rem',\r\n                                        transition: 'all 0.3s ease'\r\n                                    }}\r\n                                    onFocus={(e) => {\r\n                                        e.target.style.borderColor = '#3b82f6';\r\n                                        e.target.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';\r\n                                    }}\r\n                                    onBlur={(e) => {\r\n                                        e.target.style.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';\r\n                                        e.target.style.boxShadow = 'none';\r\n                                    }}\r\n                                />\r\n                                <i className=\"bi bi-search\" style={{\r\n                                    position: 'absolute',\r\n                                    right: '15px',\r\n                                    top: '50%',\r\n                                    transform: 'translateY(-50%)',\r\n                                    opacity: '0.6',\r\n                                    fontSize: '0.9rem'\r\n                                }}></i>\r\n                            </div>\r\n                        </div>\r\n                        <div style={{\r\n                            flex: 1,\r\n                            overflowY: 'auto',\r\n                            padding: '10px 0'\r\n                        }}>\r\n                            {filteredUsers.map(user => (\r\n                                <div\r\n                                    key={user._id}\r\n                                    onClick={() => handleChatSelect(user)}\r\n                                    style={{\r\n                                        display: 'flex',\r\n                                        alignItems: 'center',\r\n                                        padding: '12px 20px',\r\n                                        cursor: 'pointer',\r\n                                        transition: 'all 0.2s ease',\r\n                                        background: selectedChat === user._id ?\r\n                                            (theme === 'dark' ? 'rgba(59,130,246,0.2)' : 'rgba(59,130,246,0.1)') :\r\n                                            'transparent',\r\n                                        borderLeft: selectedChat === user._id ? '3px solid #3b82f6' : '3px solid transparent'\r\n                                    }}\r\n                                    onMouseEnter={(e) => {\r\n                                        if (selectedChat !== user._id) {\r\n                                            e.target.style.background = theme === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)';\r\n                                        }\r\n                                    }}\r\n                                    onMouseLeave={(e) => {\r\n                                        if (selectedChat !== user._id) {\r\n                                            e.target.style.background = 'transparent';\r\n                                        }\r\n                                    }}\r\n                                >\r\n                                    <div style={{ position: 'relative', marginRight: '15px' }}>\r\n                                        <Image\r\n                                            src={user.image || predefine}\r\n                                            alt={user.name}\r\n                                            width={50}\r\n                                            height={50}\r\n                                            style={{\r\n                                                borderRadius: '50%',\r\n                                                objectFit: 'cover',\r\n                                                border: selectedChat === user._id ? '2px solid #3b82f6' : '2px solid transparent'\r\n                                            }}\r\n                                        />\r\n                                        <span style={{\r\n                                            position: 'absolute',\r\n                                            bottom: '2px',\r\n                                            right: '2px',\r\n                                            width: '12px',\r\n                                            height: '12px',\r\n                                            background: '#10b981',\r\n                                            borderRadius: '50%',\r\n                                            border: '2px solid white'\r\n                                        }}></span>\r\n                                    </div>\r\n                                    <div style={{ flex: 1, minWidth: 0 }}>\r\n                                        <div style={{\r\n                                            display: 'flex',\r\n                                            justifyContent: 'space-between',\r\n                                            alignItems: 'center',\r\n                                            marginBottom: '4px'\r\n                                        }}>\r\n                                            <span style={{\r\n                                                fontWeight: selectedChat === user._id ? '600' : '500',\r\n                                                fontSize: '1rem',\r\n                                                color: selectedChat === user._id ? '#3b82f6' : getThemeStyles().color\r\n                                            }}>{user.name}</span>\r\n                                            <span style={{\r\n                                                fontSize: '0.75rem',\r\n                                                opacity: 0.7,\r\n                                                color: getThemeStyles().color\r\n                                            }}>\r\n                                                {(() => {\r\n                                                    const messages = getChatHistory(user._id);\r\n                                                    if (messages.length > 0) {\r\n                                                        return formatMessageTime(messages[messages.length - 1].timestamp);\r\n                                                    }\r\n                                                    return '';\r\n                                                })()}\r\n                                            </span>\r\n                                        </div>\r\n                                        <p style={{\r\n                                            margin: 0,\r\n                                            fontSize: '0.85rem',\r\n                                            opacity: 0.8,\r\n                                            whiteSpace: 'nowrap',\r\n                                            overflow: 'hidden',\r\n                                            textOverflow: 'ellipsis',\r\n                                            color: getThemeStyles().color\r\n                                        }}>{lastMessages[user._id] || 'Start a conversation'}</p>\r\n                                    </div>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Right Panel for Chat or Instructions */}\r\n                    {selectedChat && (\r\n                        <div style={{\r\n                            flex: 1,\r\n                            display: 'flex',\r\n                            flexDirection: 'column',\r\n                            background: theme === 'dark' ? 'rgba(255,255,255,0.01)' : 'rgba(0,0,0,0.01)'\r\n                        }}>\r\n                            {/* Chat Header */}\r\n                            <div style={{\r\n                                padding: '20px',\r\n                                borderBottom: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',\r\n                                background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)',\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                gap: '15px'\r\n                            }}>\r\n                                <Image\r\n                                    src={filteredUsers.find(u => u._id === selectedChat)?.image || predefine}\r\n                                    alt={filteredUsers.find(u => u._id === selectedChat)?.name}\r\n                                    width={45}\r\n                                    height={45}\r\n                                    style={{\r\n                                        borderRadius: '50%',\r\n                                        objectFit: 'cover',\r\n                                        border: '2px solid #3b82f6'\r\n                                    }}\r\n                                />\r\n                                <div>\r\n                                    <h3 style={{\r\n                                        margin: 0,\r\n                                        fontSize: '1.2rem',\r\n                                        fontWeight: '600',\r\n                                        color: getThemeStyles().color\r\n                                    }}>{filteredUsers.find(u => u._id === selectedChat)?.name}</h3>\r\n                                    <p style={{\r\n                                        margin: 0,\r\n                                        fontSize: '0.85rem',\r\n                                        opacity: 0.7,\r\n                                        color: getThemeStyles().color\r\n                                    }}>Active now</p>\r\n                                </div>\r\n                            </div>\r\n                            {/* Chat Messages */}\r\n                            <div style={{\r\n                                flex: 1,\r\n                                overflowY: 'auto',\r\n                                padding: '20px',\r\n                                background: theme === 'dark' ? 'rgba(255,255,255,0.01)' : 'rgba(0,0,0,0.01)'\r\n                            }}>\r\n                                {chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (\r\n                                    chatMessages[selectedChat].map((msg) => (\r\n                                        <div\r\n                                            key={msg.id}\r\n                                            style={{\r\n                                                display: 'flex',\r\n                                                justifyContent: msg.sender === \"You\" ? 'flex-end' : 'flex-start',\r\n                                                marginBottom: '15px'\r\n                                            }}\r\n                                        >\r\n                                            <div\r\n                                                style={{\r\n                                                    maxWidth: '70%',\r\n                                                    padding: '12px 16px',\r\n                                                    borderRadius: '18px',\r\n                                                    background: msg.sender === \"You\"\r\n                                                        ? 'linear-gradient(45deg, #3b82f6, #60a5fa)'\r\n                                                        : (theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'),\r\n                                                    color: msg.sender === \"You\" ? '#ffffff' : getThemeStyles().color,\r\n                                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\r\n                                                    animation: 'messageSlideIn 0.3s ease-out'\r\n                                                }}\r\n                                            >\r\n                                                <div style={{ marginBottom: '4px', lineHeight: '1.4' }}>{msg.text}</div>\r\n                                                <div style={{\r\n                                                    fontSize: '0.75rem',\r\n                                                    opacity: 0.7,\r\n                                                    textAlign: 'right'\r\n                                                }}>{msg.timestamp}</div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ))\r\n                                ) : (\r\n                                    <div style={{\r\n                                        textAlign: 'center',\r\n                                        padding: '60px 20px',\r\n                                        color: getThemeStyles().color,\r\n                                        opacity: 0.6\r\n                                    }}>\r\n                                        <i className=\"bi bi-chat-dots\" style={{\r\n                                            fontSize: '3rem',\r\n                                            marginBottom: '15px',\r\n                                            display: 'block'\r\n                                        }}></i>\r\n                                        <h4 style={{ marginBottom: '10px' }}>No messages yet</h4>\r\n                                        <p>Start the conversation by sending a message!</p>\r\n                                    </div>\r\n                                )}\r\n                                <div ref={messagesEndRef} />\r\n                            </div>\r\n\r\n                            {/* Chat Input */}\r\n                            <div style={{\r\n                                padding: '20px',\r\n                                borderTop: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',\r\n                                background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)'\r\n                            }}>\r\n                                <form onSubmit={handleSendMessage} style={{ display: 'flex', gap: '10px' }}>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        placeholder=\"Type a message...\"\r\n                                        value={newMessage}\r\n                                        onChange={(e) => setNewMessage(e.target.value)}\r\n                                        style={{\r\n                                            flex: 1,\r\n                                            padding: '12px 16px',\r\n                                            border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',\r\n                                            borderRadius: '25px',\r\n                                            background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',\r\n                                            color: getThemeStyles().color,\r\n                                            outline: 'none',\r\n                                            fontSize: '1rem',\r\n                                            transition: 'all 0.3s ease'\r\n                                        }}\r\n                                        onFocus={(e) => {\r\n                                            e.target.style.borderColor = '#3b82f6';\r\n                                            e.target.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';\r\n                                        }}\r\n                                        onBlur={(e) => {\r\n                                            e.target.style.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';\r\n                                            e.target.style.boxShadow = 'none';\r\n                                        }}\r\n                                    />\r\n                                    <button\r\n                                        type=\"submit\"\r\n                                        style={{\r\n                                            width: '50px',\r\n                                            height: '50px',\r\n                                            border: 'none',\r\n                                            borderRadius: '50%',\r\n                                            background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n                                            color: 'white',\r\n                                            cursor: 'pointer',\r\n                                            transition: 'all 0.3s ease',\r\n                                            display: 'flex',\r\n                                            alignItems: 'center',\r\n                                            justifyContent: 'center'\r\n                                        }}\r\n                                        onMouseEnter={(e) => {\r\n                                            e.target.style.transform = 'scale(1.05)';\r\n                                            e.target.style.boxShadow = '0 5px 15px rgba(59,130,246,0.4)';\r\n                                        }}\r\n                                        onMouseLeave={(e) => {\r\n                                            e.target.style.transform = 'scale(1)';\r\n                                            e.target.style.boxShadow = 'none';\r\n                                        }}\r\n                                    >\r\n                                        <i className=\"bi bi-send-fill\"></i>\r\n                                    </button>\r\n                                </form>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Instructions when no chat is selected */}\r\n                    {!selectedChat && (\r\n                        <div style={{\r\n                            flex: 1,\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            justifyContent: 'center',\r\n                            flexDirection: 'column',\r\n                            padding: '40px',\r\n                            textAlign: 'center',\r\n                            background: theme === 'dark' ? 'rgba(255,255,255,0.01)' : 'rgba(0,0,0,0.01)'\r\n                        }}>\r\n                            <div style={{\r\n                                background: theme === 'dark' ? 'rgba(59,130,246,0.1)' : 'rgba(59,130,246,0.05)',\r\n                                borderRadius: '50%',\r\n                                width: '120px',\r\n                                height: '120px',\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                marginBottom: '30px',\r\n                                border: theme === 'dark' ? '2px solid rgba(59,130,246,0.3)' : '2px solid rgba(59,130,246,0.2)'\r\n                            }}>\r\n                                <i className=\"bi bi-chat-heart\" style={{\r\n                                    fontSize: '3.5rem',\r\n                                    color: '#3b82f6'\r\n                                }}></i>\r\n                            </div>\r\n                            <h2 style={{\r\n                                margin: '0 0 15px 0',\r\n                                fontSize: '2rem',\r\n                                fontWeight: '600',\r\n                                color: getThemeStyles().color,\r\n                                background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n                                WebkitBackgroundClip: 'text',\r\n                                WebkitTextFillColor: 'transparent',\r\n                                backgroundClip: 'text'\r\n                            }}>Start Chatting!</h2>\r\n                            <p style={{\r\n                                margin: '0 0 30px 0',\r\n                                fontSize: '1.1rem',\r\n                                opacity: 0.8,\r\n                                color: getThemeStyles().color,\r\n                                maxWidth: '400px',\r\n                                lineHeight: '1.6'\r\n                            }}>\r\n                                Connect with your friends and start meaningful conversations.\r\n                                Select a chat from the list or send a message to someone new.\r\n                            </p>\r\n                            <button\r\n                                data-bs-toggle=\"modal\"\r\n                                data-bs-target=\"#usersModal\"\r\n                                style={{\r\n                                    padding: '15px 30px',\r\n                                    border: 'none',\r\n                                    borderRadius: '25px',\r\n                                    background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n                                    color: 'white',\r\n                                    fontSize: '1.1rem',\r\n                                    fontWeight: '600',\r\n                                    cursor: 'pointer',\r\n                                    transition: 'all 0.3s ease',\r\n                                    display: 'flex',\r\n                                    alignItems: 'center',\r\n                                    gap: '10px',\r\n                                    boxShadow: '0 4px 15px rgba(59,130,246,0.3)'\r\n                                }}\r\n                                onMouseEnter={(e) => {\r\n                                    e.target.style.transform = 'translateY(-2px)';\r\n                                    e.target.style.boxShadow = '0 8px 25px rgba(59,130,246,0.4)';\r\n                                }}\r\n                                onMouseLeave={(e) => {\r\n                                    e.target.style.transform = 'translateY(0)';\r\n                                    e.target.style.boxShadow = '0 4px 15px rgba(59,130,246,0.3)';\r\n                                }}\r\n                            >\r\n                                <i className=\"bi bi-plus-circle\"></i>\r\n                                Start New Message\r\n                            </button>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n            {/* Users Modal */}\r\n            <div className=\"modal fade\" id=\"usersModal\" tabIndex=\"-1\" aria-labelledby=\"usersModalLabel\" aria-hidden=\"true\">\r\n                <div className=\"modal-dialog modal-dialog-centered modal-lg\">\r\n                    <div className=\"modal-content\" style={{\r\n                        background: theme === 'dark' ? '#1e293b' : '#ffffff',\r\n                        color: getThemeStyles().color,\r\n                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',\r\n                        borderRadius: '15px',\r\n                        boxShadow: '0 20px 60px rgba(0,0,0,0.3)'\r\n                    }}>\r\n                        <div className=\"modal-header\" style={{\r\n                            borderBottom: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',\r\n                            padding: '20px 25px'\r\n                        }}>\r\n                            <h5 className=\"modal-title\" id=\"usersModalLabel\" style={{\r\n                                fontSize: '1.5rem',\r\n                                fontWeight: '600',\r\n                                color: getThemeStyles().color,\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                gap: '10px'\r\n                            }}>\r\n                                <i className=\"bi bi-people-fill\" style={{ color: '#3b82f6' }}></i>\r\n                                Start New Conversation\r\n                            </h5>\r\n                            <button\r\n                                type=\"button\"\r\n                                className=\"btn-close\"\r\n                                data-bs-dismiss=\"modal\"\r\n                                aria-label=\"Close\"\r\n                                style={{\r\n                                    background: theme === 'dark' ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.8)',\r\n                                    borderRadius: '50%',\r\n                                    padding: '8px'\r\n                                }}\r\n                            ></button>\r\n                        </div>\r\n                        <div className=\"modal-body\" style={{ padding: '25px' }}>\r\n                            <div style={{ position: 'relative', marginBottom: '20px' }}>\r\n                                <input\r\n                                    type=\"search\"\r\n                                    placeholder=\"Search users...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    style={{\r\n                                        width: '100%',\r\n                                        padding: '12px 40px 12px 16px',\r\n                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',\r\n                                        borderRadius: '25px',\r\n                                        background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',\r\n                                        color: getThemeStyles().color,\r\n                                        outline: 'none',\r\n                                        fontSize: '1rem',\r\n                                        transition: 'all 0.3s ease'\r\n                                    }}\r\n                                    onFocus={(e) => {\r\n                                        e.target.style.borderColor = '#3b82f6';\r\n                                        e.target.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';\r\n                                    }}\r\n                                    onBlur={(e) => {\r\n                                        e.target.style.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';\r\n                                        e.target.style.boxShadow = 'none';\r\n                                    }}\r\n                                />\r\n                                <i className=\"bi bi-search\" style={{\r\n                                    position: 'absolute',\r\n                                    right: '15px',\r\n                                    top: '50%',\r\n                                    transform: 'translateY(-50%)',\r\n                                    opacity: '0.6',\r\n                                    fontSize: '1rem'\r\n                                }}></i>\r\n                            </div>\r\n\r\n                            <div style={{\r\n                                maxHeight: '400px',\r\n                                overflowY: 'auto',\r\n                                padding: '10px 0'\r\n                            }}>\r\n                                {loading ? (\r\n                                    <div style={{\r\n                                        display: 'flex',\r\n                                        alignItems: 'center',\r\n                                        gap: '15px',\r\n                                        padding: '15px 0'\r\n                                    }}>\r\n                                        <div style={{\r\n                                            width: \"50px\",\r\n                                            height: \"50px\",\r\n                                            borderRadius: \"50%\",\r\n                                            background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',\r\n                                            animation: 'pulse 1.5s ease-in-out infinite'\r\n                                        }}></div>\r\n                                        <div>\r\n                                            <div style={{\r\n                                                width: \"120px\",\r\n                                                height: \"16px\",\r\n                                                borderRadius: \"4px\",\r\n                                                marginBottom: \"8px\",\r\n                                                background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',\r\n                                                animation: 'pulse 1.5s ease-in-out infinite'\r\n                                            }}></div>\r\n                                            <div style={{\r\n                                                width: \"80px\",\r\n                                                height: \"12px\",\r\n                                                borderRadius: \"4px\",\r\n                                                background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',\r\n                                                animation: 'pulse 1.5s ease-in-out infinite'\r\n                                            }}></div>\r\n                                        </div>\r\n                                    </div>\r\n                                ) : (\r\n                                    <>\r\n                                        {visibleUsers.length > 0 ? (\r\n                                            visibleUsers.map(user => (\r\n                                                <div\r\n                                                    key={user._id}\r\n                                                    style={{\r\n                                                        display: 'flex',\r\n                                                        alignItems: 'center',\r\n                                                        justifyContent: 'space-between',\r\n                                                        padding: '15px',\r\n                                                        borderRadius: '12px',\r\n                                                        marginBottom: '10px',\r\n                                                        background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)',\r\n                                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',\r\n                                                        transition: 'all 0.3s ease',\r\n                                                        cursor: 'pointer'\r\n                                                    }}\r\n                                                    onMouseEnter={(e) => {\r\n                                                        e.currentTarget.style.background = theme === 'dark' ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)';\r\n                                                        e.currentTarget.style.transform = 'translateY(-2px)';\r\n                                                    }}\r\n                                                    onMouseLeave={(e) => {\r\n                                                        e.currentTarget.style.background = theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)';\r\n                                                        e.currentTarget.style.transform = 'translateY(0)';\r\n                                                    }}\r\n                                                >\r\n                                                    <div style={{\r\n                                                        display: 'flex',\r\n                                                        alignItems: 'center',\r\n                                                        gap: '15px'\r\n                                                    }}>\r\n                                                        <Image\r\n                                                            src={user.image || predefine}\r\n                                                            alt={user.name}\r\n                                                            width={50}\r\n                                                            height={50}\r\n                                                            style={{\r\n                                                                borderRadius: '50%',\r\n                                                                objectFit: 'cover',\r\n                                                                border: '2px solid rgba(59,130,246,0.3)'\r\n                                                            }}\r\n                                                        />\r\n                                                        <div>\r\n                                                            <div style={{\r\n                                                                fontWeight: '600',\r\n                                                                fontSize: '1rem',\r\n                                                                color: getThemeStyles().color,\r\n                                                                marginBottom: '4px'\r\n                                                            }}>{user.name}</div>\r\n                                                            <div style={{\r\n                                                                fontSize: '0.85rem',\r\n                                                                opacity: 0.7,\r\n                                                                color: getThemeStyles().color\r\n                                                            }}>@{user.username}</div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <button\r\n                                                        onClick={() => {\r\n                                                            handleChatSelect(user);\r\n                                                            // Close modal\r\n                                                            const modal = document.getElementById('usersModal');\r\n                                                            const modalInstance = window.bootstrap?.Modal?.getInstance(modal);\r\n                                                            if (modalInstance) {\r\n                                                                modalInstance.hide();\r\n                                                            }\r\n                                                        }}\r\n                                                        style={{\r\n                                                            padding: '8px 16px',\r\n                                                            border: 'none',\r\n                                                            borderRadius: '20px',\r\n                                                            background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n                                                            color: 'white',\r\n                                                            fontSize: '0.9rem',\r\n                                                            fontWeight: '500',\r\n                                                            cursor: 'pointer',\r\n                                                            transition: 'all 0.3s ease'\r\n                                                        }}\r\n                                                        onMouseEnter={(e) => {\r\n                                                            e.target.style.transform = 'scale(1.05)';\r\n                                                            e.target.style.boxShadow = '0 4px 12px rgba(59,130,246,0.4)';\r\n                                                        }}\r\n                                                        onMouseLeave={(e) => {\r\n                                                            e.target.style.transform = 'scale(1)';\r\n                                                            e.target.style.boxShadow = 'none';\r\n                                                        }}\r\n                                                    >\r\n                                                        <i className=\"bi bi-chat-dots me-1\"></i>\r\n                                                        Message\r\n                                                    </button>\r\n                                                </div>\r\n                                            ))\r\n                                        ) : (\r\n                                            <div style={{\r\n                                                textAlign: 'center',\r\n                                                padding: '60px 20px',\r\n                                                color: getThemeStyles().color,\r\n                                                opacity: 0.6\r\n                                            }}>\r\n                                                <i className=\"bi bi-person-x\" style={{\r\n                                                    fontSize: \"3rem\",\r\n                                                    marginBottom: \"15px\",\r\n                                                    display: \"block\"\r\n                                                }}></i>\r\n                                                <h5>No Users Found</h5>\r\n                                                <p>Try adjusting your search terms</p>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Load More Button */}\r\n                                        {visibleUsers.length < filteredFollowers.length && (\r\n                                            <div style={{ textAlign: 'center', marginTop: '20px' }}>\r\n                                                <button\r\n                                                    onClick={handleLoadMore}\r\n                                                    style={{\r\n                                                        padding: '10px 20px',\r\n                                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',\r\n                                                        borderRadius: '20px',\r\n                                                        background: 'transparent',\r\n                                                        color: getThemeStyles().color,\r\n                                                        cursor: 'pointer',\r\n                                                        transition: 'all 0.3s ease',\r\n                                                        fontSize: '0.9rem'\r\n                                                    }}\r\n                                                    onMouseEnter={(e) => {\r\n                                                        e.target.style.background = theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';\r\n                                                    }}\r\n                                                    onMouseLeave={(e) => {\r\n                                                        e.target.style.background = 'transparent';\r\n                                                    }}\r\n                                                >\r\n                                                    Load More\r\n                                                </button>\r\n                                            </div>\r\n                                        )}\r\n                                            {/* Show \"Load More\" button only if there are more followed users to show */}\r\n                                            {visibleUsers.length < filteredFollowers.length && (\r\n                                                searchTerm.trim() === ''\r\n                                                    ? (profile.followers.length || 0)\r\n                                                    : users.filter(user =>\r\n                                                        profile.followers.includes(user._id) &&\r\n                                                        (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                                                            user.username.toLowerCase().includes(searchTerm.toLowerCase()))\r\n                                                    ).length\r\n                                            ) && (\r\n                                                    <div className=\"text-center mt-3\">\r\n                                                        <button className=\"btn w-100 btn-outline-primary\" onClick={handleLoadMore}>\r\n                                                            Load More\r\n                                                        </button>\r\n                                                    </div>\r\n                                                )}\r\n\r\n\r\n                                        </>\r\n\r\n\r\n                                    )\r\n                                }\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </DashboardLayout>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEe,SAAS;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;QACV,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ,qRAAA,CAAA,UAAS;QACjB,OAAO;QACP,gBAAgB;QAChB,gBAAgB;IACpB;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IAEvB,4BAA4B;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,mGAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uCAAuC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAE5C,kCAAkC;IAClC,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO,EAAE;YACxC,MAAM,WAAU,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ;YACjD,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;QAC3C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACb;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,WAAU,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ;YACjD,+DAA+D;YAC/D,MAAM,kBAAkB,SAAS,KAAK,CAAC,CAAC;YACxC,aAAa,OAAO,CAAC,UAAS,KAAK,SAAS,CAAC;QACjD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,kDAAkD;YAClD,IAAI,MAAM,IAAI,KAAK,sBAAsB;gBACrC,QAAQ,IAAI,CAAC;gBACb;gBACA,mBAAmB;gBACnB,IAAI;oBACA,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,KAAK,CAAC,CAAC;gBACjE,EAAE,OAAO,YAAY;oBACjB,QAAQ,KAAK,CAAC,sCAAsC;gBACxD;YACJ;QACJ;IACJ;IAEA,MAAM,gBAAgB;QAClB,IAAI;YACA,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC;YACnD,gDAAgD;YAChD,SAAS,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,SAAS,MAAM,GAAG,IAAI,OAAO,CAAC,CAAA;gBACvD,aAAa,UAAU,CAAC;YAC5B;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,6BAA6B;QAC/C;IACJ;IAEA,MAAM,wBAAwB,CAAC;QAC3B,MAAM,WAAW,eAAe;QAChC,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;QAClC,MAAM,UAAU,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;QAC7C,MAAM,UAAU,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ,QAAQ,IAAI;QAC/F,OAAO,QAAQ,MAAM,KAAK,QAAQ,CAAC,KAAK,EAAE,SAAS,GAAG;IAC1D;IAEA,MAAM,oBAAoB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,KAAK;QACzB,MAAM,cAAc,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,KAAK,EAAE;QAErD,IAAI,cAAc,IAAI;YAClB,OAAO,QAAQ,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC/E,OAAO,IAAI,cAAc,KAAK;YAC1B,OAAO,QAAQ,kBAAkB,CAAC,EAAE,EAAE;gBAAE,SAAS;YAAQ;QAC7D,OAAO;YACH,OAAO,QAAQ,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU;QAC3E;IACJ;IAEA,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACtB,gBAAgB,KAAK,GAAG;QACxB,gBAAgB;QAEhB,0BAA0B;QAC1B,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,KAAK,GAAG,EAAE,EAAE,WAAW;YAAE,SAAS;QAAK;QAEjF,kCAAkC;QAClC,MAAM,UAAU,eAAe,KAAK,GAAG;QACvC,gBAAgB,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,KAAK,GAAG,CAAC,EAAE;YAChB,CAAC;IACL;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,YAAY;YACd,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;YACrD,MAAM,YAAY,YAAY,MAAM;YACpC,iBAAiB;YAEjB,IAAI,CAAC,WAAW;gBACZ,SAAS;gBACT,WAAW;gBACX;YACJ;YAEA,IAAI;gBACA,kBAAkB;gBAClB,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;oBACI,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,iBAAiB;gBACrB;gBAGJ,MAAM,aAAa,SAAS,IAAI;gBAChC,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBAC7D,MAAM,cAAc,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;gBACzD,eAAe;gBAEf,sBAAsB;gBACtB,MAAM,YAAY,MAAM,MAAM,CAAC,gDAAgD,EAAE,WAAW;gBAC5F,MAAM,aAAa,MAAM,UAAU,IAAI;gBACvC,aAAa,IAAI,IAAI,WAAW,SAAS;gBACzC,YAAY,IAAI,IAAI,WAAW,QAAQ;gBAEvC,4CAA4C;gBAC5C,MAAM,mBAAmB,cAAc,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;wBAC1D,GAAG,GAAG;wBACN,CAAC,KAAK,GAAG,CAAC,EAAE,sBAAsB,KAAK,GAAG;oBAC9C,CAAC,GAAG,CAAC;gBACL,gBAAgB;gBAEhB,SAAS;gBACT,WAAW;gBAEX,0FAA0F;gBAC1F,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,KAAK;gBAC/B,IAAI,UAAU,OAAO,MAAM,CAAC,QAAQ,CAAC,YAAY;oBAC7C,MAAM,eAAe,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;oBACvD,IAAI,cAAc;wBACd,iBAAiB;oBACrB;gBACJ;YACJ,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,SAAS;gBACT,WAAW;YACf;QACJ;QAEA;IACJ,GAAG;QAAC,OAAO,KAAK;KAAC;IAEjB,oDAAoD;IACpD,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,eAAe,OAAO,EAAE;YACxB,eAAe,OAAO,CAAC,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC/D;IACJ,GAAG;QAAC;QAAc;KAAa;IAE/B,uDAAuD;IACvD,MAAM,gBAAgB,MACjB,MAAM,CAAC,CAAA,OAAQ,SAAS,GAAG,CAAC,KAAK,GAAG,GACpC,MAAM,CAAC,CAAA,OACJ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,KAE3D,MAAM,CAAC,CAAA,OACJ,iBAAiB,KAAK,QAAQ,GAAG;IAGzC,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,aAAa;gBACb,gBAAgB;YACpB;QACJ;QACA,OAAO;YACH,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,aAAa;QACjB;IACJ;IAEA,MAAM,SAAS;IAEf,MAAM,kBAAkB;QACpB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,iBAAiB;YACrB;QACJ;QACA,4DAA4D;QAC5D,OAAO;YACH,YAAY;YACZ,OAAO;YACP,SAAS;YACT,YAAY;YACZ,cAAc;YACd,iBAAiB;QACrB;IACJ;IAEA,MAAM,qBAAqB;IAE3B,MAAM,oBAAoB,CAAC;QACvB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,MAAM,cAAc;YACnC,MAAM,kBAAkB,YAAY,CAAC,aAAa,IAAI,EAAE;YACxD,MAAM,SAAS;gBACX,IAAI,KAAK,GAAG;gBACZ,QAAQ;gBACR,MAAM;gBACN,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;oBACzC,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACZ;YACJ;YAEA,MAAM,kBAAkB;mBAAI;gBAAiB;aAAO;YAEpD,6BAA6B;YAC7B,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,aAAa,EAAE;gBACpB,CAAC;YAED,wBAAwB;YACxB,gBAAgB,cAAc;YAE9B,yCAAyC;YACzC,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ,OAAO,IAAI;gBAChG,CAAC;YAED,cAAc;QAClB;IACJ;IAEA,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,QAAQ,SAAS,GAAG;QAEnD,+DAA+D;QAC/D,MAAM,iBAAiB,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,EAAE;QAEzD,sCAAsC;QACtC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAC/B,eAAe,QAAQ,CAAC,KAAK,GAAG,EAAE;QAGtC,oBAAoB;QACpB,MAAM,WAAW,cAAc,MAAM,CAAC,CAAA,OAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAG/D,qBAAqB,WAAW,6BAA6B;QAE7D,yDAAyD;QACzD,IAAI,WAAW,IAAI,OAAO,IAAI;YAC1B,gBAAgB,cAAc,KAAK,CAAC,GAAG;QAC3C,OAAO;YACH,gBAAgB,SAAS,KAAK,CAAC,GAAG;QACtC;IAEJ,GAAG;QAAC;QAAY;QAAO;QAAc;KAAQ;IAG7C,MAAM,iBAAiB;QACnB,MAAM,YAAY;QAClB,MAAM,WAAW,YAAY;QAC7B,gBAAgB;QAEhB,qDAAqD;QACrD,WAAW;YACP,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAC5C,IAAI,SAAS,CAAC,UAAU,EAAE;gBACtB,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC;oBAChC,UAAU;oBACV,OAAO;gBACX;YACJ;QACJ,GAAG,MAAM,+CAA+C;IAC5D;IACA,qBACI,qKAAC,+HAAA,CAAA,UAAe;;YACX,wBACG,qKAAC;gBAAI,WAAU;0BACX,cAAA,qKAAC;oBAAI,SAAQ;8BACT,cAAA,qKAAC;wBAAE,MAAK;wBAAO,QAAO;wBAAO,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;;0CAElF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAqB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG/G,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAsB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGhH,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG3G,qKAAC;gCAAK,GAAE;;;;;;0CAER,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAE3G,qKAAC;gCAAiB,eAAc;gCAAY,MAAK;gCAAY,QAAO;gCAAa,KAAI;gCAAK,aAAY;;;;;;;;;;;;;;;;;;;;;uBAIlH,sBACA,qKAAC;gBAAI,WAAU;0BAAS;;;;;uBACxB,cAAc,MAAM,KAAK,kBACzB,qKAAC;gBAAI,WAAU;0BAAa;;;;;qCAE5B,qKAAC;gBAAI,WAAU;gBAA2B,OAAO;oBAC7C,YAAY,iBAAiB,UAAU;oBACvC,OAAO,iBAAiB,KAAK;oBAC7B,SAAS;oBACT,QAAQ;oBACR,cAAc;oBACd,UAAU;oBACV,WAAW,UAAU,SAAS,+BAA+B;oBAC7D,QAAQ,UAAU,SAAS,oCAAoC;gBACnE;;kCAEI,qKAAC;wBAAI,WAAU;wBAAsB,OAAO;4BACxC,OAAO,eAAe,UAAU;4BAChC,aAAa,eAAgB,UAAU,SAAS,oCAAoC,8BAA+B;4BACnH,YAAY,UAAU,SAAS,2BAA2B;4BAC1D,SAAS;4BACT,eAAe;4BACf,YAAY;wBAChB;;0CACI,qKAAC;gCAAI,OAAO;oCACR,SAAS;oCACT,cAAc,UAAU,SAAS,oCAAoC;oCACrE,YAAY,UAAU,SAAS,2BAA2B;gCAC9D;;kDACI,qKAAC;wCAAG,OAAO;4CACP,QAAQ;4CACR,UAAU;4CACV,YAAY;4CACZ,WAAW;wCACf;kDAAG;;;;;;kDACH,qKAAC;wCAAI,OAAO;4CAAE,UAAU;wCAAW;;0DAC/B,qKAAC;gDACG,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,OAAO;oDACH,OAAO;oDACP,SAAS;oDACT,QAAQ,UAAU,SAAS,oCAAoC;oDAC/D,cAAc;oDACd,YAAY,UAAU,SAAS,0BAA0B;oDACzD,OAAO,iBAAiB,KAAK;oDAC7B,SAAS;oDACT,UAAU;oDACV,YAAY;gDAChB;gDACA,SAAS,CAAC;oDACN,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oDAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gDAC/B;gDACA,QAAQ,CAAC;oDACL,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,SAAS,0BAA0B;oDAC1E,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gDAC/B;;;;;;0DAEJ,qKAAC;gDAAE,WAAU;gDAAe,OAAO;oDAC/B,UAAU;oDACV,OAAO;oDACP,KAAK;oDACL,WAAW;oDACX,SAAS;oDACT,UAAU;gDACd;;;;;;;;;;;;;;;;;;0CAGR,qKAAC;gCAAI,OAAO;oCACR,MAAM;oCACN,WAAW;oCACX,SAAS;gCACb;0CACK,cAAc,GAAG,CAAC,CAAA,qBACf,qKAAC;wCAEG,SAAS,IAAM,iBAAiB;wCAChC,OAAO;4CACH,SAAS;4CACT,YAAY;4CACZ,SAAS;4CACT,QAAQ;4CACR,YAAY;4CACZ,YAAY,iBAAiB,KAAK,GAAG,GAChC,UAAU,SAAS,yBAAyB,yBAC7C;4CACJ,YAAY,iBAAiB,KAAK,GAAG,GAAG,sBAAsB;wCAClE;wCACA,cAAc,CAAC;4CACX,IAAI,iBAAiB,KAAK,GAAG,EAAE;gDAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,SAAS,2BAA2B;4CAC9E;wCACJ;wCACA,cAAc,CAAC;4CACX,IAAI,iBAAiB,KAAK,GAAG,EAAE;gDAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG;4CAChC;wCACJ;;0DAEA,qKAAC;gDAAI,OAAO;oDAAE,UAAU;oDAAY,aAAa;gDAAO;;kEACpD,qKAAC,sHAAA,CAAA,UAAK;wDACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;wDAC5B,KAAK,KAAK,IAAI;wDACd,OAAO;wDACP,QAAQ;wDACR,OAAO;4DACH,cAAc;4DACd,WAAW;4DACX,QAAQ,iBAAiB,KAAK,GAAG,GAAG,sBAAsB;wDAC9D;;;;;;kEAEJ,qKAAC;wDAAK,OAAO;4DACT,UAAU;4DACV,QAAQ;4DACR,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,YAAY;4DACZ,cAAc;4DACd,QAAQ;wDACZ;;;;;;;;;;;;0DAEJ,qKAAC;gDAAI,OAAO;oDAAE,MAAM;oDAAG,UAAU;gDAAE;;kEAC/B,qKAAC;wDAAI,OAAO;4DACR,SAAS;4DACT,gBAAgB;4DAChB,YAAY;4DACZ,cAAc;wDAClB;;0EACI,qKAAC;gEAAK,OAAO;oEACT,YAAY,iBAAiB,KAAK,GAAG,GAAG,QAAQ;oEAChD,UAAU;oEACV,OAAO,iBAAiB,KAAK,GAAG,GAAG,YAAY,iBAAiB,KAAK;gEACzE;0EAAI,KAAK,IAAI;;;;;;0EACb,qKAAC;gEAAK,OAAO;oEACT,UAAU;oEACV,SAAS;oEACT,OAAO,iBAAiB,KAAK;gEACjC;0EACK,CAAC;oEACE,MAAM,WAAW,eAAe,KAAK,GAAG;oEACxC,IAAI,SAAS,MAAM,GAAG,GAAG;wEACrB,OAAO,kBAAkB,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,SAAS;oEACpE;oEACA,OAAO;gEACX,CAAC;;;;;;;;;;;;kEAGT,qKAAC;wDAAE,OAAO;4DACN,QAAQ;4DACR,UAAU;4DACV,SAAS;4DACT,YAAY;4DACZ,UAAU;4DACV,cAAc;4DACd,OAAO,iBAAiB,KAAK;wDACjC;kEAAI,YAAY,CAAC,KAAK,GAAG,CAAC,IAAI;;;;;;;;;;;;;uCAjF7B,KAAK,GAAG;;;;;;;;;;;;;;;;oBAyF5B,8BACG,qKAAC;wBAAI,OAAO;4BACR,MAAM;4BACN,SAAS;4BACT,eAAe;4BACf,YAAY,UAAU,SAAS,2BAA2B;wBAC9D;;0CAEI,qKAAC;gCAAI,OAAO;oCACR,SAAS;oCACT,cAAc,UAAU,SAAS,oCAAoC;oCACrE,YAAY,UAAU,SAAS,2BAA2B;oCAC1D,SAAS;oCACT,YAAY;oCACZ,KAAK;gCACT;;kDACI,qKAAC,sHAAA,CAAA,UAAK;wCACF,KAAK,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe,SAAS,qRAAA,CAAA,UAAS;wCACxE,KAAK,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe;wCACtD,OAAO;wCACP,QAAQ;wCACR,OAAO;4CACH,cAAc;4CACd,WAAW;4CACX,QAAQ;wCACZ;;;;;;kDAEJ,qKAAC;;0DACG,qKAAC;gDAAG,OAAO;oDACP,QAAQ;oDACR,UAAU;oDACV,YAAY;oDACZ,OAAO,iBAAiB,KAAK;gDACjC;0DAAI,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe;;;;;;0DACrD,qKAAC;gDAAE,OAAO;oDACN,QAAQ;oDACR,UAAU;oDACV,SAAS;oDACT,OAAO,iBAAiB,KAAK;gDACjC;0DAAG;;;;;;;;;;;;;;;;;;0CAIX,qKAAC;gCAAI,OAAO;oCACR,MAAM;oCACN,WAAW;oCACX,SAAS;oCACT,YAAY,UAAU,SAAS,2BAA2B;gCAC9D;;oCACK,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,IAC/D,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAC5B,qKAAC;4CAEG,OAAO;gDACH,SAAS;gDACT,gBAAgB,IAAI,MAAM,KAAK,QAAQ,aAAa;gDACpD,cAAc;4CAClB;sDAEA,cAAA,qKAAC;gDACG,OAAO;oDACH,UAAU;oDACV,SAAS;oDACT,cAAc;oDACd,YAAY,IAAI,MAAM,KAAK,QACrB,6CACC,UAAU,SAAS,0BAA0B;oDACpD,OAAO,IAAI,MAAM,KAAK,QAAQ,YAAY,iBAAiB,KAAK;oDAChE,WAAW;oDACX,WAAW;gDACf;;kEAEA,qKAAC;wDAAI,OAAO;4DAAE,cAAc;4DAAO,YAAY;wDAAM;kEAAI,IAAI,IAAI;;;;;;kEACjE,qKAAC;wDAAI,OAAO;4DACR,UAAU;4DACV,SAAS;4DACT,WAAW;wDACf;kEAAI,IAAI,SAAS;;;;;;;;;;;;2CAzBhB,IAAI,EAAE;;;;kEA8BnB,qKAAC;wCAAI,OAAO;4CACR,WAAW;4CACX,SAAS;4CACT,OAAO,iBAAiB,KAAK;4CAC7B,SAAS;wCACb;;0DACI,qKAAC;gDAAE,WAAU;gDAAkB,OAAO;oDAClC,UAAU;oDACV,cAAc;oDACd,SAAS;gDACb;;;;;;0DACA,qKAAC;gDAAG,OAAO;oDAAE,cAAc;gDAAO;0DAAG;;;;;;0DACrC,qKAAC;0DAAE;;;;;;;;;;;;kDAGX,qKAAC;wCAAI,KAAK;;;;;;;;;;;;0CAId,qKAAC;gCAAI,OAAO;oCACR,SAAS;oCACT,WAAW,UAAU,SAAS,oCAAoC;oCAClE,YAAY,UAAU,SAAS,2BAA2B;gCAC9D;0CACI,cAAA,qKAAC;oCAAK,UAAU;oCAAmB,OAAO;wCAAE,SAAS;wCAAQ,KAAK;oCAAO;;sDACrE,qKAAC;4CACG,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,OAAO;gDACH,MAAM;gDACN,SAAS;gDACT,QAAQ,UAAU,SAAS,oCAAoC;gDAC/D,cAAc;gDACd,YAAY,UAAU,SAAS,0BAA0B;gDACzD,OAAO,iBAAiB,KAAK;gDAC7B,SAAS;gDACT,UAAU;gDACV,YAAY;4CAChB;4CACA,SAAS,CAAC;gDACN,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;gDAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4CAC/B;4CACA,QAAQ,CAAC;gDACL,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,SAAS,0BAA0B;gDAC1E,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4CAC/B;;;;;;sDAEJ,qKAAC;4CACG,MAAK;4CACL,OAAO;gDACH,OAAO;gDACP,QAAQ;gDACR,QAAQ;gDACR,cAAc;gDACd,YAAY;gDACZ,OAAO;gDACP,QAAQ;gDACR,YAAY;gDACZ,SAAS;gDACT,YAAY;gDACZ,gBAAgB;4CACpB;4CACA,cAAc,CAAC;gDACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gDAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4CAC/B;4CACA,cAAc,CAAC;gDACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gDAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4CAC/B;sDAEA,cAAA,qKAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQhC,CAAC,8BACE,qKAAC;wBAAI,OAAO;4BACR,MAAM;4BACN,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,eAAe;4BACf,SAAS;4BACT,WAAW;4BACX,YAAY,UAAU,SAAS,2BAA2B;wBAC9D;;0CACI,qKAAC;gCAAI,OAAO;oCACR,YAAY,UAAU,SAAS,yBAAyB;oCACxD,cAAc;oCACd,OAAO;oCACP,QAAQ;oCACR,SAAS;oCACT,YAAY;oCACZ,gBAAgB;oCAChB,cAAc;oCACd,QAAQ,UAAU,SAAS,mCAAmC;gCAClE;0CACI,cAAA,qKAAC;oCAAE,WAAU;oCAAmB,OAAO;wCACnC,UAAU;wCACV,OAAO;oCACX;;;;;;;;;;;0CAEJ,qKAAC;gCAAG,OAAO;oCACP,QAAQ;oCACR,UAAU;oCACV,YAAY;oCACZ,OAAO,iBAAiB,KAAK;oCAC7B,YAAY;oCACZ,sBAAsB;oCACtB,qBAAqB;oCACrB,gBAAgB;gCACpB;0CAAG;;;;;;0CACH,qKAAC;gCAAE,OAAO;oCACN,QAAQ;oCACR,UAAU;oCACV,SAAS;oCACT,OAAO,iBAAiB,KAAK;oCAC7B,UAAU;oCACV,YAAY;gCAChB;0CAAG;;;;;;0CAIH,qKAAC;gCACG,kBAAe;gCACf,kBAAe;gCACf,OAAO;oCACH,SAAS;oCACT,QAAQ;oCACR,cAAc;oCACd,YAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,YAAY;oCACZ,QAAQ;oCACR,YAAY;oCACZ,SAAS;oCACT,YAAY;oCACZ,KAAK;oCACL,WAAW;gCACf;gCACA,cAAc,CAAC;oCACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;oCAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC/B;gCACA,cAAc,CAAC;oCACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;oCAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gCAC/B;;kDAEA,qKAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;;0BAQzD,qKAAC;gBAAI,WAAU;gBAAa,IAAG;gBAAa,UAAS;gBAAK,mBAAgB;gBAAkB,eAAY;0BACpG,cAAA,qKAAC;oBAAI,WAAU;8BACX,cAAA,qKAAC;wBAAI,WAAU;wBAAgB,OAAO;4BAClC,YAAY,UAAU,SAAS,YAAY;4BAC3C,OAAO,iBAAiB,KAAK;4BAC7B,QAAQ,UAAU,SAAS,oCAAoC;4BAC/D,cAAc;4BACd,WAAW;wBACf;;0CACI,qKAAC;gCAAI,WAAU;gCAAe,OAAO;oCACjC,cAAc,UAAU,SAAS,oCAAoC;oCACrE,SAAS;gCACb;;kDACI,qKAAC;wCAAG,WAAU;wCAAc,IAAG;wCAAkB,OAAO;4CACpD,UAAU;4CACV,YAAY;4CACZ,OAAO,iBAAiB,KAAK;4CAC7B,SAAS;4CACT,YAAY;4CACZ,KAAK;wCACT;;0DACI,qKAAC;gDAAE,WAAU;gDAAoB,OAAO;oDAAE,OAAO;gDAAU;;;;;;4CAAO;;;;;;;kDAGtE,qKAAC;wCACG,MAAK;wCACL,WAAU;wCACV,mBAAgB;wCAChB,cAAW;wCACX,OAAO;4CACH,YAAY,UAAU,SAAS,0BAA0B;4CACzD,cAAc;4CACd,SAAS;wCACb;;;;;;;;;;;;0CAGR,qKAAC;gCAAI,WAAU;gCAAa,OAAO;oCAAE,SAAS;gCAAO;;kDACjD,qKAAC;wCAAI,OAAO;4CAAE,UAAU;4CAAY,cAAc;wCAAO;;0DACrD,qKAAC;gDACG,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,OAAO;oDACH,OAAO;oDACP,SAAS;oDACT,QAAQ,UAAU,SAAS,oCAAoC;oDAC/D,cAAc;oDACd,YAAY,UAAU,SAAS,0BAA0B;oDACzD,OAAO,iBAAiB,KAAK;oDAC7B,SAAS;oDACT,UAAU;oDACV,YAAY;gDAChB;gDACA,SAAS,CAAC;oDACN,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG;oDAC7B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gDAC/B;gDACA,QAAQ,CAAC;oDACL,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,SAAS,0BAA0B;oDAC1E,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gDAC/B;;;;;;0DAEJ,qKAAC;gDAAE,WAAU;gDAAe,OAAO;oDAC/B,UAAU;oDACV,OAAO;oDACP,KAAK;oDACL,WAAW;oDACX,SAAS;oDACT,UAAU;gDACd;;;;;;;;;;;;kDAGJ,qKAAC;wCAAI,OAAO;4CACR,WAAW;4CACX,WAAW;4CACX,SAAS;wCACb;kDACK,wBACG,qKAAC;4CAAI,OAAO;gDACR,SAAS;gDACT,YAAY;gDACZ,KAAK;gDACL,SAAS;4CACb;;8DACI,qKAAC;oDAAI,OAAO;wDACR,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,YAAY,UAAU,SAAS,0BAA0B;wDACzD,WAAW;oDACf;;;;;;8DACA,qKAAC;;sEACG,qKAAC;4DAAI,OAAO;gEACR,OAAO;gEACP,QAAQ;gEACR,cAAc;gEACd,cAAc;gEACd,YAAY,UAAU,SAAS,0BAA0B;gEACzD,WAAW;4DACf;;;;;;sEACA,qKAAC;4DAAI,OAAO;gEACR,OAAO;gEACP,QAAQ;gEACR,cAAc;gEACd,YAAY,UAAU,SAAS,0BAA0B;gEACzD,WAAW;4DACf;;;;;;;;;;;;;;;;;iEAIR;;gDACK,aAAa,MAAM,GAAG,IACnB,aAAa,GAAG,CAAC,CAAA,qBACb,qKAAC;wDAEG,OAAO;4DACH,SAAS;4DACT,YAAY;4DACZ,gBAAgB;4DAChB,SAAS;4DACT,cAAc;4DACd,cAAc;4DACd,YAAY,UAAU,SAAS,2BAA2B;4DAC1D,QAAQ,UAAU,SAAS,oCAAoC;4DAC/D,YAAY;4DACZ,QAAQ;wDACZ;wDACA,cAAc,CAAC;4DACX,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,SAAS,2BAA2B;4DACjF,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wDACtC;wDACA,cAAc,CAAC;4DACX,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,SAAS,2BAA2B;4DACjF,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;wDACtC;;0EAEA,qKAAC;gEAAI,OAAO;oEACR,SAAS;oEACT,YAAY;oEACZ,KAAK;gEACT;;kFACI,qKAAC,sHAAA,CAAA,UAAK;wEACF,KAAK,KAAK,KAAK,IAAI,qRAAA,CAAA,UAAS;wEAC5B,KAAK,KAAK,IAAI;wEACd,OAAO;wEACP,QAAQ;wEACR,OAAO;4EACH,cAAc;4EACd,WAAW;4EACX,QAAQ;wEACZ;;;;;;kFAEJ,qKAAC;;0FACG,qKAAC;gFAAI,OAAO;oFACR,YAAY;oFACZ,UAAU;oFACV,OAAO,iBAAiB,KAAK;oFAC7B,cAAc;gFAClB;0FAAI,KAAK,IAAI;;;;;;0FACb,qKAAC;gFAAI,OAAO;oFACR,UAAU;oFACV,SAAS;oFACT,OAAO,iBAAiB,KAAK;gFACjC;;oFAAG;oFAAE,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;0EAG1B,qKAAC;gEACG,SAAS;oEACL,iBAAiB;oEACjB,cAAc;oEACd,MAAM,QAAQ,SAAS,cAAc,CAAC;oEACtC,MAAM,gBAAgB,OAAO,SAAS,EAAE,OAAO,YAAY;oEAC3D,IAAI,eAAe;wEACf,cAAc,IAAI;oEACtB;gEACJ;gEACA,OAAO;oEACH,SAAS;oEACT,QAAQ;oEACR,cAAc;oEACd,YAAY;oEACZ,OAAO;oEACP,UAAU;oEACV,YAAY;oEACZ,QAAQ;oEACR,YAAY;gEAChB;gEACA,cAAc,CAAC;oEACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;oEAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gEAC/B;gEACA,cAAc,CAAC;oEACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;oEAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;gEAC/B;;kFAEA,qKAAC;wEAAE,WAAU;;;;;;oEAA2B;;;;;;;;uDAlFvC,KAAK,GAAG;;;;8EAwFrB,qKAAC;oDAAI,OAAO;wDACR,WAAW;wDACX,SAAS;wDACT,OAAO,iBAAiB,KAAK;wDAC7B,SAAS;oDACb;;sEACI,qKAAC;4DAAE,WAAU;4DAAiB,OAAO;gEACjC,UAAU;gEACV,cAAc;gEACd,SAAS;4DACb;;;;;;sEACA,qKAAC;sEAAG;;;;;;sEACJ,qKAAC;sEAAE;;;;;;;;;;;;gDAKV,aAAa,MAAM,GAAG,kBAAkB,MAAM,kBAC3C,qKAAC;oDAAI,OAAO;wDAAE,WAAW;wDAAU,WAAW;oDAAO;8DACjD,cAAA,qKAAC;wDACG,SAAS;wDACT,OAAO;4DACH,SAAS;4DACT,QAAQ,UAAU,SAAS,oCAAoC;4DAC/D,cAAc;4DACd,YAAY;4DACZ,OAAO,iBAAiB,KAAK;4DAC7B,QAAQ;4DACR,YAAY;4DACZ,UAAU;wDACd;wDACA,cAAc,CAAC;4DACX,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,SAAS,0BAA0B;wDAC7E;wDACA,cAAc,CAAC;4DACX,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG;wDAChC;kEACH;;;;;;;;;;;gDAMJ,aAAa,MAAM,GAAG,kBAAkB,MAAM,IAAI,CAC/C,WAAW,IAAI,OAAO,KACf,QAAQ,SAAS,CAAC,MAAM,IAAI,IAC7B,MAAM,MAAM,CAAC,CAAA,OACX,QAAQ,SAAS,CAAC,QAAQ,CAAC,KAAK,GAAG,KACnC,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,GAAG,GACpE,MAAM,AAChB,mBACQ,qKAAC;oDAAI,WAAU;8DACX,cAAA,qKAAC;wDAAO,WAAU;wDAAgC,SAAS;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBnI", "debugId": null}}]}