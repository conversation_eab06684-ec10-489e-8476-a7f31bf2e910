/* [project]/pages/styles/Messages.css [client] (css) */
.chat-container {
  height: 100%;
  font-family: Arial, sans-serif;
  display: flex;
}

.chat-list {
  border-right: 1px solid #a0aec033;
  width: 100%;
  padding: 20px;
  transition: transform .3s;
  overflow-y: auto;
}

.chat-title {
  text-shadow: 0 0 5px #ffffff1a;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: bold;
}

.chat-item {
  cursor: pointer;
  border-radius: 10px;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 6px #0000001a;
}

.chat-item:hover {
  background: #7c8797e6;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px #0003;
}

.avatar-container {
  position: relative;
}

.avatar {
  border: 2px solid #a0aec0;
  border-radius: 50%;
  transition: transform .3s;
}

.chat-item:hover .avatar {
  transform: scale(1.05);
}

.status-indicator {
  border: 2px solid #2d3748;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  position: absolute;
  bottom: 0;
  right: 0;
  box-shadow: 0 0 5px #48bb78;
}

.chat-details {
  flex: 1;
  margin-left: 15px;
}

.chat-header {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.user-name {
  text-transform: capitalize;
  font-size: .875rem;
  font-weight: bold;
}

.timestamp {
  text-shadow: 0 0 2px #ffffff1a;
  font-size: .75rem;
}

.last-message {
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: .75rem;
  overflow: hidden;
}

.reaction {
  color: #f56565;
  font-size: .875rem;
  animation: 1.5s infinite pulse;
}

.chat-panel {
  width: 66.6667%;
  padding: 24px;
  transition: opacity .3s;
  display: none;
}

@media (width >= 1024px) {
  .chat-list {
    width: 33.3333%;
    display: block;
  }

  .chat-panel {
    width: 66.6667%;
    display: flex;
  }
}

@media (width <= 1023px) {
  .chat-container {
    margin: 0;
    padding: 0;
    position: relative;
  }

  .chat-list {
    width: 100%;
    margin: 0;
    padding: 0;
    display: block;
  }

  .chat-panel, .chat-list.chat-list-with-panel {
    display: none;
  }

  .chat-panel.chat-panel-active {
    background: inherit;
    z-index: 1000;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    position: fixed;
    inset: 0;
  }

  .chat-input-form {
    background: inherit;
    z-index: 1001;
    border-top: 1px solid #0000001a;
    width: 100%;
    padding: 10px 15px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .chat-input-form .input-group {
    align-items: center;
    gap: 8px;
    max-width: 100%;
    display: flex;
  }

  .chat-input {
    flex: 1;
    border: 1px solid #0000001a !important;
    border-radius: 20px !important;
    padding: 10px 16px !important;
  }

  .chat-send-btn {
    border-radius: 50% !important;
    justify-content: center !important;
    align-items: center !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    display: flex !important;
  }

  .chat-messages {
    width: 100%;
    overflow-y: auto;
    height: calc(100vh - 140px) !important;
    padding: 0 15px !important;
  }

  .chat-header {
    background: inherit;
    z-index: 1001;
    border-bottom: 1px solid #0000001a;
    width: 100%;
    padding: 15px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .chat-window {
    width: 100% !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 80px 0 !important;
  }

  .chat-item {
    border-bottom: 1px solid #0000000d;
    border-radius: 0;
    margin: 0;
    padding: 12px 15px;
  }
}

.chat-window, .instructions {
  border-radius: 12px;
  width: 100%;
  height: 100%;
  padding: 20px;
  animation: .5s ease-in fadeIn;
  box-shadow: inset 0 0 10px #0000004d;
}

.chat-header {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: bold;
}

.chat-content, .instruction-text {
  margin-top: 1rem;
  line-height: 1.5;
}

.icon-container {
  margin-bottom: 20px;
}

.chat-icon {
  text-align: center;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  padding: 4px;
  font-size: 3rem;
  line-height: 80px;
  display: inline-block;
}

.instruction-title {
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: bold;
}

.instruction-text {
  color: #ccc;
  margin-bottom: 20px;
  font-size: 1rem;
}

.send-button {
  color: #fff;
  cursor: pointer;
  background-color: #6366f1;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1rem;
  transition: background-color .3s;
}

.send-button:hover {
  background-color: #4f46e5;
}

.instructions {
  justify-content: center;
  align-items: center;
  display: flex;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/*# sourceMappingURL=pages_styles_Messages_css_2bef753f._.single.css.map*/