/* [project]/pages/styles/Login.css [client] (css) */
.login-container {
  color: #333;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
}

.login-wrapper {
  background: #fff;
  border-radius: 20px;
  width: 100%;
  max-width: 1000px;
  animation: .8s ease-out forwards slideIn;
  overflow: hidden;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(100vh);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-section {
  background: #fff;
  padding: 2rem;
}

.alert {
  border-radius: 10px;
  margin-bottom: 20px;
  padding: 12px;
  font-size: 1rem;
  animation: .5s ease-in fadeIn;
}

.create-section {
  color: #fff;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.create-section1 {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.Randomimages1 {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.rand {
  text-align: right;
  cursor: pointer;
}

.form-control {
  border: 2px solid #e0e0e0;
  border-radius: 10px;
  padding: 12px;
  font-size: 1.1rem;
  transition: border-color .3s, box-shadow .3s;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 8px #667eea4d;
}

.form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

.login-btn {
  background: linear-gradient(90deg, #667eea, #764ba2);
  border: none;
  border-radius: 10px;
  padding: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  transition: background .3s, transform .2s;
}

.login-btn:hover {
  background: linear-gradient(90deg, #764ba2, #667eea);
  transform: scale(1.05);
}

.create-btn {
  border-radius: 10px;
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all .3s;
}

.create-btn:hover {
  color: #667eea;
  background: #fff;
  transform: scale(1.05);
}

.form-label {
  color: #333;
  font-family: Poppins, sans-serif;
}

@media (width <= 767.98px) {
  .login-wrapper {
    max-width: 100%;
    animation: .6s ease-out forwards slideInMobile;
  }

  .login-section, .create-section {
    padding: 1.5rem;
  }

  .login-btn, .create-btn, .form-control {
    font-size: 1rem;
  }
}

@media (width >= 767.98px) {
  .login-container {
    background: linear-gradient(135deg, #667eea, #764ba2);
    margin-top: 80px;
  }

  .create-section1 {
    margin-top: 120px;
  }
}

@media (width <= 575.98px) {
  .create-section1 {
    height: 100%;
    margin-top: 20px;
  }

  .login-wrapper {
    border-radius: 1px;
  }

  .login-section, .create-section {
    padding: 1rem;
  }

  .create-section {
    height: 300px;
  }

  .login-btn, .create-btn {
    width: 100%;
  }
}

@keyframes slideInMobile {
  0% {
    opacity: 0;
    transform: translateY(50vh);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.custom-loader-overlay {
  z-index: 9999;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

.custom-loader-overlay svg {
  width: 150px;
  height: 150px;
}

.loading-text {
  color: #fff;
  margin-top: 20px;
  font-size: 1.2rem;
  font-weight: bold;
}


/* [project]/pages/styles/Home.css [client] (css) */
.loading {
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  background: linear-gradient(135deg, #1a1a1a, #2c2c2c);
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-family: Inter, sans-serif;
  font-size: 1.5rem;
  font-weight: 500;
  animation: 1.5s ease-in-out infinite pulse;
  display: flex;
}

.loading:before {
  content: "";
  background: #0f8;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin-right: 10px;
  animation: .6s ease-in-out infinite alternate bounce;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-8px);
  }
}

.error {
  color: #ff4d4d;
  text-align: center;
  background: linear-gradient(135deg, #2c0b0b, #3d1c1c);
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  max-width: 600px;
  min-height: 100vh;
  margin: 0 auto;
  padding: 20px;
  font-family: Inter, sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  animation: .4s ease-in-out shake, .8s ease-out fadeIn;
  display: flex;
  box-shadow: 0 4px 15px #00000080;
}

.error:before {
  content: "⚠";
  margin-right: 10px;
  font-size: 1.5rem;
  animation: 1.2s ease-in-out infinite pulseIcon;
  display: inline-block;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseIcon {
  0%, 100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }
}

.home-container {
  perspective: 1000px;
  min-height: calc(100vh - 60px);
  padding: 20px;
  overflow-y: auto;
}

.home-title {
  text-align: center;
  color: #3b82f6;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 40px;
  font-size: 1.8rem;
  font-weight: 800;
  animation: 2s infinite pulse;
}

.home-grid {
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
}

.notification-section, .user-section {
  background: #ffffff0d;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 20px #0000001a;
}

.section-title {
  color: inherit;
  margin-bottom: 25px;
  font-size: 1.8rem;
  font-weight: 700;
  display: inline-block;
  position: relative;
}

.section-title:after {
  content: "";
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 2px;
  width: 100%;
  height: 3px;
  animation: 1s slideInLeft;
  position: absolute;
  bottom: -5px;
  left: 0;
}

.notification-list {
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  display: flex;
  overflow-y: auto;
}

.notification-card {
  cursor: pointer;
  border-radius: 12px;
  align-items: center;
  padding: 15px;
  transition: transform .3s, box-shadow .3s, background .3s;
  display: flex;
  box-shadow: 0 4px 15px #00000026;
}

.notification-card:hover {
  background: #3b82f61a;
  transform: translateX(10px);
  box-shadow: 0 6px 20px #0003;
}

.notif-avatar {
  object-fit: cover;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-right: 15px;
  transition: transform .3s;
}

.notification-card:hover .notif-avatar {
  transform: scale(1.1);
}

.notif-content {
  flex-direction: column;
  flex: 1;
  display: flex;
}

.notif-message {
  font-size: 1rem;
  font-weight: 500;
}

.notif-time {
  color: #60a5fa;
  margin-top: 5px;
  font-size: .8rem;
}

.user-list {
  grid-template-columns: 1fr;
  gap: 20px;
  display: grid;
}

.user-card {
  width: 100%;
  height: 80px;
  transform-style: preserve-3d;
  cursor: pointer;
  transition: transform .6s;
  position: relative;
}

.user-card.flipped {
  transform: rotateY(180deg);
}

.card-front, .card-back {
  backface-visibility: hidden;
  border-radius: 12px;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 15px;
  transition: box-shadow .3s;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  box-shadow: 0 6px 25px #00000026;
}

.card-front:hover, .card-back:hover {
  box-shadow: 0 8px 30px #0003;
}

.card-front {
  z-index: 2;
}

.card-back {
  justify-content: center;
  transform: rotateY(180deg);
}

.user-avatar {
  object-fit: cover;
  border: 3px solid #3b82f6;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  margin-right: 20px;
  transition: transform .4s, border-color .3s;
}

.user-card:hover .user-avatar {
  border-color: #60a5fa;
  transform: scale(1.15)rotate(5deg);
}

.user-name {
  letter-spacing: .5px;
  flex: 1;
  font-size: 1.2rem;
  font-weight: 600;
}

.user-bio {
  color: #60a5fa;
  text-align: center;
  font-size: 1rem;
  font-style: italic;
}

.follow-btn, .accept-btn {
  color: #fff;
  cursor: pointer;
  border: none;
  border-radius: 25px;
  padding: 8px 20px;
  font-size: .95rem;
  font-weight: 600;
  transition: transform .3s, box-shadow .3s;
  box-shadow: 0 4px 15px #3b82f64d;
}

.follow-btn:hover, .accept-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px #3b82f680;
}

.follow-btn:active, .accept-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px #3b82f633;
}

.simulate-buttons {
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
  display: flex;
}

.simulate-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: .9rem;
  transition: transform .3s, box-shadow .3s;
}

.simulate-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px #3b82f666;
}

.simulate-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px #3b82f633;
}

.loading, .error {
  text-align: center;
  color: #3b82f6;
  padding: 50px;
  font-size: 1.5rem;
  animation: .5s fadeIn;
}

.error {
  color: #ef4444;
  font-weight: 500;
}

@keyframes slideInLeft {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.8);
  }

  60% {
    opacity: 1;
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@media (width <= 768px) {
  .home-grid {
    grid-template-columns: 1fr;
  }

  .home-title {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.4rem;
  }

  .notification-list {
    max-height: 300px;
  }

  .notification-card {
    padding: 10px;
  }

  .notif-avatar {
    width: 35px;
    height: 35px;
  }

  .notif-message {
    font-size: .9rem;
  }

  .notif-time {
    font-size: .7rem;
  }

  .user-card {
    height: 70px;
  }

  .user-avatar {
    width: 45px;
    height: 45px;
    margin-right: 15px;
  }

  .user-name {
    font-size: 1rem;
  }

  .user-bio {
    font-size: .9rem;
  }

  .follow-btn, .accept-btn {
    padding: 6px 16px;
    font-size: .85rem;
  }

  .simulate-btn {
    padding: 5px 10px;
    font-size: .8rem;
  }

  .home-container {
    padding: 2px;
    position: relative;
  }
}

.notification-card, .user-card {
  animation: .5s bounceIn;
}

.home-content {
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
}

.image-section {
  flex: 1;
}

.image-list {
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  gap: 15px;
  padding: 10px 0;
  display: flex;
  overflow-x: auto;
}

.image-card {
  scroll-snap-align: start;
  text-align: center;
  flex: none;
  position: relative;
}

.image-wrapper {
  cursor: pointer;
  transition: transform .3s, box-shadow .3s;
  position: relative;
}

.image-item {
  object-fit: cover;
  border: 2.5px solid #3131bf;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  padding: 2.5px;
  transition: border-image .3s;
}

.image-username {
  margin-top: 5px;
  font-size: .8rem;
  transition: color .3s;
  display: block;
}

.tooltip {
  color: #fff;
  text-align: center;
  z-index: 10;
  border-radius: 5px;
  flex-direction: column;
  padding: 5px 10px;
  font-size: .8rem;
  display: none;
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
}

.image-wrapper:hover .tooltip {
  display: flex;
}

.suggested-section {
  border-radius: 15px;
  flex: 0 0 300px;
  padding: 15px;
  box-shadow: 5px 5px 15px #0000004d, -5px -5px 15px #ffffff1a;
}

.suggested-header {
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  display: flex;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.see-all {
  color: #1e90ff;
  font-size: .9rem;
  text-decoration: none;
}

.see-all:hover {
  text-decoration: none;
}

.follow-all-btn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  border: none;
  border-radius: 5px;
  width: 100%;
  margin-bottom: 15px;
  padding: 8px;
  font-size: .9rem;
  transition: background .3s, transform .3s;
}

.follow-all-btn:hover {
  background: linear-gradient(45deg, #033483, #054a9d);
  transform: scale(1.02);
}

.suggested-grid {
  scroll-behavior: smooth;
  grid-template-columns: 1fr;
  gap: 15px;
  max-height: 400px;
  display: grid;
  overflow-y: auto;
}

::-webkit-scrollbar {
  display: none;
}

.suggested-card {
  cursor: default;
  background: #ffffff0d;
  border-radius: 10px;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  transition: transform .3s, box-shadow .3s;
  display: flex;
}

.suggested-card:hover {
  box-shadow: 0 5px 15px #0003;
}

.suggested-image-wrapper {
  margin-bottom: 10px;
  position: relative;
}

.suggested-image {
  object-fit: cover;
  border: 2px solid;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  transition: transform .3s;
}

.suggested-card:hover .suggested-image {
  transform: scale(1.1);
}

.suggested-info {
  text-align: center;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  display: flex;
}

.suggested-name {
  font-size: .9rem;
  font-weight: 500;
}

.suggested-followed-by {
  color: #b0b0b0;
  font-size: .75rem;
}

.follow-btn {
  cursor: pointer;
  color: #fff;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  border: none;
  border-radius: 5px;
  margin-top: 5px;
  padding: 5px 15px;
  font-size: .9rem;
  transition: transform .3s, background .3s;
}

.follow-btn:hover {
  background: linear-gradient(45deg, #2563eb, #3b82f6);
  transform: scale(1.05);
}

.modal-overlay {
  opacity: 0;
  z-index: 1000;
  background: #00000080;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  animation: .3s forwards fadeSlideIn;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  transform: translateY(-20px);
}

@keyframes fadeSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content {
  background: #1e293b;
  border-radius: 15px;
  width: 90%;
  max-width: 400px;
  padding: 20px;
  animation: .3s modalFadeIn;
  position: relative;
}

.modal-close {
  color: #fff;
  cursor: pointer;
  background: none;
  border: none;
  font-size: 1.5rem;
  position: absolute;
  top: 10px;
  right: 10px;
}

.modal-body {
  flex-direction: column;
  align-items: center;
  gap: 10px;
  display: flex;
}

.modal-image {
  object-fit: cover;
  border: 2px solid;
  border-radius: 50%;
  width: 100px;
  height: 100px;
}

.modal-body h3 {
  color: #b0b0b0;
  margin: 10px 0 5px;
  font-size: 1.2rem;
}

.modal-body p {
  color: #b0b0b0;
  text-align: center;
  font-size: .9rem;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px #0fc, 0 0 20px #0fc, 0 0 30px #0fc;
  }

  to {
    box-shadow: 0 0 20px #0fc, 0 0 30px #0fc, 0 0 40px #0fc;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: .5s fadeIn;
}

@media (width <= 768px) {
  .home-content {
    flex-direction: column;
  }

  .image-list {
    -webkit-overflow-scrolling: touch;
    gap: 10px;
    display: flex;
    overflow-x: auto;
  }

  .image-card {
    flex: 0 0 calc(25% - 10px);
  }

  .image-item {
    width: 80px;
    height: 80px;
  }

  .suggested-section {
    flex: 1;
  }
}

@media (width >= 769px) {
  .image-list {
    gap: 15px;
    display: flex;
    overflow-x: auto;
  }

  .image-card {
    flex: 0 0 calc(14.28% - 15px);
  }

  .image-item {
    width: 80px;
    height: 80px;
  }
}

.image-list::-webkit-scrollbar {
  display: none;
}

.image-list {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.image-card.session-user {
  margin-right: 1.2rem;
  padding-right: 1rem;
  position: relative;
}

.image-card.session-user:after {
  content: "";
  background: linear-gradient(#4f46e5, #9333ea);
  border-radius: 2px;
  width: 2px;
  height: 80%;
  position: absolute;
  top: 5%;
  right: 0;
}


/* [project]/pages/styles/DashboardLayout.css [client] (css) */
.dashboard-wrapper {
  flex-direction: column;
  min-height: 100vh;
  font-family: Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
  transition: all .4s;
  display: flex;
  overflow: hidden;
}

.sleek-navbar {
  padding: 1rem 1.5rem;
  box-shadow: 0 2px 10px #0000004d;
  background: linear-gradient(90deg, #0f172a 0%, #1e293b 100%) !important;
}

.sleek-navbar.bg-light {
  box-shadow: 0 2px 10px #0000001a;
  background: linear-gradient(90deg, #f1f5f9 0%, #e2e8f0 100%) !important;
}

.sleek-brand {
  letter-spacing: 1.5px;
  font-size: 1.6rem;
  transition: transform .3s;
  color: #3b82f6 !important;
}

.sleek-brand:hover {
  transform: scale(1.05);
}

.sleek-toggler {
  background: none;
  border: none;
  padding: .5rem;
  transition: transform .3s;
}

.sleek-toggler:hover {
  transform: rotate(90deg);
}

.sleek-toggler.dark-toggler .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(226, 232, 240, 0.95)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

.sleek-toggler.light-toggler .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(30, 41, 59, 0.95)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
}

.sleek-sidebar {
  scroll-behavior: smooth;
  z-index: 1000;
  -webkit-overflow-scrolling: touch;
  width: 300px;
  height: 100dvh;
  transition: left .3s cubic-bezier(.4, 0, .2, 1);
  position: fixed;
  top: 0;
  left: -300px;
  overflow: hidden auto;
  box-shadow: 4px 0 20px #0006;
  background: linear-gradient(#0f172a 0%, #1e293b 100%) !important;
}

.sleek-sidebar.bg-light {
  box-shadow: 4px 0 15px #0000001a;
  background: linear-gradient(#f1f5f9 0%, #e2e8f0 100%) !important;
}

.sleek-sidebar.open {
  scroll-behavior: smooth;
  left: 0;
}

.sleek-header h4 {
  color: #3b82f6;
  letter-spacing: 2px;
  font-size: 1.4rem;
}

.sleek-close {
  filter: brightness(1.8);
  transition: transform .3s;
}

.sleek-close:hover {
  transform: rotate(90deg);
}

.sleek-nav {
  margin-top: 1rem;
}

.sleek-nav-link {
  color: inherit;
  background: #ffffff0d;
  border-radius: 12px;
  align-items: center;
  margin: 8px 0;
  padding: 12px 20px;
  font-size: 1.1rem;
  font-weight: 500;
  transition: all .3s;
  display: flex;
}

.sleek-nav-link:hover {
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: translateX(8px);
  box-shadow: 0 4px 15px #3b82f64d;
  color: #fff !important;
}

.sleek-footer {
  background: #0000000d;
  border-top: 1px solid #ffffff1a;
  border-radius: 0 0 12px 12px;
}

.sleek-profile {
  background: #ffffff14;
  border-radius: 12px;
  padding: 15px;
  transition: all .3s;
}

.sleek-profile:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px #0003;
}

.sleek-avatar {
  border: 2px solid #3b82f6;
  padding: 2px;
  transition: transform .3s;
}

.sleek-avatar:hover {
  transform: scale(1.1);
}

.sleek-username {
  font-size: 1.1rem;
}

.sleek-status {
  opacity: .9;
  font-size: .85rem;
}

.sleek-status.online:before {
  content: "• ";
  color: #22c55e;
  vertical-align: middle;
  font-size: 1.2rem;
}

.sleek-btn {
  border: none;
  border-radius: 30px;
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
  box-shadow: 0 2px 10px #0000001a;
}

.theme-toggle {
  color: #fff;
  background: #3b82f6;
}

.theme-toggle:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px #3b82f666;
}

.logout-btn {
  color: #fff;
  background: #ef4444;
}

.logout-btn:hover {
  background: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px #ef444466;
}

.sleek-main {
  border-radius: 20px 0 0 20px;
  flex: 1;
  margin-left: 0;
  transition: all .4s;
  box-shadow: inset 0 0 15px #0000000d;
}

.sleek-container {
  max-width: 1400px;
  margin: 0 auto;
}

@media (width >= 992px) {
  .sleek-sidebar {
    left: 0;
  }

  .sleek-main {
    margin-left: 300px;
  }

  .sleek-sidebar.bg-light {
    box-shadow: 4px 0 15px #0000001a;
  }
}

.user-result {
  gap: 40px;
  transition: background-color .3s;
  display: flex;
}

.user-result:hover {
  background-color: #69696980;
}

.fade-in {
  opacity: 0;
  animation: .5s ease-in-out forwards fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}


/* [project]/pages/styles/globals.css [client] (css) */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #fff;
  background: linear-gradient(135deg, #667eea, #764ba2);
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

::-webkit-scrollbar {
  display: none;
}

h2, h3 {
  font-family: Poppins, sans-serif;
  font-weight: 700;
}

p {
  font-family: Poppins, sans-serif;
  font-size: 1.1rem;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;
}

@media (width <= 767.98px) {
  h2, h3 {
    font-size: 1.5rem;
  }

  p {
    font-size: .9rem;
  }
}

@media (width >= 767.98px) {
  body {
    height: 100vh;
  }
}


/* [project]/pages/styles/Profile.css [client] (css) */
.skeleton {
  background: #ddd;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.skeleton:after {
  content: "";
  background: linear-gradient(90deg, #0000, #fff6, #0000);
  width: 150px;
  height: 100%;
  animation: 1.5s infinite shimmer;
  position: absolute;
  top: 0;
  left: -150px;
}

@keyframes shimmer {
  0% {
    left: -150px;
  }

  100% {
    left: 100%;
  }
}

.skeleton-avatar {
  border-radius: 50%;
  width: 60px;
  height: 60px;
}

.skeleton-username {
  width: 150px;
  height: 25px;
}

.skeleton-button {
  border-radius: 5px;
  width: 100px;
  height: 30px;
}

.skeleton-icon {
  border-radius: 5px;
  width: 30px;
  height: 30px;
}

.skeleton-text {
  width: 60px;
  height: 20px;
  margin-bottom: 5px;
}

.skeleton-line {
  width: 100%;
  height: 15px;
  margin-top: 10px;
}

.skeleton-line.short {
  width: 70%;
}

.profile-container {
  justify-content: center;
  padding: 20px;
  display: flex;
}

.profile-card:hover {
  box-shadow: 0 6px 20px #ff4d4d33;
}

.profile-content {
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  display: flex;
}

.profile-avatar {
  object-fit: cover;
  border: 3px solid gray;
  border-radius: 50%;
  width: 110px;
  height: 110px;
  transition: transform .3s;
}

.profile-card:hover .profile-avatar {
  transform: scale(1.05);
}

.profile-info {
  text-align: left;
  flex: 1;
}

.profile-username {
  color: #0f172a;
  margin-bottom: 5px;
  font-size: 1.5rem;
  font-weight: 600;
}

.profile-name {
  color: #0f172a;
  margin-bottom: 8px;
  font-size: 1.1rem;
  font-weight: 400;
}

.profile-bio {
  color: #4a5568;
  margin-bottom: 10px;
  font-size: .9rem;
  line-height: 1.4;
}

.profile-stats {
  gap: 15px;
  display: flex;
}

.stat-item {
  color: #0f172a;
  font-size: .9rem;
}

.stat-item strong {
  color: #ff4d4d;
}

.ot-but {
  display: none;
}

.edit-button {
  color: #f1f5f9;
  cursor: pointer;
  background: #ff4d4d;
  border: none;
  border-radius: 10px;
  width: 100%;
  padding: 12px;
  font-size: 1rem;
  font-weight: 600;
  transition: background .3s, box-shadow .3s;
  display: block;
}

.edit-button:hover {
  background: #e04343;
  box-shadow: 0 0 15px #ff4d4d66;
}

.edit-button:active {
  box-shadow: none;
}

.edit-profile-layout {
  height: 98vh;
  display: flex;
  overflow: hidden;
}

.sidebar-title {
  margin-bottom: 20px;
  font-size: 1.8rem;
  font-weight: 600;
}

.sidebar-section {
  margin-bottom: 20px;
}

.sidebar-section-title {
  margin-bottom: 10px;
  font-size: 1.2rem;
  font-weight: 500;
}

.sidebar-section-content {
  color: gray;
  font-size: .9rem;
  line-height: 1.5;
}

.sidebar-buttons {
  flex-direction: column;
  gap: 10px;
  display: flex;
}

.sidebar-button {
  color: #f1f5f9;
  cursor: pointer;
  background: #3a3a3a;
  border: none;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 1rem;
  font-weight: 500;
  transition: background .3s, transform .1s;
}

.sidebar-button:hover {
  background: #4a4a4a;
  transform: translateX(5px);
}

.sidebar-button:active {
  transform: translateX(0);
}

.sidebar-button.active {
  cursor: default;
  background: #3b82f6;
}

.sidebar-button.active:hover {
  background: #2563eb;
  transform: none;
}

.edit-profile-card {
  border-radius: 15px;
  width: 100%;
  max-width: 600px;
  padding: 40px;
  box-shadow: 0 2px 10px #0000004d;
}

.edit-profile-title {
  margin-bottom: 30px;
  font-size: 1.5rem;
  font-weight: 600;
}

.edit-form {
  flex-direction: column;
  gap: 20px;
  display: flex;
}

.form-group {
  flex-direction: column;
  gap: 8px;
  display: flex;
}

.form-group label {
  font-size: 1rem;
  font-weight: 500;
}

.form-group input, .form-group textarea {
  background: #e2e8f0;
  border: none;
  border-radius: 8px;
  padding: 10px;
  font-size: 1rem;
  transition: background .3s, transform .1s;
}

.form-group input:focus, .form-group textarea:focus {
  background: #d1d5db;
  outline: none;
  transform: scale(1.01);
}

.form-group textarea {
  resize: vertical;
}

.avatar-preview {
  object-fit: cover;
  border: 2px solid #ff4d4d;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  margin-top: 10px;
  transition: transform .3s;
}

.avatar-preview:hover {
  transform: scale(1.1);
}

.form-instructions {
  color: #4a5568;
  margin-top: 20px;
  font-size: .1rem;
}

.visibility-link {
  color: #ff4d4d;
  cursor: pointer;
  transition: color .3s;
}

.visibility-link:hover {
  color: #e04343;
  text-decoration: underline;
}

.form-buttons {
  justify-content: space-between;
  gap: 10px;
  margin-top: 20px;
  display: flex;
}

.submit-button, .cancel-button {
  cursor: pointer;
  border: none;
  border-radius: 25px;
  flex: 1;
  padding: 12px;
  font-size: 1rem;
  font-weight: 600;
  transition: background .3s, transform .1s;
}

.submit-button {
  color: #f1f5f9;
  background: #3b82f6;
}

.submit-button:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

.submit-button:active {
  transform: translateY(0);
}

.cancel-button {
  color: #f1f5f9;
  background: #3a3a3a;
}

.cancel-button:hover {
  background: #4a4a4a;
  transform: translateY(-2px);
}

.cancel-button:active {
  transform: translateY(0);
}

.loading, .error {
  text-align: center;
  color: #f1f5f9;
  padding: 20px;
  font-size: 1.2rem;
}

.error {
  color: #ff4d4d;
}

@media (width >= 770px) {
  .edit-profile-main {
    flex: 1;
    justify-content: center;
    align-items: flex-start;
    padding: 40px;
    display: flex;
  }

  .edit-profile-sidebar {
    border-right: 1px solid #3a3a3a;
    flex-shrink: 0;
    width: 350px;
    padding: 20px;
    overflow-y: auto;
  }

  .p-car {
    justify-content: space-between;
    width: 600px;
  }

  .edit-profile-main {
    scroll-behavior: smooth;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .p-card {
    justify-content: space-around;
    gap: 40px;
  }

  .profile-card {
    background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 15px;
    width: 700px;
    padding: 25px;
    transition: box-shadow .3s;
    box-shadow: 0 4px 15px #0003;
  }

  .edit-profile-sidebar {
    display: block;
  }
}

@media (width <= 768px) {
  html, body {
    overflow: hidden auto;
  }

  .edit-profile-sidebar {
    display: none;
  }

  .p-car {
    gap: 14px;
  }

  .p-card {
    gap: 20px;
  }

  .edit-profile-main {
    padding: 4px;
  }

  .profile-card {
    background: linear-gradient(145deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 15px;
    width: 100%;
    max-width: 100%;
    padding: 25px;
    transition: box-shadow .3s;
    box-shadow: 0 4px 15px #0003;
  }

  .profile-content {
    text-align: center;
    flex-direction: column;
    align-items: center;
  }

  .profile-avatar {
    width: 60px;
    height: 60px;
  }

  .ot-but {
    display: block;
  }

  .ot-butt {
    display: none;
  }

  .profile-username {
    font-size: 1.4rem;
  }

  .profile-name {
    font-size: 1rem;
  }

  .profile-stats {
    flex-direction: column;
    gap: 8px;
  }

  .edit-profile-layout {
    flex-direction: column;
  }

  .edit-profile-sidebar {
    border-bottom: 1px solid #3a3a3a;
    border-right: none;
    width: 100%;
  }

  .edit-profile-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
    display: flex;
    overflow: visible;
  }

  .edit-profile-card {
    padding: 8px;
  }

  .edit-profile-title {
    font-size: 1.5rem;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-overlay {
  z-index: 1000;
  background: #0f172ab3;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
}

.modal-content {
  color: #fff;
  text-align: center;
  background: #16213e;
  border-radius: 15px;
  width: 100%;
  max-width: 400px;
  padding: 25px;
  animation: .3s ease-out forwards fadeIn;
  box-shadow: 0 4px 20px #000000e6;
}

.modal-title {
  margin-bottom: 15px;
  font-size: 1.5rem;
  font-weight: 600;
}

.modal-text {
  margin-bottom: 20px;
  font-size: 1rem;
  line-height: 1.5;
}

.modal-actions {
  justify-content: space-between;
  gap: 10px;
  display: flex;
}

.modal-btn {
  cursor: pointer;
  border: none;
  border-radius: 10px;
  flex: 1;
  padding: 10px;
  font-size: 1rem;
  font-weight: 500;
  transition: background .3s, transform .2s, box-shadow .3s;
}

.modal-btn-success {
  color: #f1f5f9;
  background: #3b82f6;
}

.modal-btn-success:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px #ff4d4d4d;
}

.modal-btn-success:active {
  box-shadow: none;
  transform: translateY(0);
}

.modal-btn-cancel {
  color: #f1f5f9;
  background: #3a3a3a;
}

.modal-btn-cancel:hover {
  background: #4a4a4a;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px #3a3a3a4d;
}

.modal-btn-cancel:active {
  box-shadow: none;
  transform: translateY(0);
}

.custom-alert-container {
  z-index: 9999;
  animation: .5s forwards slideIn;
  position: fixed;
  top: 30px;
  right: 30px;
}

.custom-alert {
  color: #fff;
  background-color: #dc3545;
  border-radius: 8px;
  min-width: 300px;
  padding: 16px 24px;
  font-weight: bold;
  position: relative;
  box-shadow: 0 6px 12px #0000004d;
}

.timer-bar {
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  height: 5px;
  animation: 4s linear forwards countdownBar;
  position: absolute;
  bottom: 0;
  left: 0;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes countdownBar {
  from {
    width: 100%;
  }

  to {
    width: 0%;
  }
}

.no-followers-container {
  animation: .6s ease-in-out fadeInUp;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-wrapper {
  animation: .5s ease-in-out iconPop;
}

@keyframes iconPop {
  0% {
    opacity: 0;
    transform: scale(.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}


/* [project]/pages/styles/Chats.css [client] (css) */
.chat-wrapper {
  z-index: 5;
  border-radius: 15px;
  flex-direction: column;
  transition: all .4s;
  display: flex;
  position: fixed;
  inset: 10px 10px 10px 300px;
  overflow: hidden;
  box-shadow: 0 4px 20px #0000001a;
}

.chat-header {
  background: #0000000d;
  border-bottom: 1px solid #ffffff1a;
  flex-shrink: 0;
  padding: 15px 20px;
}

.chat-title {
  color: #3b82f6;
  letter-spacing: 1px;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.chat-messages {
  flex-direction: column;
  flex: 1;
  gap: 15px;
  padding: 20px;
  display: flex;
  overflow-y: auto;
}

.message {
  align-items: flex-end;
  animation: .3s slideIn;
  display: flex;
}

.message-you {
  justify-content: flex-end;
}

.message-friend {
  justify-content: flex-start;
}

.message-bubble {
  border-radius: 20px;
  max-width: 70%;
  padding: 12px 18px;
  transition: transform .2s;
  position: relative;
  box-shadow: 0 2px 10px #0000001a;
}

.message-bubble:hover {
  transform: scale(1.02);
}

.message-text {
  word-wrap: break-word;
  font-size: 1rem;
  display: block;
}

.message-timestamp {
  opacity: .7;
  margin-top: 5px;
  font-size: .75rem;
  display: block;
}

.chat-input-form {
  background: #0000000d;
  border-top: 1px solid #ffffff1a;
  flex-shrink: 0;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  display: flex;
}

.chat-input {
  border: none;
  border-radius: 30px;
  flex: 1;
  padding: 12px 20px;
  font-size: 1rem;
  transition: all .3s;
  box-shadow: 0 2px 10px #0000001a;
}

.chat-input:focus {
  outline: none;
  box-shadow: 0 4px 15px #0003;
}

.chat-send-btn {
  color: #fff;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  padding: 12px 20px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 2px 10px #3b82f64d;
}

.chat-send-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px #3b82f680;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-messages::-webkit-scrollbar-track {
  background: none;
}

@media (width <= 991px) {
  .chat-wrapper {
    top: 70px;
    left: 10px;
  }
}

.chat-list-with-panel {
  width: 350px !important;
}

@media (width <= 1024px) {
  .chat-container {
    flex-direction: column;
    height: 90vh;
  }

  .chat-list {
    border-bottom: 1px solid #ffffff1a;
    border-right: none;
    max-height: none;
    width: 100% !important;
  }

  .chat-panel {
    width: 100% !important;
    display: flex !important;
  }

  .chat-list-with-panel {
    display: none;
  }
}

@media (width <= 768px) {
  .chat-container {
    height: 95vh;
  }

  .chat-item {
    padding: 10px 15px;
  }

  .chat-messages {
    max-height: 300px;
  }

  .message-bubble {
    max-width: 85%;
  }
}

@media (width <= 480px) {
  .chat-input-form {
    padding: 15px;
  }

  .chat-input {
    padding: 10px 14px;
    font-size: .9rem;
  }

  .chat-send-btn {
    width: 45px;
    height: 45px;
  }

  .chat-title {
    font-size: 1.2rem;
  }

  .user-name {
    font-size: .8rem;
  }

  .last-message {
    font-size: .7rem;
  }
}

.typing-indicator {
  align-items: center;
  gap: 4px;
  padding: 8px 0;
  display: flex;
}

.typing-indicator span {
  background-color: #6b7280;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  animation: 1.4s ease-in-out infinite typing;
}

.typing-indicator span:first-child {
  animation-delay: -.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    opacity: .5;
    transform: scale(.8);
  }

  40% {
    opacity: 1;
    transform: scale(1);
  }
}

.status-indicator {
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: background-color .3s;
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #0000001a;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #0000004d;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #00000080;
}

.chat-input-form {
  background: inherit;
  z-index: 10;
  border-top: 1px solid #0000001a;
  padding: 15px 20px;
  position: sticky;
  bottom: 0;
}

.chat-input-form .input-group {
  align-items: center;
  gap: 10px;
  display: flex;
}

.chat-input {
  border: 1px solid #0000001a;
  border-radius: 25px;
  outline: none;
  flex: 1;
  padding: 12px 20px;
  transition: all .3s;
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.chat-send-btn {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 45px;
  height: 45px;
  transition: all .3s;
  display: flex;
}

.chat-send-btn:hover {
  background: #2563eb;
  transform: scale(1.05);
}

.chat-send-btn:active {
  transform: scale(.95);
}

.message-footer {
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  display: flex;
}

.message-timestamp {
  opacity: .7;
  font-size: .75rem;
}

.delivery-status {
  opacity: .8;
  font-size: .8rem;
}

.delivery-status i {
  font-size: 12px;
}

.message-bubble {
  word-wrap: break-word;
  border-radius: 18px;
  max-width: 70%;
  padding: 8px 12px;
  position: relative;
}

.message-you .message-bubble {
  color: #fff;
  background: #3b82f6;
  margin-left: auto;
}

.message-friend .message-bubble {
  background: #0000000d;
  margin-right: auto;
}

.message-text {
  line-height: 1.4;
  display: block;
}

.typing-text {
  color: #10b981;
  align-items: center;
  gap: 8px;
  font-style: italic;
  display: flex;
}

.typing-text .typing-indicator {
  align-items: center;
  gap: 2px;
  display: flex;
}

.typing-text .typing-indicator span {
  background-color: #10b981;
  border-radius: 50%;
  width: 4px;
  height: 4px;
  animation: 1.4s ease-in-out infinite typing;
}

.typing-text .typing-indicator span:first-child {
  animation-delay: -.32s;
}

.typing-text .typing-indicator span:nth-child(2) {
  animation-delay: -.16s;
}

.status-indicator {
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: background-color .3s;
  position: absolute;
  bottom: 2px;
  right: 2px;
  box-shadow: 0 0 0 1px #0000001a;
}

.status-indicator.online {
  background-color: #10b981;
  box-shadow: 0 0 0 1px #10b9814d;
}

.status-indicator.offline {
  background-color: #ef4444;
  box-shadow: 0 0 0 1px #ef44444d;
}

.unread-badge {
  color: #fff;
  background: #ff3b30;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  height: 20px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 600;
  animation: 2s infinite unreadPulse;
  display: flex;
  position: absolute;
  top: 8px;
  right: 8px;
  box-shadow: 0 2px 4px #ff3b304d;
}

@keyframes unreadPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: .8;
    transform: scale(1.1);
  }
}

.chat-item {
  cursor: pointer;
  border-radius: 12px;
  align-items: center;
  padding: 12px;
  transition: all .3s;
  display: flex;
  position: relative;
}

.chat-item:hover {
  background-color: #0000000d;
}

.chat-item.selected {
  background-color: #3b82f61a;
  border-left: 4px solid #3b82f6;
}

.status-indicator {
  z-index: 2;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  transition: all .3s;
  position: absolute;
  bottom: 2px;
  right: 2px;
  box-shadow: 0 0 0 1px #0000001a;
}


/* [project]/pages/styles/Messages.css [client] (css) */
.chat-container {
  height: 100%;
  font-family: Arial, sans-serif;
  display: flex;
}

.chat-list {
  border-right: 1px solid #a0aec033;
  width: 100%;
  padding: 20px;
  transition: transform .3s;
  overflow-y: auto;
}

.chat-title {
  text-shadow: 0 0 5px #ffffff1a;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: bold;
}

.chat-item {
  cursor: pointer;
  border-radius: 10px;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  transition: all .3s;
  display: flex;
  box-shadow: 0 4px 6px #0000001a;
}

.chat-item:hover {
  background: #7c8797e6;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px #0003;
}

.avatar-container {
  position: relative;
}

.avatar {
  border: 2px solid #a0aec0;
  border-radius: 50%;
  transition: transform .3s;
}

.chat-item:hover .avatar {
  transform: scale(1.05);
}

.status-indicator {
  border: 2px solid #2d3748;
  border-radius: 50%;
  width: 15px;
  height: 15px;
  position: absolute;
  bottom: 0;
  right: 0;
  box-shadow: 0 0 5px #48bb78;
}

.chat-details {
  flex: 1;
  margin-left: 15px;
}

.chat-header {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.user-name {
  text-transform: capitalize;
  font-size: .875rem;
  font-weight: bold;
}

.timestamp {
  text-shadow: 0 0 2px #ffffff1a;
  font-size: .75rem;
}

.last-message {
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: .75rem;
  overflow: hidden;
}

.reaction {
  color: #f56565;
  font-size: .875rem;
  animation: 1.5s infinite pulse;
}

.chat-panel {
  width: 66.6667%;
  padding: 24px;
  transition: opacity .3s;
  display: none;
}

@media (width >= 1024px) {
  .chat-list {
    width: 33.3333%;
    display: block;
  }

  .chat-panel {
    width: 66.6667%;
    display: flex;
  }
}

@media (width <= 1023px) {
  .chat-container {
    margin: 0;
    padding: 0;
    position: relative;
  }

  .chat-list {
    width: 100%;
    margin: 0;
    padding: 0;
    display: block;
  }

  .chat-panel, .chat-list.chat-list-with-panel {
    display: none;
  }

  .chat-panel.chat-panel-active {
    background: inherit;
    z-index: 1000;
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    position: fixed;
    inset: 0;
  }

  .chat-input-form {
    background: inherit;
    z-index: 1001;
    border-top: 1px solid #0000001a;
    width: 100%;
    padding: 10px 15px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .chat-input-form .input-group {
    align-items: center;
    gap: 8px;
    max-width: 100%;
    display: flex;
  }

  .chat-input {
    flex: 1;
    border: 1px solid #0000001a !important;
    border-radius: 20px !important;
    padding: 10px 16px !important;
  }

  .chat-send-btn {
    border-radius: 50% !important;
    justify-content: center !important;
    align-items: center !important;
    width: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    display: flex !important;
  }

  .chat-messages {
    width: 100%;
    overflow-y: auto;
    height: calc(100vh - 140px) !important;
    padding: 0 15px !important;
  }

  .chat-header {
    background: inherit;
    z-index: 1001;
    border-bottom: 1px solid #0000001a;
    width: 100%;
    padding: 15px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .chat-window {
    width: 100% !important;
    height: 100vh !important;
    margin: 0 !important;
    padding: 80px 0 !important;
  }

  .chat-item {
    border-bottom: 1px solid #0000000d;
    border-radius: 0;
    margin: 0;
    padding: 12px 15px;
  }
}

.chat-window, .instructions {
  border-radius: 12px;
  width: 100%;
  height: 100%;
  padding: 20px;
  animation: .5s ease-in fadeIn;
  box-shadow: inset 0 0 10px #0000004d;
}

.chat-header {
  margin-bottom: 1rem;
  font-size: 1.125rem;
  font-weight: bold;
}

.chat-content, .instruction-text {
  margin-top: 1rem;
  line-height: 1.5;
}

.icon-container {
  margin-bottom: 20px;
}

.chat-icon {
  text-align: center;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  padding: 4px;
  font-size: 3rem;
  line-height: 80px;
  display: inline-block;
}

.instruction-title {
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: bold;
}

.instruction-text {
  color: #ccc;
  margin-bottom: 20px;
  font-size: 1rem;
}

.send-button {
  color: #fff;
  cursor: pointer;
  background-color: #6366f1;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 1rem;
  transition: background-color .3s;
}

.send-button:hover {
  background-color: #4f46e5;
}

.instructions {
  justify-content: center;
  align-items: center;
  display: flex;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}


/*# sourceMappingURL=pages_styles_0a6f1148._.css.map*/