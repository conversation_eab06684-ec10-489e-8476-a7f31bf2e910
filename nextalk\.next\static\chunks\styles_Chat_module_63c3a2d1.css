/* [project]/styles/Chat.module.css [client] (css) */
.Chat-module__vVjGxa__chatContainer {
  backdrop-filter: blur(10px);
  border-radius: 15px;
  height: 80vh;
  display: flex;
  overflow: hidden;
  box-shadow: 0 10px 30px #0000001a;
}

.Chat-module__vVjGxa__chatList {
  background: #ffffff0d;
  border-right: 1px solid #ffffff1a;
  width: 350px;
  overflow-y: auto;
}

.Chat-module__vVjGxa__chatTitle {
  text-align: center;
  border-bottom: 1px solid #ffffff1a;
  margin: 0;
  padding: 20px;
  font-size: 1.5rem;
  font-weight: 600;
}

.Chat-module__vVjGxa__chatItem {
  cursor: pointer;
  border-bottom: 1px solid #ffffff0d;
  align-items: center;
  padding: 15px 20px;
  transition: all .3s;
  display: flex;
}

.Chat-module__vVjGxa__chatItem:hover {
  background: #ffffff1a;
  transform: translateX(5px);
}

.Chat-module__vVjGxa__chatItem.Chat-module__vVjGxa__active {
  color: #fff;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
}

.Chat-module__vVjGxa__avatarContainer {
  margin-right: 15px;
  position: relative;
}

.Chat-module__vVjGxa__avatar {
  object-fit: cover;
  border: 2px solid #fff3;
  border-radius: 50%;
}

.Chat-module__vVjGxa__statusIndicator {
  background: #10b981;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  position: absolute;
  bottom: 2px;
  right: 2px;
}

.Chat-module__vVjGxa__chatDetails {
  flex: 1;
  min-width: 0;
}

.Chat-module__vVjGxa__chatHeader {
  justify-content: between;
  align-items: center;
  margin-bottom: 5px;
  display: flex;
}

.Chat-module__vVjGxa__userName {
  font-size: 1rem;
  font-weight: 600;
}

.Chat-module__vVjGxa__timestamp {
  opacity: .7;
  margin-left: auto;
  font-size: .75rem;
}

.Chat-module__vVjGxa__lastMessage {
  opacity: .8;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin: 0;
  font-size: .85rem;
  overflow: hidden;
}

.Chat-module__vVjGxa__chatPanel {
  background: #ffffff05;
  flex-direction: column;
  flex: 1;
  display: flex;
}

.Chat-module__vVjGxa__chatWindow {
  flex-direction: column;
  height: 100%;
  display: flex;
}

.Chat-module__vVjGxa__chatHeader {
  background: #ffffff0d;
  border-bottom: 1px solid #ffffff1a;
  padding: 20px;
}

.Chat-module__vVjGxa__chatMessages {
  flex: 1;
  max-height: 400px;
  padding: 20px;
  overflow-y: auto;
}

.Chat-module__vVjGxa__chatMessages::-webkit-scrollbar {
  width: 6px;
}

.Chat-module__vVjGxa__chatMessages::-webkit-scrollbar-track {
  background: #ffffff1a;
  border-radius: 3px;
}

.Chat-module__vVjGxa__chatMessages::-webkit-scrollbar-thumb {
  background: #ffffff4d;
  border-radius: 3px;
}

.Chat-module__vVjGxa__message {
  margin-bottom: 15px;
  display: flex;
}

.Chat-module__vVjGxa__messageYou {
  justify-content: flex-end;
}

.Chat-module__vVjGxa__messageFriend {
  justify-content: flex-start;
}

.Chat-module__vVjGxa__messageBubble {
  border-radius: 18px;
  flex-direction: column;
  max-width: 70%;
  padding: 12px 16px;
  animation: .3s ease-out Chat-module__vVjGxa__messageSlideIn;
  display: flex;
  box-shadow: 0 2px 8px #0000001a;
}

@keyframes Chat-module__vVjGxa__messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.Chat-module__vVjGxa__messageText {
  margin-bottom: 4px;
  line-height: 1.4;
}

.Chat-module__vVjGxa__messageTimestamp {
  opacity: .7;
  align-self: flex-end;
  font-size: .75rem;
}

.Chat-module__vVjGxa__chatInputForm {
  background: #ffffff0d;
  border-top: 1px solid #ffffff1a;
  gap: 10px;
  padding: 20px;
  display: flex;
}

.Chat-module__vVjGxa__chatInput {
  border: 1px solid #fff3;
  border-radius: 25px;
  outline: none;
  flex: 1;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all .3s;
}

.Chat-module__vVjGxa__chatInput:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.Chat-module__vVjGxa__chatSendBtn {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  transition: all .3s;
  display: flex;
}

.Chat-module__vVjGxa__chatSendBtn:hover {
  transform: scale(1.05);
  box-shadow: 0 5px 15px #3b82f666;
}

.Chat-module__vVjGxa__instructions {
  text-align: center;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
  display: flex;
}

.Chat-module__vVjGxa__iconContainer {
  margin-bottom: 20px;
}

.Chat-module__vVjGxa__chatIcon {
  opacity: .6;
  font-size: 4rem;
}

.Chat-module__vVjGxa__instructionTitle {
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: 600;
}

.Chat-module__vVjGxa__instructionText {
  opacity: .8;
  margin-bottom: 20px;
}

.Chat-module__vVjGxa__sendButton {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(45deg, #3b82f6, #60a5fa);
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all .3s;
}

.Chat-module__vVjGxa__sendButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px #3b82f666;
}

.Chat-module__vVjGxa__noMessages {
  text-align: center;
  opacity: .6;
  padding: 40px 20px;
}

.Chat-module__vVjGxa__noMessages i {
  margin-bottom: 15px;
  font-size: 3rem;
  display: block;
}

.Chat-module__vVjGxa__noMessages h4 {
  margin-bottom: 10px;
}

@media (width <= 768px) {
  .Chat-module__vVjGxa__chatContainer {
    flex-direction: column;
    height: 90vh;
  }

  .Chat-module__vVjGxa__chatList {
    width: 100%;
    max-height: 200px;
  }

  .Chat-module__vVjGxa__chatItem {
    padding: 10px 15px;
  }

  .Chat-module__vVjGxa__chatMessages {
    max-height: 300px;
  }

  .Chat-module__vVjGxa__messageBubble {
    max-width: 85%;
  }
}

@media (width <= 480px) {
  .Chat-module__vVjGxa__chatInputForm {
    padding: 15px;
  }

  .Chat-module__vVjGxa__chatInput {
    padding: 10px 14px;
    font-size: .9rem;
  }

  .Chat-module__vVjGxa__chatSendBtn {
    width: 45px;
    height: 45px;
  }
}


/*# sourceMappingURL=styles_Chat_module_63c3a2d1.css.map*/