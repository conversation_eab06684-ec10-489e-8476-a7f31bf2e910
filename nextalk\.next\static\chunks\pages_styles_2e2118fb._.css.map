{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Chats.css"], "sourcesContent": ["/* ../../styles/Chats.css */\r\n.chat-wrapper {\r\n    position: fixed; /* Fixes the entire chat component in place */\r\n    top: 10px; /* Margin from top */\r\n    left: 300px; /* Accounts for sidebar width on desktop */\r\n    right: 10px; /* Margin from right */\r\n    bottom: 10px; /* Margin from bottom */\r\n    display: flex;\r\n    flex-direction: column;\r\n    transition: all 0.4s ease;\r\n    border-radius: 15px;\r\n    overflow: hidden; /* Prevents wrapper from scrolling */\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n    z-index: 5; /* Ensures it stays above other content */\r\n}\r\n\r\n/* Chat Header */\r\n.chat-header {\r\n    padding: 15px 20px;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    background: rgba(0, 0, 0, 0.05);\r\n    flex-shrink: 0; /* Fixed height, no shrinking */\r\n}\r\n\r\n/* Chat Title */\r\n.chat-title {\r\n    margin: 0;\r\n    font-size: 1.5rem;\r\n    font-weight: 600;\r\n    color: #3b82f6;\r\n    letter-spacing: 1px;\r\n}\r\n\r\n/* Chat Messages */\r\n.chat-messages {\r\n    flex: 1; /* Takes remaining space between header and input */\r\n    padding: 20px;\r\n    overflow-y: auto; /* Messages scrollable */\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 15px;\r\n}\r\n\r\n/* Message Styling */\r\n.message {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    animation: slideIn 0.3s ease;\r\n}\r\n\r\n.message-you {\r\n    justify-content: flex-end;\r\n}\r\n\r\n.message-friend {\r\n    justify-content: flex-start;\r\n}\r\n\r\n.message-bubble {\r\n    max-width: 70%;\r\n    padding: 12px 18px;\r\n    border-radius: 20px;\r\n    position: relative;\r\n    transition: transform 0.2s ease;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.message-bubble:hover {\r\n    transform: scale(1.02);\r\n}\r\n\r\n.message-text {\r\n    display: block;\r\n    font-size: 1rem;\r\n    word-wrap: break-word;\r\n}\r\n\r\n.message-timestamp {\r\n    display: block;\r\n    font-size: 0.75rem;\r\n    opacity: 0.7;\r\n    margin-top: 5px;\r\n}\r\n\r\n/* Chat Input */\r\n.chat-input-form {\r\n    padding: 15px 20px;\r\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n    background: rgba(0, 0, 0, 0.05);\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n    flex-shrink: 0; /* Fixed height, no shrinking */\r\n}\r\n\r\n/* Input Field */\r\n.chat-input {\r\n    flex: 1;\r\n    padding: 12px 20px;\r\n    border: none;\r\n    border-radius: 30px;\r\n    font-size: 1rem;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-input:focus {\r\n    outline: none;\r\n    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* Send Button */\r\n.chat-send-btn {\r\n    padding: 12px 20px;\r\n    border-radius: 50%;\r\n    background: #3b82f6;\r\n    color: #ffffff;\r\n    border: none;\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\r\n}\r\n\r\n.chat-send-btn:hover {\r\n    background: #2563eb;\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.5);\r\n}\r\n\r\n/* Animation */\r\n@keyframes slideIn {\r\n    from {\r\n        opacity: 0;\r\n        transform: translateY(20px);\r\n    }\r\n    to {\r\n        opacity: 1;\r\n        transform: translateY(0);\r\n    }\r\n}\r\n\r\n/* Scrollbar Styling */\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 8px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: transparent;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(59, 130, 246, 0.5);\r\n    border-radius: 10px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: #3b82f6;\r\n}\r\n\r\n/* Mobile Adjustments */\r\n@media (max-width: 991px) {\r\n    .chat-wrapper {\r\n        left: 10px; /* No sidebar on mobile, full width */\r\n        top: 70px; /* Accounts for navbar height */\r\n    }\r\n}\r\n\r\n/* Messages Component Responsive Styles */\r\n.chat-list-with-panel {\r\n    width: 350px !important;\r\n}\r\n\r\n@media (max-width: 1024px) {\r\n    .chat-container {\r\n        flex-direction: column;\r\n        height: 90vh;\r\n    }\r\n\r\n    .chat-list {\r\n        width: 100% !important;\r\n        max-height: none;\r\n        border-right: none;\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n\r\n    .chat-panel {\r\n        width: 100% !important;\r\n        display: flex !important;\r\n    }\r\n\r\n    .chat-list-with-panel {\r\n        display: none; /* Hide chat list when chat is open on mobile */\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .chat-container {\r\n        height: 95vh;\r\n    }\r\n\r\n    .chat-item {\r\n        padding: 10px 15px;\r\n    }\r\n\r\n    .chat-messages {\r\n        max-height: 300px;\r\n    }\r\n\r\n    .message-bubble {\r\n        max-width: 85%;\r\n    }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n    .chat-input-form {\r\n        padding: 15px;\r\n    }\r\n\r\n    .chat-input {\r\n        padding: 10px 14px;\r\n        font-size: 0.9rem;\r\n    }\r\n\r\n    .chat-send-btn {\r\n        width: 45px;\r\n        height: 45px;\r\n    }\r\n\r\n    .chat-title {\r\n        font-size: 1.2rem;\r\n    }\r\n\r\n    .user-name {\r\n        font-size: 0.8rem;\r\n    }\r\n\r\n    .last-message {\r\n        font-size: 0.7rem;\r\n    }\r\n}\r\n\r\n/* Typing Indicator Animation */\r\n.typing-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n    padding: 8px 0;\r\n}\r\n\r\n.typing-indicator span {\r\n    width: 8px;\r\n    height: 8px;\r\n    border-radius: 50%;\r\n    background-color: #6b7280;\r\n    animation: typing 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-indicator span:nth-child(1) {\r\n    animation-delay: -0.32s;\r\n}\r\n\r\n.typing-indicator span:nth-child(2) {\r\n    animation-delay: -0.16s;\r\n}\r\n\r\n@keyframes typing {\r\n    0%, 80%, 100% {\r\n        transform: scale(0.8);\r\n        opacity: 0.5;\r\n    }\r\n    40% {\r\n        transform: scale(1);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* Status Indicator Styles */\r\n.status-indicator {\r\n    position: absolute;\r\n    bottom: 2px;\r\n    right: 2px;\r\n    width: 12px;\r\n    height: 12px;\r\n    border-radius: 50%;\r\n    border: 2px solid white;\r\n    transition: background-color 0.3s ease;\r\n}\r\n\r\n/* Chat Messages Scrollbar */\r\n.chat-messages::-webkit-scrollbar {\r\n    width: 6px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-track {\r\n    background: rgba(0, 0, 0, 0.1);\r\n    border-radius: 3px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb {\r\n    background: rgba(0, 0, 0, 0.3);\r\n    border-radius: 3px;\r\n}\r\n\r\n.chat-messages::-webkit-scrollbar-thumb:hover {\r\n    background: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n/* Fixed Chat Input Position */\r\n.chat-input-form {\r\n    position: sticky;\r\n    bottom: 0;\r\n    background: inherit;\r\n    padding: 15px 20px;\r\n    border-top: 1px solid rgba(0, 0, 0, 0.1);\r\n    z-index: 10;\r\n}\r\n\r\n.chat-input-form .input-group {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n}\r\n\r\n.chat-input {\r\n    flex: 1;\r\n    border: 1px solid rgba(0, 0, 0, 0.1);\r\n    border-radius: 25px;\r\n    padding: 12px 20px;\r\n    outline: none;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.chat-input:focus {\r\n    border-color: #3b82f6;\r\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.chat-send-btn {\r\n    width: 45px;\r\n    height: 45px;\r\n    border-radius: 50%;\r\n    border: none;\r\n    background: #3b82f6;\r\n    color: white;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.chat-send-btn:hover {\r\n    background: #2563eb;\r\n    transform: scale(1.05);\r\n}\r\n\r\n.chat-send-btn:active {\r\n    transform: scale(0.95);\r\n}\r\n\r\n/* Message Footer and Delivery Status */\r\n.message-footer {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-top: 4px;\r\n    gap: 8px;\r\n}\r\n\r\n.message-timestamp {\r\n    font-size: 0.75rem;\r\n    opacity: 0.7;\r\n}\r\n\r\n.delivery-status {\r\n    font-size: 0.8rem;\r\n    opacity: 0.8;\r\n}\r\n\r\n.delivery-status i {\r\n    font-size: 12px;\r\n}\r\n\r\n/* Message Bubble Enhancements */\r\n.message-bubble {\r\n    padding: 8px 12px;\r\n    border-radius: 18px;\r\n    max-width: 70%;\r\n    word-wrap: break-word;\r\n    position: relative;\r\n}\r\n\r\n.message-you .message-bubble {\r\n    background: #3b82f6;\r\n    color: white;\r\n    margin-left: auto;\r\n}\r\n\r\n.message-friend .message-bubble {\r\n    background: rgba(0, 0, 0, 0.05);\r\n    margin-right: auto;\r\n}\r\n\r\n.message-text {\r\n    display: block;\r\n    line-height: 1.4;\r\n}\r\n\r\n/* Typing Indicator in User List */\r\n.typing-text {\r\n    color: #10b981;\r\n    font-style: italic;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n}\r\n\r\n.typing-text .typing-indicator {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 2px;\r\n}\r\n\r\n.typing-text .typing-indicator span {\r\n    width: 4px;\r\n    height: 4px;\r\n    border-radius: 50%;\r\n    background-color: #10b981;\r\n    animation: typing 1.4s infinite ease-in-out;\r\n}\r\n\r\n.typing-text .typing-indicator span:nth-child(1) {\r\n    animation-delay: -0.32s;\r\n}\r\n\r\n.typing-text .typing-indicator span:nth-child(2) {\r\n    animation-delay: -0.16s;\r\n}\r\n\r\n/* Enhanced Status Indicator */\r\n.status-indicator {\r\n    position: absolute;\r\n    bottom: 2px;\r\n    right: 2px;\r\n    width: 12px;\r\n    height: 12px;\r\n    border-radius: 50%;\r\n    border: 2px solid white;\r\n    transition: background-color 0.3s ease;\r\n    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.status-indicator.online {\r\n    background-color: #10b981;\r\n    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);\r\n}\r\n\r\n.status-indicator.offline {\r\n    background-color: #ef4444;\r\n    box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);\r\n}\r\n\r\n/* Unread Message Badge */\r\n.unread-badge {\r\n    position: absolute;\r\n    top: 8px;\r\n    right: 8px;\r\n    background: #ff3b30;\r\n    color: white;\r\n    border-radius: 12px;\r\n    padding: 2px 8px;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    min-width: 20px;\r\n    height: 20px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    box-shadow: 0 2px 4px rgba(255, 59, 48, 0.3);\r\n    animation: unreadPulse 2s infinite;\r\n}\r\n\r\n@keyframes unreadPulse {\r\n    0%, 100% {\r\n        transform: scale(1);\r\n        opacity: 1;\r\n    }\r\n    50% {\r\n        transform: scale(1.1);\r\n        opacity: 0.8;\r\n    }\r\n}\r\n\r\n/* Chat Item Positioning */\r\n.chat-item {\r\n    position: relative;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 12px;\r\n    border-radius: 12px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n}\r\n\r\n.chat-item:hover {\r\n    background-color: rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.chat-item.selected {\r\n    background-color: rgba(59, 130, 246, 0.1);\r\n    border-left: 4px solid #3b82f6;\r\n}\r\n\r\n/* Enhanced Status Indicator */\r\n.status-indicator {\r\n    position: absolute;\r\n    bottom: 2px;\r\n    right: 2px;\r\n    width: 12px;\r\n    height: 12px;\r\n    border-radius: 50%;\r\n    border: 2px solid white;\r\n    transition: all 0.3s ease;\r\n    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);\r\n    z-index: 2;\r\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;AAgBA;;;;;;;AAQA;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;;;;AAQA;;;;;;;;;;AAWA;;;;;;;;;;AAUA;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;;;;;;;AAgBA;;;;AAcA;EACI;;;;;;AAOJ;;;;AAIA;EACI;;;;;EAKA;;;;;;;EAOA;;;;;EAKA;;;;;AAKJ;EACI;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAMJ;;;;;;;AAOA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;AAKA;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;AAKA;;;;;AAMA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;AAKA;;;;;;;;;;;;AAYA;;;;;AAKA;;;;;AAMA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;;AAYA;;;;;;;;;;AAUA;;;;AAIA;;;;;AAMA", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/styles/Messages.css"], "sourcesContent": [".chat-container {\r\n  display: flex;\r\n  height: 100%;\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.chat-list {\r\n  width: 100%;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n  border-right: 1px solid rgba(160, 174, 192, 0.2);\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.chat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 12px;\r\n  margin-bottom: 12px;\r\n  border-radius: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-item:hover {\r\n  background: rgba(124, 135, 151, 0.9);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.avatar-container {\r\n  position: relative;\r\n}\r\n\r\n.avatar {\r\n  border-radius: 50%;\r\n  border: 2px solid #a0aec0;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.chat-item:hover .avatar {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: 15px;\r\n  height: 15px;\r\n  border-radius: 50%;\r\n  border: 2px solid #2d3748;\r\n  box-shadow: 0 0 5px #48bb78;\r\n}\r\n\r\n.chat-details {\r\n  margin-left: 15px;\r\n  flex: 1;\r\n}\r\n\r\n.chat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.user-name {\r\n  font-weight: bold;\r\n  font-size: 0.875rem;\r\n  text-transform: capitalize;\r\n}\r\n\r\n.timestamp {\r\n  font-size: 0.75rem;\r\n  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.last-message {\r\n  font-size: 0.75rem;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.reaction {\r\n  color: #f56565;\r\n  animation: pulse 1.5s infinite;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.chat-panel {\r\n  display: none;\r\n  width: 66.666667%;\r\n  padding: 24px;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n/* Desktop - Show both list and panel */\r\n@media (min-width: 1024px) {\r\n  .chat-list {\r\n    width: 33.333333%;\r\n    display: block;\r\n  }\r\n  .chat-panel {\r\n    display: flex;\r\n    width: 66.666667%;\r\n  }\r\n}\r\n\r\n/* Mobile - Bootstrap Modal Approach */\r\n@media (max-width: 1023px) {\r\n  /* Main container stays normal on mobile */\r\n  .chat-container {\r\n    position: relative;\r\n  }\r\n\r\n  /* Chat list takes full width on mobile */\r\n  .chat-list {\r\n    width: 100% !important;\r\n    display: block;\r\n  }\r\n\r\n  /* Hide desktop chat panel on mobile - use modal instead */\r\n  .chat-panel {\r\n    display: none !important;\r\n  }\r\n}\r\n\r\n/* Bootstrap Modal Styling for Mobile Chat */\r\n.modal-fullscreen .modal-content {\r\n  border-radius: 0;\r\n  border: none;\r\n}\r\n\r\n.modal-fullscreen .modal-header {\r\n  padding: 1rem;\r\n  min-height: 60px;\r\n}\r\n\r\n.modal-fullscreen .modal-body {\r\n  padding: 0;\r\n}\r\n\r\n/* Mobile Chat Message Styling in Modal */\r\n.modal .message {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.modal .message .rounded-3 {\r\n  border-radius: 1rem !important;\r\n  max-width: 70%;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.modal .bg-primary {\r\n  background-color: #3b82f6 !important;\r\n}\r\n\r\n.modal .bg-light {\r\n  background-color: rgba(0, 0, 0, 0.05) !important;\r\n}\r\n\r\n.chat-window, .instructions {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 20px;\r\n  border-radius: 12px;\r\n  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);\r\n  animation: fadeIn 0.5s ease-in;\r\n}\r\n\r\n.chat-header {\r\n  font-size: 1.125rem;\r\n  font-weight: bold;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.chat-content, .instruction-text {\r\n  margin-top: 1rem;\r\n  line-height: 1.5;\r\n}\r\n\r\n.icon-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.chat-icon {\r\n  font-size: 3rem;\r\n  display: inline-block;\r\n  padding: 4px;\r\n  width: 80px;\r\n  height: 80px;\r\n  line-height: 80px;\r\n  border: 2px solid #fff;\r\n  border-radius: 50%;\r\n  text-align: center;\r\n}\r\n\r\n.instruction-title {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.instruction-text {\r\n  font-size: 1rem;\r\n  color: #ccc;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.send-button {\r\n  background-color: #6366f1;\r\n  color: #fff;\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 1rem;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.send-button:hover {\r\n  background-color: #4f46e5;\r\n}\r\n\r\n.instructions{\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n@keyframes pulse {\r\n  0% { transform: scale(1); }\r\n  50% { transform: scale(1.2); }\r\n  100% { transform: scale(1); }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;;;AAQA;EACE;;;;;EAIA;;;;;;AAOF;EAEE;;;;EAKA;;;;;EAMA;;;;;AAMF;;;;;AAKA;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;;;AAMA", "debugId": null}}]}