.chat-container {
  display: flex;
  height: 100%;
  font-family: 'Arial', sans-serif;
}

.chat-list {
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid rgba(160, 174, 192, 0.2);
  transition: transform 0.3s ease;
}

.chat-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chat-item:hover {
  background: rgba(124, 135, 151, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.avatar-container {
  position: relative;
}

.avatar {
  border-radius: 50%;
  border: 2px solid #a0aec0;
  transition: transform 0.3s ease;
}

.chat-item:hover .avatar {
  transform: scale(1.05);
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 2px solid #2d3748;
  box-shadow: 0 0 5px #48bb78;
}

.chat-details {
  margin-left: 15px;
  flex: 1;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name {
  font-weight: bold;
  font-size: 0.875rem;
  text-transform: capitalize;
}

.timestamp {
  font-size: 0.75rem;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);
}

.last-message {
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reaction {
  color: #f56565;
  animation: pulse 1.5s infinite;
  font-size: 0.875rem;
}

.chat-panel {
  display: none;
  width: 66.666667%;
  padding: 24px;
  transition: opacity 0.3s ease;
}

/* Desktop - Show both list and panel */
@media (min-width: 1024px) {
  .chat-list {
    width: 33.333333%;
    display: block;
  }
  .chat-panel {
    display: flex;
    width: 66.666667%;
  }
}

/* Mobile - Responsive behavior */
@media (max-width: 1023px) {
  .chat-container {
    position: relative;
    padding: 0; /* Remove padding for full width */
    margin: 0; /* Remove margin for full width */
  }

  /* When no chat is selected, show only chat list */
  .chat-list {
    width: 100%;
    display: block;
    padding: 0; /* Remove padding for full width */
    margin: 0; /* Remove margin for full width */
  }

  .chat-panel {
    display: none;
  }

  /* When chat is selected, hide list and show panel */
  .chat-list.chat-list-with-panel {
    display: none;
  }

  .chat-panel.chat-panel-active {
    display: flex;
    width: 100vw; /* Full viewport width */
    height: 100vh; /* Full viewport height */
    position: fixed; /* Fixed positioning for full screen */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    z-index: 1000; /* Higher z-index */
    padding: 0; /* No padding */
    margin: 0; /* No margin */
  }

  /* Mobile chat input styling - Full width */
  .chat-input-form {
    padding: 10px 15px; /* Minimal padding */
    background: inherit;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 1001;
  }

  .chat-input-form .input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 100%;
  }

  .chat-input {
    flex: 1;
    border-radius: 20px !important;
    padding: 10px 16px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
  }

  .chat-send-btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Mobile chat messages - Full width */
  .chat-messages {
    padding: 0 15px !important; /* Minimal side padding */
    height: calc(100vh - 140px) !important; /* Account for header and input */
    width: 100%;
    overflow-y: auto;
  }

  /* Mobile chat header - Full width */
  .chat-header {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: inherit;
    z-index: 1001;
  }

  /* Mobile chat window - Full width */
  .chat-window {
    width: 100% !important;
    height: 100vh !important;
    padding: 0 !important;
    margin: 0 !important;
    padding-top: 80px !important; /* Space for fixed header */
    padding-bottom: 80px !important; /* Space for fixed input */
  }

  /* Mobile user list items */
  .chat-item {
    padding: 12px 15px;
    margin: 0;
    border-radius: 0; /* Remove border radius for full width feel */
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.chat-window, .instructions {
  width: 100%;
  height: 100%;
  padding: 20px;
  border-radius: 12px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease-in;
}

.chat-header {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.chat-content, .instruction-text {
  margin-top: 1rem;
  line-height: 1.5;
}

.icon-container {
  margin-bottom: 20px;
}

.chat-icon {
  font-size: 3rem;
  display: inline-block;
  padding: 4px;
  width: 80px;
  height: 80px;
  line-height: 80px;
  border: 2px solid #fff;
  border-radius: 50%;
  text-align: center;
}

.instruction-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.instruction-text {
  font-size: 1rem;
  color: #ccc;
  margin-bottom: 20px;
}

.send-button {
  background-color: #6366f1;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.send-button:hover {
  background-color: #4f46e5;
}

.instructions{
  display: flex;
  justify-content: center;
  align-items: center;
}
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}