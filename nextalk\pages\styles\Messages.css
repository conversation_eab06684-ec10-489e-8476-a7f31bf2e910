.chat-container {
  display: flex;
  height: 100%;
  font-family: 'Arial', sans-serif;
}

.chat-list {
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid rgba(160, 174, 192, 0.2);
  transition: transform 0.3s ease;
}

.chat-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chat-item:hover {
  background: rgba(124, 135, 151, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.avatar-container {
  position: relative;
}

.avatar {
  border-radius: 50%;
  border: 2px solid #a0aec0;
  transition: transform 0.3s ease;
}

.chat-item:hover .avatar {
  transform: scale(1.05);
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 2px solid #2d3748;
  box-shadow: 0 0 5px #48bb78;
}

.chat-details {
  margin-left: 15px;
  flex: 1;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name {
  font-weight: bold;
  font-size: 0.875rem;
  text-transform: capitalize;
}

.timestamp {
  font-size: 0.75rem;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);
}

.last-message {
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reaction {
  color: #f56565;
  animation: pulse 1.5s infinite;
  font-size: 0.875rem;
}

.chat-panel {
  display: none;
  width: 66.666667%;
  padding: 24px;
  transition: opacity 0.3s ease;
}

/* Desktop - Show both list and panel */
@media (min-width: 1024px) {
  .chat-list {
    width: 33.333333%;
    display: block;
  }
  .chat-panel {
    display: flex;
    width: 66.666667%;
  }
}

/* Mobile - Bootstrap Modal Approach */
@media (max-width: 1023px) {
  /* Main container stays normal on mobile */
  .chat-container {
    position: relative;
  }

  /* Chat list takes full width on mobile */
  .chat-list {
    width: 100% !important;
    display: block;
  }

  /* Hide desktop chat panel on mobile - use modal instead */
  .chat-panel {
    display: none !important;
  }
}

/* Bootstrap Modal Styling for Mobile Chat */
.modal-fullscreen .modal-content {
  border-radius: 0;
  border: none;
}

.modal-fullscreen .modal-header {
  padding: 1rem;
  min-height: 60px;
}

.modal-fullscreen .modal-body {
  padding: 0;
}

/* Mobile Chat Message Styling in Modal */
.modal .message {
  margin-bottom: 1rem;
}

.modal .rounded-3 {
  border-radius: 1rem !important;
  max-width: 70%;
  word-wrap: break-word;
}

.modal .bg-primary {
  background-color: #3b82f6 !important;
}

.modal .bg-light {
  background-color: rgba(255, 255, 255, 0.9) !important;
  color: #333 !important;
}

/* Message alignment in modal */
.modal .justify-content-end {
  justify-content: flex-end !important;
}

.modal .justify-content-start {
  justify-content: flex-start !important;
}

/* Input section styling */
.modal .border-top {
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.modal .form-control {
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  border-radius: 25px !important;
}

.modal .btn-primary {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

.chat-window, .instructions {
  width: 100%;
  height: 100%;
  padding: 20px;
  border-radius: 12px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease-in;
}

.chat-header {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.chat-content, .instruction-text {
  margin-top: 1rem;
  line-height: 1.5;
}

.icon-container {
  margin-bottom: 20px;
}

.chat-icon {
  font-size: 3rem;
  display: inline-block;
  padding: 4px;
  width: 80px;
  height: 80px;
  line-height: 80px;
  border: 2px solid #fff;
  border-radius: 50%;
  text-align: center;
}

.instruction-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 15px;
  color: inherit;
}

.instruction-text {
  font-size: 1rem;
  opacity: 0.7;
  margin-bottom: 25px;
  color: inherit;
}

.instruction-content {
  padding: 40px 20px;
}

.instructions {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.send-button {
  background-color: #6366f1;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.send-button:hover {
  background-color: #4f46e5;
}

.instructions{
  display: flex;
  justify-content: center;
  align-items: center;
}
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Instagram-Style Typing Indicator Animation */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 3px;
}

.typing-indicator span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #667eea;
    animation: instagramTyping 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes instagramTyping {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.4;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Instagram-Style Message Bubbles */
.message-bubble {
    position: relative;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Instagram-Style Modal */
.modal-fullscreen .modal-content {
    border-radius: 0;
    border: none;
    overflow: hidden;
}

.modal-fullscreen .modal-header {
    border: none;
}

.modal-fullscreen .modal-body {
    padding: 0;
}

/* Smooth Scrolling */
.modal-body .overflow-auto {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.modal-body .overflow-auto::-webkit-scrollbar {
    width: 4px;
}

.modal-body .overflow-auto::-webkit-scrollbar-track {
    background: transparent;
}

.modal-body .overflow-auto::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

/* Input Focus Effects */
.form-control:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
    border-color: transparent !important;
}

/* Mobile Chat Modal Styles */
.mobile-chat-modal .modal-dialog {
    margin: 0;
    max-width: 100%;
    height: 100vh;
}

.mobile-chat-modal .modal-content {
    height: 100vh;
    border-radius: 0;
    border: none;
}

.mobile-chat-modal .modal-header {
    border-bottom: 1px solid #e0e0e0;
    padding: 12px 16px;
    background: #fff;
}

.mobile-chat-modal .modal-body {
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    flex-direction: column;
    height: calc(100vh - 120px);
    background: #f8f9fa;
}

/* Remove any default Bootstrap gaps */
.mobile-chat-modal .container-fluid,
.mobile-chat-modal .row,
.mobile-chat-modal .col {
    padding: 0 !important;
    margin: 0 !important;
}

/* Ensure full width for chat input */
.mobile-chat-modal .form-control {
    width: 100%;
    box-sizing: border-box;
}

/* Typing Indicator Animation */
.typing-indicator {
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.typing-indicator span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #28a745;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* Scroll to Bottom Button Animation */
.scroll-to-bottom {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile message alignment */
@media (max-width: 768px) {
    .mobile-chat-modal .d-flex {
        width: 100% !important;
    }

    .mobile-chat-modal .justify-content-end {
        padding-right: 0 !important;
    }

    .mobile-chat-modal .justify-content-start {
        padding-left: 0 !important;
    }

    .chat-container {
        padding: 0;
    }

    .user-list {
        border-right: none;
    }

    .chat-area {
        border-radius: 0;
    }

    .message-input {
        padding: 8px;
    }
}