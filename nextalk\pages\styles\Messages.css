.chat-container {
  display: flex;
  height: 100%;
  font-family: 'Arial', sans-serif;
}

.chat-list {
  width: 100%;
  padding: 20px;
  overflow-y: auto;
  border-right: 1px solid rgba(160, 174, 192, 0.2);
  transition: transform 0.3s ease;
}

.chat-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chat-item:hover {
  background: rgba(124, 135, 151, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.avatar-container {
  position: relative;
}

.avatar {
  border-radius: 50%;
  border: 2px solid #a0aec0;
  transition: transform 0.3s ease;
}

.chat-item:hover .avatar {
  transform: scale(1.05);
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 2px solid #2d3748;
  box-shadow: 0 0 5px #48bb78;
}

.chat-details {
  margin-left: 15px;
  flex: 1;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name {
  font-weight: bold;
  font-size: 0.875rem;
  text-transform: capitalize;
}

.timestamp {
  font-size: 0.75rem;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.1);
}

.last-message {
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reaction {
  color: #f56565;
  animation: pulse 1.5s infinite;
  font-size: 0.875rem;
}

.chat-panel {
  display: none;
  width: 66.666667%;
  padding: 24px;
  transition: opacity 0.3s ease;
}

/* Desktop - Show both list and panel */
@media (min-width: 1024px) {
  .chat-list {
    width: 33.333333%;
    display: block;
  }
  .chat-panel {
    display: flex;
    width: 66.666667%;
  }
}

/* Mobile - Responsive behavior */
@media (max-width: 1023px) {
  .chat-container {
    position: relative;
    padding: 6px;
  }

  /* When no chat is selected, show only chat list */
  .chat-list {
    width: 100%;
    display: block;
    padding: 6px;
  }

  .chat-panel {
    display: none;
  }

  /* When chat is selected, hide list and show panel */
  .chat-list.chat-list-with-panel {
    display: none;
  }

  .chat-panel.chat-panel-active {
    display: flex;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    z-index: 10;
    padding: 6px;
  }

  /* Mobile chat input styling */
  .chat-input-form {
    padding: 10px 6px;
    background: inherit;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }

  .chat-input-form .input-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .chat-input {
    flex: 1;
    border-radius: 20px !important;
    padding: 10px 16px !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
  }

  .chat-send-btn {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Mobile chat messages */
  .chat-messages {
    padding: 6px 10px !important;
    height: calc(100vh - 200px) !important;
  }

  /* Mobile chat header */
  .chat-header {
    padding: 10px 6px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Mobile user list items */
  .chat-item {
    padding: 12px 6px;
    margin: 4px 0;
    border-radius: 12px;
  }
}

.chat-window, .instructions {
  width: 100%;
  height: 100%;
  padding: 20px;
  border-radius: 12px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease-in;
}

.chat-header {
  font-size: 1.125rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.chat-content, .instruction-text {
  margin-top: 1rem;
  line-height: 1.5;
}

.icon-container {
  margin-bottom: 20px;
}

.chat-icon {
  font-size: 3rem;
  display: inline-block;
  padding: 4px;
  width: 80px;
  height: 80px;
  line-height: 80px;
  border: 2px solid #fff;
  border-radius: 50%;
  text-align: center;
}

.instruction-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.instruction-text {
  font-size: 1rem;
  color: #ccc;
  margin-bottom: 20px;
}

.send-button {
  background-color: #6366f1;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.send-button:hover {
  background-color: #4f46e5;
}

.instructions{
  display: flex;
  justify-content: center;
  align-items: center;
}
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}