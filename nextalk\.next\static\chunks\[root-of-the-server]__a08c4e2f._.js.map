{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/browser/dev/hmr-client/hmr-client.ts"], "sourcesContent": ["/// <reference path=\"../../../shared/runtime-types.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-globals.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-protocol.d.ts\" />\r\n/// <reference path=\"../../runtime/base/dev-extensions.ts\" />\r\n\r\ntype SendMessage = (msg: any) => void;\r\nexport type WebSocketMessage =\r\n  | {\r\n      type: \"turbopack-connected\";\r\n    }\r\n  | {\r\n      type: \"turbopack-message\";\r\n      data: Record<string, any>;\r\n    };\r\n\r\n\r\nexport type ClientOptions = {\r\n  addMessageListener: (cb: (msg: WebSocketMessage) => void) => void;\r\n  sendMessage: SendMessage;\r\n  onUpdateError: (err: unknown) => void;\r\n};\r\n\r\nexport function connect({\r\n  addMessageListener,\r\n  sendMessage,\r\n  onUpdateError = console.error,\r\n}: ClientOptions) {\r\n  addMessageListener((msg) => {\r\n    switch (msg.type) {\r\n      case \"turbopack-connected\":\r\n        handleSocketConnected(sendMessage);\r\n        break;\r\n      default:\r\n        try {\r\n          if (Array.isArray(msg.data)) {\r\n            for (let i = 0; i < msg.data.length; i++) {\r\n              handleSocketMessage(msg.data[i] as ServerMessage);\r\n            }\r\n          } else {\r\n            handleSocketMessage(msg.data as ServerMessage);\r\n          }\r\n          applyAggregatedUpdates();\r\n        } catch (e: unknown) {\r\n          console.warn(\r\n            \"[Fast Refresh] performing full reload\\n\\n\" +\r\n              \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\r\n              \"You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n\" +\r\n              \"Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n\" +\r\n              \"It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n\" +\r\n              \"Fast Refresh requires at least one parent function component in your React tree.\"\r\n          );\r\n          onUpdateError(e);\r\n          location.reload();\r\n        }\r\n        break;\r\n    }\r\n  });\r\n\r\n  const queued = globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS;\r\n  if (queued != null && !Array.isArray(queued)) {\r\n    throw new Error(\"A separate HMR handler was already registered\");\r\n  }\r\n  globalThis.TURBOPACK_CHUNK_UPDATE_LISTENERS = {\r\n    push: ([chunkPath, callback]: [ChunkListPath, UpdateCallback]) => {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    },\r\n  };\r\n\r\n  if (Array.isArray(queued)) {\r\n    for (const [chunkPath, callback] of queued) {\r\n      subscribeToChunkUpdate(chunkPath, sendMessage, callback);\r\n    }\r\n  }\r\n}\r\n\r\ntype UpdateCallbackSet = {\r\n  callbacks: Set<UpdateCallback>;\r\n  unsubscribe: () => void;\r\n};\r\n\r\nconst updateCallbackSets: Map<ResourceKey, UpdateCallbackSet> = new Map();\r\n\r\nfunction sendJSON(sendMessage: SendMessage, message: ClientMessage) {\r\n  sendMessage(JSON.stringify(message));\r\n}\r\n\r\ntype ResourceKey = string;\r\n\r\nfunction resourceKey(resource: ResourceIdentifier): ResourceKey {\r\n  return JSON.stringify({\r\n    path: resource.path,\r\n    headers: resource.headers || null,\r\n  });\r\n}\r\n\r\nfunction subscribeToUpdates(\r\n  sendMessage: SendMessage,\r\n  resource: ResourceIdentifier\r\n): () => void {\r\n  sendJSON(sendMessage, {\r\n    type: \"turbopack-subscribe\",\r\n    ...resource,\r\n  });\r\n\r\n  return () => {\r\n    sendJSON(sendMessage, {\r\n      type: \"turbopack-unsubscribe\",\r\n      ...resource,\r\n    });\r\n  };\r\n}\r\n\r\nfunction handleSocketConnected(sendMessage: SendMessage) {\r\n  for (const key of updateCallbackSets.keys()) {\r\n    subscribeToUpdates(sendMessage, JSON.parse(key));\r\n  }\r\n}\r\n\r\n// we aggregate all pending updates until the issues are resolved\r\nconst chunkListsWithPendingUpdates: Map<ResourceKey, PartialServerMessage> =\r\n  new Map();\r\n\r\nfunction aggregateUpdates(msg: PartialServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  let aggregated = chunkListsWithPendingUpdates.get(key);\r\n\r\n  if (aggregated) {\r\n    aggregated.instruction = mergeChunkListUpdates(\r\n      aggregated.instruction,\r\n      msg.instruction\r\n    );\r\n  } else {\r\n    chunkListsWithPendingUpdates.set(key, msg);\r\n  }\r\n}\r\n\r\nfunction applyAggregatedUpdates() {\r\n  if (chunkListsWithPendingUpdates.size === 0) return;\r\n  hooks.beforeRefresh();\r\n  for (const msg of chunkListsWithPendingUpdates.values()) {\r\n    triggerUpdate(msg);\r\n  }\r\n  chunkListsWithPendingUpdates.clear();\r\n  finalizeUpdate();\r\n}\r\n\r\nfunction mergeChunkListUpdates(\r\n  updateA: ChunkListUpdate,\r\n  updateB: ChunkListUpdate\r\n): ChunkListUpdate {\r\n  let chunks;\r\n  if (updateA.chunks != null) {\r\n    if (updateB.chunks == null) {\r\n      chunks = updateA.chunks;\r\n    } else {\r\n      chunks = mergeChunkListChunks(updateA.chunks, updateB.chunks);\r\n    }\r\n  } else if (updateB.chunks != null) {\r\n    chunks = updateB.chunks;\r\n  }\r\n\r\n  let merged;\r\n  if (updateA.merged != null) {\r\n    if (updateB.merged == null) {\r\n      merged = updateA.merged;\r\n    } else {\r\n      // Since `merged` is an array of updates, we need to merge them all into\r\n      // one, consistent update.\r\n      // Since there can only be `EcmascriptMergeUpdates` in the array, there is\r\n      // no need to key on the `type` field.\r\n      let update = updateA.merged[0];\r\n      for (let i = 1; i < updateA.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateA.merged[i]\r\n        );\r\n      }\r\n\r\n      for (let i = 0; i < updateB.merged.length; i++) {\r\n        update = mergeChunkListEcmascriptMergedUpdates(\r\n          update,\r\n          updateB.merged[i]\r\n        );\r\n      }\r\n\r\n      merged = [update];\r\n    }\r\n  } else if (updateB.merged != null) {\r\n    merged = updateB.merged;\r\n  }\r\n\r\n  return {\r\n    type: \"ChunkListUpdate\",\r\n    chunks,\r\n    merged,\r\n  };\r\n}\r\n\r\nfunction mergeChunkListChunks(\r\n  chunksA: Record<ChunkPath, ChunkUpdate>,\r\n  chunksB: Record<ChunkPath, ChunkUpdate>\r\n): Record<ChunkPath, ChunkUpdate> {\r\n  const chunks: Record<ChunkPath, ChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeChunkUpdates(chunkUpdateA, chunkUpdateB);\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, ChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeChunkUpdates(\r\n  updateA: ChunkUpdate,\r\n  updateB: ChunkUpdate\r\n): ChunkUpdate | undefined {\r\n  if (\r\n    (updateA.type === \"added\" && updateB.type === \"deleted\") ||\r\n    (updateA.type === \"deleted\" && updateB.type === \"added\")\r\n  ) {\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"partial\") {\r\n    invariant(updateA.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  if (updateB.type === \"partial\") {\r\n    invariant(updateB.instruction, \"Partial updates are unsupported\");\r\n  }\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction mergeChunkListEcmascriptMergedUpdates(\r\n  mergedA: EcmascriptMergedUpdate,\r\n  mergedB: EcmascriptMergedUpdate\r\n): EcmascriptMergedUpdate {\r\n  const entries = mergeEcmascriptChunkEntries(mergedA.entries, mergedB.entries);\r\n  const chunks = mergeEcmascriptChunksUpdates(mergedA.chunks, mergedB.chunks);\r\n\r\n  return {\r\n    type: \"EcmascriptMergedUpdate\",\r\n    entries,\r\n    chunks,\r\n  };\r\n}\r\n\r\nfunction mergeEcmascriptChunkEntries(\r\n  entriesA: Record<ModuleId, EcmascriptModuleEntry> | undefined,\r\n  entriesB: Record<ModuleId, EcmascriptModuleEntry> | undefined\r\n): Record<ModuleId, EcmascriptModuleEntry> {\r\n  return { ...entriesA, ...entriesB };\r\n}\r\n\r\nfunction mergeEcmascriptChunksUpdates(\r\n  chunksA: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined,\r\n  chunksB: Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined\r\n): Record<ChunkPath, EcmascriptMergedChunkUpdate> | undefined {\r\n  if (chunksA == null) {\r\n    return chunksB;\r\n  }\r\n\r\n  if (chunksB == null) {\r\n    return chunksA;\r\n  }\r\n\r\n  const chunks: Record<ChunkPath, EcmascriptMergedChunkUpdate> = {};\r\n\r\n  for (const [chunkPath, chunkUpdateA] of Object.entries(chunksA) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    const chunkUpdateB = chunksB[chunkPath];\r\n    if (chunkUpdateB != null) {\r\n      const mergedUpdate = mergeEcmascriptChunkUpdates(\r\n        chunkUpdateA,\r\n        chunkUpdateB\r\n      );\r\n      if (mergedUpdate != null) {\r\n        chunks[chunkPath] = mergedUpdate;\r\n      }\r\n    } else {\r\n      chunks[chunkPath] = chunkUpdateA;\r\n    }\r\n  }\r\n\r\n  for (const [chunkPath, chunkUpdateB] of Object.entries(chunksB) as Array<[ChunkPath, EcmascriptMergedChunkUpdate]>) {\r\n    if (chunks[chunkPath] == null) {\r\n      chunks[chunkPath] = chunkUpdateB;\r\n    }\r\n  }\r\n\r\n  if (Object.keys(chunks).length === 0) {\r\n    return undefined;\r\n  }\r\n\r\n  return chunks;\r\n}\r\n\r\nfunction mergeEcmascriptChunkUpdates(\r\n  updateA: EcmascriptMergedChunkUpdate,\r\n  updateB: EcmascriptMergedChunkUpdate\r\n): EcmascriptMergedChunkUpdate | undefined {\r\n  if (updateA.type === \"added\" && updateB.type === \"deleted\") {\r\n    // These two completely cancel each other out.\r\n    return undefined;\r\n  }\r\n\r\n  if (updateA.type === \"deleted\" && updateB.type === \"added\") {\r\n    const added = [];\r\n    const deleted = [];\r\n    const deletedModules = new Set(updateA.modules ?? []);\r\n    const addedModules = new Set(updateB.modules ?? []);\r\n\r\n    for (const moduleId of addedModules) {\r\n      if (!deletedModules.has(moduleId)) {\r\n        added.push(moduleId);\r\n      }\r\n    }\r\n\r\n    for (const moduleId of deletedModules) {\r\n      if (!addedModules.has(moduleId)) {\r\n        deleted.push(moduleId);\r\n      }\r\n    }\r\n\r\n    if (added.length === 0 && deleted.length === 0) {\r\n      return undefined;\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added,\r\n      deleted,\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"partial\") {\r\n    const added = new Set([...(updateA.added ?? []), ...(updateB.added ?? [])]);\r\n    const deleted = new Set([\r\n      ...(updateA.deleted ?? []),\r\n      ...(updateB.deleted ?? []),\r\n    ]);\r\n\r\n    if (updateB.added != null) {\r\n      for (const moduleId of updateB.added) {\r\n        deleted.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    if (updateB.deleted != null) {\r\n      for (const moduleId of updateB.deleted) {\r\n        added.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"partial\",\r\n      added: [...added],\r\n      deleted: [...deleted],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"added\" && updateB.type === \"partial\") {\r\n    const modules = new Set([\r\n      ...(updateA.modules ?? []),\r\n      ...(updateB.added ?? []),\r\n    ]);\r\n\r\n    for (const moduleId of updateB.deleted ?? []) {\r\n      modules.delete(moduleId);\r\n    }\r\n\r\n    return {\r\n      type: \"added\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  if (updateA.type === \"partial\" && updateB.type === \"deleted\") {\r\n    // We could eagerly return `updateB` here, but this would potentially be\r\n    // incorrect if `updateA` has added modules.\r\n\r\n    const modules = new Set(updateB.modules ?? []);\r\n\r\n    if (updateA.added != null) {\r\n      for (const moduleId of updateA.added) {\r\n        modules.delete(moduleId);\r\n      }\r\n    }\r\n\r\n    return {\r\n      type: \"deleted\",\r\n      modules: [...modules],\r\n    };\r\n  }\r\n\r\n  // Any other update combination is invalid.\r\n\r\n  return undefined;\r\n}\r\n\r\nfunction invariant(_: never, message: string): never {\r\n  throw new Error(`Invariant: ${message}`);\r\n}\r\n\r\nconst CRITICAL = [\"bug\", \"error\", \"fatal\"];\r\n\r\nfunction compareByList(list: any[], a: any, b: any) {\r\n  const aI = list.indexOf(a) + 1 || list.length;\r\n  const bI = list.indexOf(b) + 1 || list.length;\r\n  return aI - bI;\r\n}\r\n\r\nconst chunksWithIssues: Map<ResourceKey, Issue[]> = new Map();\r\n\r\nfunction emitIssues() {\r\n  const issues = [];\r\n  const deduplicationSet = new Set();\r\n\r\n  for (const [_, chunkIssues] of chunksWithIssues) {\r\n    for (const chunkIssue of chunkIssues) {\r\n      if (deduplicationSet.has(chunkIssue.formatted)) continue;\r\n\r\n      issues.push(chunkIssue);\r\n      deduplicationSet.add(chunkIssue.formatted);\r\n    }\r\n  }\r\n\r\n  sortIssues(issues);\r\n\r\n  hooks.issues(issues);\r\n}\r\n\r\nfunction handleIssues(msg: ServerMessage): boolean {\r\n  const key = resourceKey(msg.resource);\r\n  let hasCriticalIssues = false;\r\n\r\n  for (const issue of msg.issues) {\r\n    if (CRITICAL.includes(issue.severity)) {\r\n      hasCriticalIssues = true;\r\n    }\r\n  }\r\n\r\n  if (msg.issues.length > 0) {\r\n    chunksWithIssues.set(key, msg.issues);\r\n  } else if (chunksWithIssues.has(key)) {\r\n    chunksWithIssues.delete(key);\r\n  }\r\n\r\n  emitIssues();\r\n\r\n  return hasCriticalIssues;\r\n}\r\n\r\nconst SEVERITY_ORDER = [\"bug\", \"fatal\", \"error\", \"warning\", \"info\", \"log\"];\r\nconst CATEGORY_ORDER = [\r\n  \"parse\",\r\n  \"resolve\",\r\n  \"code generation\",\r\n  \"rendering\",\r\n  \"typescript\",\r\n  \"other\",\r\n];\r\n\r\nfunction sortIssues(issues: Issue[]) {\r\n  issues.sort((a, b) => {\r\n    const first = compareByList(SEVERITY_ORDER, a.severity, b.severity);\r\n    if (first !== 0) return first;\r\n    return compareByList(CATEGORY_ORDER, a.category, b.category);\r\n  });\r\n}\r\n\r\nconst hooks = {\r\n  beforeRefresh: () => {},\r\n  refresh: () => {},\r\n  buildOk: () => {},\r\n  issues: (_issues: Issue[]) => {},\r\n};\r\n\r\nexport function setHooks(newHooks: typeof hooks) {\r\n  Object.assign(hooks, newHooks);\r\n}\r\n\r\nfunction handleSocketMessage(msg: ServerMessage) {\r\n  sortIssues(msg.issues);\r\n\r\n  handleIssues(msg);\r\n\r\n  switch (msg.type) {\r\n    case \"issues\":\r\n      // issues are already handled\r\n      break;\r\n    case \"partial\":\r\n      // aggregate updates\r\n      aggregateUpdates(msg);\r\n      break;\r\n    default:\r\n      // run single update\r\n      const runHooks = chunkListsWithPendingUpdates.size === 0;\r\n      if (runHooks) hooks.beforeRefresh();\r\n      triggerUpdate(msg);\r\n      if (runHooks) finalizeUpdate();\r\n      break;\r\n  }\r\n}\r\n\r\nfunction finalizeUpdate() {\r\n  hooks.refresh();\r\n  hooks.buildOk();\r\n\r\n  // This is used by the Next.js integration test suite to notify it when HMR\r\n  // updates have been completed.\r\n  // TODO: Only run this in test environments (gate by `process.env.__NEXT_TEST_MODE`)\r\n  if (globalThis.__NEXT_HMR_CB) {\r\n    globalThis.__NEXT_HMR_CB();\r\n    globalThis.__NEXT_HMR_CB = null;\r\n  }\r\n}\r\n\r\nfunction subscribeToChunkUpdate(\r\n  chunkListPath: ChunkListPath,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n): () => void {\r\n  return subscribeToUpdate(\r\n    {\r\n      path: chunkListPath,\r\n    },\r\n    sendMessage,\r\n    callback\r\n  );\r\n}\r\n\r\nexport function subscribeToUpdate(\r\n  resource: ResourceIdentifier,\r\n  sendMessage: SendMessage,\r\n  callback: UpdateCallback\r\n) {\r\n  const key = resourceKey(resource);\r\n  let callbackSet: UpdateCallbackSet;\r\n  const existingCallbackSet = updateCallbackSets.get(key);\r\n  if (!existingCallbackSet) {\r\n    callbackSet = {\r\n      callbacks: new Set([callback]),\r\n      unsubscribe: subscribeToUpdates(sendMessage, resource),\r\n    };\r\n    updateCallbackSets.set(key, callbackSet);\r\n  } else {\r\n    existingCallbackSet.callbacks.add(callback);\r\n    callbackSet = existingCallbackSet;\r\n  }\r\n\r\n  return () => {\r\n    callbackSet.callbacks.delete(callback);\r\n\r\n    if (callbackSet.callbacks.size === 0) {\r\n      callbackSet.unsubscribe();\r\n      updateCallbackSets.delete(key);\r\n    }\r\n  };\r\n}\r\n\r\nfunction triggerUpdate(msg: ServerMessage) {\r\n  const key = resourceKey(msg.resource);\r\n  const callbackSet = updateCallbackSets.get(key);\r\n  if (!callbackSet) {\r\n    return;\r\n  }\r\n\r\n  for (const callback of callbackSet.callbacks) {\r\n    callback(msg);\r\n  }\r\n\r\n  if (msg.type === \"notFound\") {\r\n    // This indicates that the resource which we subscribed to either does not exist or\r\n    // has been deleted. In either case, we should clear all update callbacks, so if a\r\n    // new subscription is created for the same resource, it will send a new \"subscribe\"\r\n    // message to the server.\r\n    // No need to send an \"unsubscribe\" message to the server, it will have already\r\n    // dropped the update stream before sending the \"notFound\" message.\r\n    updateCallbackSets.delete(key);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,4DAA4D;AAC5D,6DAA6D;AAC7D,6DAA6D;;;;;;AAmBtD,SAAS,QAAQ,EACtB,kBAAkB,EAClB,WAAW,EACX,gBAAgB,QAAQ,KAAK,EACf;IACd,mBAAmB,CAAC;QAClB,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,sBAAsB;gBACtB;YACF;gBACE,IAAI;oBACF,IAAI,MAAM,OAAO,CAAC,IAAI,IAAI,GAAG;wBAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,IAAK;4BACxC,oBAAoB,IAAI,IAAI,CAAC,EAAE;wBACjC;oBACF,OAAO;wBACL,oBAAoB,IAAI,IAAI;oBAC9B;oBACA;gBACF,EAAE,OAAO,GAAY;oBACnB,QAAQ,IAAI,CACV,8CACE,mIACA,qIACA,+GACA,8HACA;oBAEJ,cAAc;oBACd,SAAS,MAAM;gBACjB;gBACA;QACJ;IACF;IAEA,MAAM,SAAS,WAAW,gCAAgC;IAC1D,IAAI,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS;QAC5C,MAAM,IAAI,MAAM;IAClB;IACA,WAAW,gCAAgC,GAAG;QAC5C,MAAM,CAAC,CAAC,WAAW,SAA0C;YAC3D,uBAAuB,WAAW,aAAa;QACjD;IACF;IAEA,IAAI,MAAM,OAAO,CAAC,SAAS;QACzB,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,OAAQ;YAC1C,uBAAuB,WAAW,aAAa;QACjD;IACF;AACF;AAOA,MAAM,qBAA0D,IAAI;AAEpE,SAAS,SAAS,WAAwB,EAAE,OAAsB;IAChE,YAAY,KAAK,SAAS,CAAC;AAC7B;AAIA,SAAS,YAAY,QAA4B;IAC/C,OAAO,KAAK,SAAS,CAAC;QACpB,MAAM,SAAS,IAAI;QACnB,SAAS,SAAS,OAAO,IAAI;IAC/B;AACF;AAEA,SAAS,mBACP,WAAwB,EACxB,QAA4B;IAE5B,SAAS,aAAa;QACpB,MAAM;QACN,GAAG,QAAQ;IACb;IAEA,OAAO;QACL,SAAS,aAAa;YACpB,MAAM;YACN,GAAG,QAAQ;QACb;IACF;AACF;AAEA,SAAS,sBAAsB,WAAwB;IACrD,KAAK,MAAM,OAAO,mBAAmB,IAAI,GAAI;QAC3C,mBAAmB,aAAa,KAAK,KAAK,CAAC;IAC7C;AACF;AAEA,iEAAiE;AACjE,MAAM,+BACJ,IAAI;AAEN,SAAS,iBAAiB,GAAyB;IACjD,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,aAAa,6BAA6B,GAAG,CAAC;IAElD,IAAI,YAAY;QACd,WAAW,WAAW,GAAG,sBACvB,WAAW,WAAW,EACtB,IAAI,WAAW;IAEnB,OAAO;QACL,6BAA6B,GAAG,CAAC,KAAK;IACxC;AACF;AAEA,SAAS;IACP,IAAI,6BAA6B,IAAI,KAAK,GAAG;IAC7C,MAAM,aAAa;IACnB,KAAK,MAAM,OAAO,6BAA6B,MAAM,GAAI;QACvD,cAAc;IAChB;IACA,6BAA6B,KAAK;IAClC;AACF;AAEA,SAAS,sBACP,OAAwB,EACxB,OAAwB;IAExB,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,SAAS,qBAAqB,QAAQ,MAAM,EAAE,QAAQ,MAAM;QAC9D;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,IAAI;IACJ,IAAI,QAAQ,MAAM,IAAI,MAAM;QAC1B,IAAI,QAAQ,MAAM,IAAI,MAAM;YAC1B,SAAS,QAAQ,MAAM;QACzB,OAAO;YACL,wEAAwE;YACxE,0BAA0B;YAC1B,0EAA0E;YAC1E,sCAAsC;YACtC,IAAI,SAAS,QAAQ,MAAM,CAAC,EAAE;YAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE,IAAK;gBAC9C,SAAS,sCACP,QACA,QAAQ,MAAM,CAAC,EAAE;YAErB;YAEA,SAAS;gBAAC;aAAO;QACnB;IACF,OAAO,IAAI,QAAQ,MAAM,IAAI,MAAM;QACjC,SAAS,QAAQ,MAAM;IACzB;IAEA,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,qBACP,OAAuC,EACvC,OAAuC;IAEvC,MAAM,SAAyC,CAAC;IAEhD,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,kBAAkB,cAAc;YACrD,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6C;QAClG,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,kBACP,OAAoB,EACpB,OAAoB;IAEpB,IACE,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,aAC7C,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAChD;QACA,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,UAAU,QAAQ,WAAW,EAAE;IACjC;IAEA,OAAO;AACT;AAEA,SAAS,sCACP,OAA+B,EAC/B,OAA+B;IAE/B,MAAM,UAAU,4BAA4B,QAAQ,OAAO,EAAE,QAAQ,OAAO;IAC5E,MAAM,SAAS,6BAA6B,QAAQ,MAAM,EAAE,QAAQ,MAAM;IAE1E,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;AAEA,SAAS,4BACP,QAA6D,EAC7D,QAA6D;IAE7D,OAAO;QAAE,GAAG,QAAQ;QAAE,GAAG,QAAQ;IAAC;AACpC;AAEA,SAAS,6BACP,OAAmE,EACnE,OAAmE;IAEnE,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,IAAI,WAAW,MAAM;QACnB,OAAO;IACT;IAEA,MAAM,SAAyD,CAAC;IAEhE,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,MAAM,eAAe,OAAO,CAAC,UAAU;QACvC,IAAI,gBAAgB,MAAM;YACxB,MAAM,eAAe,4BACnB,cACA;YAEF,IAAI,gBAAgB,MAAM;gBACxB,MAAM,CAAC,UAAU,GAAG;YACtB;QACF,OAAO;YACL,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,KAAK,MAAM,CAAC,WAAW,aAAa,IAAI,OAAO,OAAO,CAAC,SAA6D;QAClH,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;YAC7B,MAAM,CAAC,UAAU,GAAG;QACtB;IACF;IAEA,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAAS,4BACP,OAAoC,EACpC,OAAoC;IAEpC,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,8CAA8C;QAC9C,OAAO;IACT;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,SAAS;QAC1D,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,EAAE;QAClB,MAAM,iBAAiB,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QACpD,MAAM,eAAe,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAElD,KAAK,MAAM,YAAY,aAAc;YACnC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW;gBACjC,MAAM,IAAI,CAAC;YACb;QACF;QAEA,KAAK,MAAM,YAAY,eAAgB;YACrC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;YAC9C,OAAO;QACT;QAEA,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,MAAM,QAAQ,IAAI,IAAI;eAAK,QAAQ,KAAK,IAAI,EAAE;eAAO,QAAQ,KAAK,IAAI,EAAE;SAAE;QAC1E,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,OAAO,IAAI,EAAE;SAC1B;QAED,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,IAAI,QAAQ,OAAO,IAAI,MAAM;YAC3B,KAAK,MAAM,YAAY,QAAQ,OAAO,CAAE;gBACtC,MAAM,MAAM,CAAC;YACf;QACF;QAEA,OAAO;YACL,MAAM;YACN,OAAO;mBAAI;aAAM;YACjB,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW;QAC1D,MAAM,UAAU,IAAI,IAAI;eAClB,QAAQ,OAAO,IAAI,EAAE;eACrB,QAAQ,KAAK,IAAI,EAAE;SACxB;QAED,KAAK,MAAM,YAAY,QAAQ,OAAO,IAAI,EAAE,CAAE;YAC5C,QAAQ,MAAM,CAAC;QACjB;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,IAAI,QAAQ,IAAI,KAAK,aAAa,QAAQ,IAAI,KAAK,WAAW;QAC5D,wEAAwE;QACxE,4CAA4C;QAE5C,MAAM,UAAU,IAAI,IAAI,QAAQ,OAAO,IAAI,EAAE;QAE7C,IAAI,QAAQ,KAAK,IAAI,MAAM;YACzB,KAAK,MAAM,YAAY,QAAQ,KAAK,CAAE;gBACpC,QAAQ,MAAM,CAAC;YACjB;QACF;QAEA,OAAO;YACL,MAAM;YACN,SAAS;mBAAI;aAAQ;QACvB;IACF;IAEA,2CAA2C;IAE3C,OAAO;AACT;AAEA,SAAS,UAAU,CAAQ,EAAE,OAAe;IAC1C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;AACzC;AAEA,MAAM,WAAW;IAAC;IAAO;IAAS;CAAQ;AAE1C,SAAS,cAAc,IAAW,EAAE,CAAM,EAAE,CAAM;IAChD,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,MAAM,KAAK,KAAK,OAAO,CAAC,KAAK,KAAK,KAAK,MAAM;IAC7C,OAAO,KAAK;AACd;AAEA,MAAM,mBAA8C,IAAI;AAExD,SAAS;IACP,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,IAAI;IAE7B,KAAK,MAAM,CAAC,GAAG,YAAY,IAAI,iBAAkB;QAC/C,KAAK,MAAM,cAAc,YAAa;YACpC,IAAI,iBAAiB,GAAG,CAAC,WAAW,SAAS,GAAG;YAEhD,OAAO,IAAI,CAAC;YACZ,iBAAiB,GAAG,CAAC,WAAW,SAAS;QAC3C;IACF;IAEA,WAAW;IAEX,MAAM,MAAM,CAAC;AACf;AAEA,SAAS,aAAa,GAAkB;IACtC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,IAAI,oBAAoB;IAExB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAE;QAC9B,IAAI,SAAS,QAAQ,CAAC,MAAM,QAAQ,GAAG;YACrC,oBAAoB;QACtB;IACF;IAEA,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG;QACzB,iBAAiB,GAAG,CAAC,KAAK,IAAI,MAAM;IACtC,OAAO,IAAI,iBAAiB,GAAG,CAAC,MAAM;QACpC,iBAAiB,MAAM,CAAC;IAC1B;IAEA;IAEA,OAAO;AACT;AAEA,MAAM,iBAAiB;IAAC;IAAO;IAAS;IAAS;IAAW;IAAQ;CAAM;AAC1E,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS,WAAW,MAAe;IACjC,OAAO,IAAI,CAAC,CAAC,GAAG;QACd,MAAM,QAAQ,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;QAClE,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO,cAAc,gBAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ;IAC7D;AACF;AAEA,MAAM,QAAQ;IACZ,eAAe,KAAO;IACtB,SAAS,KAAO;IAChB,SAAS,KAAO;IAChB,QAAQ,CAAC,WAAsB;AACjC;AAEO,SAAS,SAAS,QAAsB;IAC7C,OAAO,MAAM,CAAC,OAAO;AACvB;AAEA,SAAS,oBAAoB,GAAkB;IAC7C,WAAW,IAAI,MAAM;IAErB,aAAa;IAEb,OAAQ,IAAI,IAAI;QACd,KAAK;YAEH;QACF,KAAK;YACH,oBAAoB;YACpB,iBAAiB;YACjB;QACF;YACE,oBAAoB;YACpB,MAAM,WAAW,6BAA6B,IAAI,KAAK;YACvD,IAAI,UAAU,MAAM,aAAa;YACjC,cAAc;YACd,IAAI,UAAU;YACd;IACJ;AACF;AAEA,SAAS;IACP,MAAM,OAAO;IACb,MAAM,OAAO;IAEb,2EAA2E;IAC3E,+BAA+B;IAC/B,oFAAoF;IACpF,IAAI,WAAW,aAAa,EAAE;QAC5B,WAAW,aAAa;QACxB,WAAW,aAAa,GAAG;IAC7B;AACF;AAEA,SAAS,uBACP,aAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,OAAO,kBACL;QACE,MAAM;IACR,GACA,aACA;AAEJ;AAEO,SAAS,kBACd,QAA4B,EAC5B,WAAwB,EACxB,QAAwB;IAExB,MAAM,MAAM,YAAY;IACxB,IAAI;IACJ,MAAM,sBAAsB,mBAAmB,GAAG,CAAC;IACnD,IAAI,CAAC,qBAAqB;QACxB,cAAc;YACZ,WAAW,IAAI,IAAI;gBAAC;aAAS;YAC7B,aAAa,mBAAmB,aAAa;QAC/C;QACA,mBAAmB,GAAG,CAAC,KAAK;IAC9B,OAAO;QACL,oBAAoB,SAAS,CAAC,GAAG,CAAC;QAClC,cAAc;IAChB;IAEA,OAAO;QACL,YAAY,SAAS,CAAC,MAAM,CAAC;QAE7B,IAAI,YAAY,SAAS,CAAC,IAAI,KAAK,GAAG;YACpC,YAAY,WAAW;YACvB,mBAAmB,MAAM,CAAC;QAC5B;IACF;AACF;AAEA,SAAS,cAAc,GAAkB;IACvC,MAAM,MAAM,YAAY,IAAI,QAAQ;IACpC,MAAM,cAAc,mBAAmB,GAAG,CAAC;IAC3C,IAAI,CAAC,aAAa;QAChB;IACF;IAEA,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;QAC5C,SAAS;IACX;IAEA,IAAI,IAAI,IAAI,KAAK,YAAY;QAC3B,mFAAmF;QACnF,kFAAkF;QAClF,oFAAoF;QACpF,yBAAyB;QACzB,+EAA+E;QAC/E,mEAAmE;QACnE,mBAAmB,MAAM,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/axiosConfig.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create an instance with smart server URL detection\r\nlet baseURL;\r\n\r\n// Check if we're on localhost (client-side)\r\nif (typeof window !== 'undefined' && window.location.hostname === 'localhost') {\r\n    baseURL = 'http://localhost:5000';\r\n} else if (process.env.NEXT_PUBLIC_SERVER_URL) {\r\n    baseURL = process.env.NEXT_PUBLIC_SERVER_URL;\r\n} else if (process.env.REACT_APP_API_URL) {\r\n    baseURL = process.env.REACT_APP_API_URL;\r\n} else {\r\n    // Default to production server\r\n    baseURL = 'https://nextalk-u0y1.onrender.com';\r\n}\r\n\r\nconsole.log('🌐 Axios baseURL:', baseURL);\r\n\r\nconst axiosInstance = axios.create({\r\n    baseURL: baseURL,\r\n    withCredentials: true,           // Crucial for sending cookies\r\n    headers: {\r\n        \"Content-Type\": \"application/json\",\r\n    },\r\n    timeout: 10000, // 10 second timeout\r\n});\r\n\r\n// Add request interceptor for debugging\r\naxiosInstance.interceptors.request.use(\r\n    (config) => {\r\n        console.log('📤 Axios request:', config.method?.toUpperCase(), config.url);\r\n        return config;\r\n    },\r\n    (error) => {\r\n        console.error('❌ Axios request error:', error);\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Add response interceptor for debugging\r\naxiosInstance.interceptors.response.use(\r\n    (response) => {\r\n        console.log('✅ Axios response:', response.status, response.config.url);\r\n        return response;\r\n    },\r\n    (error) => {\r\n        console.error('❌ Axios response error:', error.message);\r\n        if (error.code === 'ECONNREFUSED' || error.message === 'Network Error') {\r\n            console.error('🚨 Server connection failed. Is your backend running on', baseURL, '?');\r\n        }\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAQW;AARX;;AAEA,qDAAqD;AACrD,IAAI;AAEJ,4CAA4C;AAC5C,IAAI,aAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,aAAa;IAC3E,UAAU;AACd,OAAO,IAAI,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE;IAC3C,UAAU,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;AAChD,OAAO,IAAI,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;IACtC,UAAU,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;AAC3C,OAAO;IACH,+BAA+B;IAC/B,UAAU;AACd;AAEA,QAAQ,GAAG,CAAC,qBAAqB;AAEjC,MAAM,gBAAgB,iIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS;IACT,iBAAiB;IACjB,SAAS;QACL,gBAAgB;IACpB;IACA,SAAS;AACb;AAEA,wCAAwC;AACxC,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;IACG,QAAQ,GAAG,CAAC,qBAAqB,OAAO,MAAM,EAAE,eAAe,OAAO,GAAG;IACzE,OAAO;AACX,GACA,CAAC;IACG,QAAQ,KAAK,CAAC,0BAA0B;IACxC,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGJ,yCAAyC;AACzC,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC;IACG,QAAQ,GAAG,CAAC,qBAAqB,SAAS,MAAM,EAAE,SAAS,MAAM,CAAC,GAAG;IACrE,OAAO;AACX,GACA,CAAC;IACG,QAAQ,KAAK,CAAC,2BAA2B,MAAM,OAAO;IACtD,IAAI,MAAM,IAAI,KAAK,kBAAkB,MAAM,OAAO,KAAK,iBAAiB;QACpE,QAAQ,KAAK,CAAC,2DAA2D,SAAS;IACtF;IACA,OAAO,QAAQ,MAAM,CAAC;AAC1B;uCAGW", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/public/Images/predefine.webp.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 200, height: 200, blurDataURL: \"data:image/webp;base64,UklGRsEAAABXRUJQVlA4TLUAAAAvB8ABEM1VICICHghADgIAAIAjADAABggAAAzICAAAAKBkQAHAAAAAQhAIsw1ABAAFAAgECiwArdUDAAAiPBAQFAYAAIDzvx8AwhiAAQAeAkCABhAAAABAAAAABAAAAAjgAQFAEAAgAGMQACsh2ZHf7ATh22f6gfC2Tka2Cg+YazNXfRHXLjMnQxFfOblIImMZPiKeA/IkfpZmzO3Ig9vG5navodRjfKkafQkLbF2l5iGV1g4AAA==\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,uHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA2S,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/context/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext } from 'react';\r\n\r\nconst ThemeContext = createContext();\r\n\r\nexport const ThemeProvider = ({ children }) => {\r\n    const [theme, setTheme] = useState('homeback');\r\n\r\n    const handleThemeClick = (newTheme) => {\r\n        setTheme(newTheme);\r\n    };\r\n\r\n    return (\r\n        <ThemeContext.Provider value={{ theme, handleThemeClick }}>\r\n            {children}\r\n        </ThemeContext.Provider>\r\n    );\r\n};\r\n\r\nexport const useTheme = () => useContext(ThemeContext);\r\n"], "names": [], "mappings": ";;;;;AAAA;;;;AAEA,MAAM,6BAAe,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;AAE1B,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE;;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,mBAAmB,CAAC;QACtB,SAAS;IACb;IAEA,qBACI,0JAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;QAAiB;kBACnD;;;;;;AAGb;GAZa;KAAA;AAcN,MAAM,WAAW;;IAAM,OAAA,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD,EAAE;AAAY;IAAxC", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Components/DashboardLayout.js"], "sourcesContent": ["// DashboardLayout.jsx\r\n'use client'; // if you're using App Router (recommended)\r\n\r\nimport Link from 'next/link';\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Image from 'next/image';\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport axios from '../../utils/axiosConfig';\r\n\r\nexport default function DashboardLayout({ children }) {\r\n    const [isSidebarOpen, setIsSidebarOpen] = useState(false);\r\n    const { theme, handleThemeClick } = useTheme();\r\n    const router = useRouter(); // corrected here\r\n\r\n    const toggleTheme = () => {\r\n        const newTheme = theme === 'dark' ? 'light' : 'dark';\r\n        handleThemeClick(newTheme);\r\n    };\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return { background: '#0f172a', color: '#e2e8f0', chatBackground: '#1e293b', cardBg: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)' };\r\n        }\r\n        return { background: '#f1f5f9', color: '#1e293b', chatBackground: '#ffffff' };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyles();\r\n    const [user, setUser] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const storedUser = sessionStorage.getItem(\"user\");\r\n        if (!storedUser) {\r\n            router.push(\"/\");\r\n        } else {\r\n            try {\r\n                const parsed = JSON.parse(storedUser);\r\n                setUser(parsed.user);\r\n            } catch (err) {\r\n                console.error(\"Error parsing sessionStorage user:\", err);\r\n                router.push(\"/\");\r\n            }\r\n        }\r\n    }, [router]);\r\n\r\n    const [profile, setProfile] = useState(null);\r\n\r\n    const [tempProfile, setTempProfile] = useState(profile);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    const fetchProfile = async () => {\r\n        setLoading(true);\r\n        const userData = JSON.parse(sessionStorage.getItem('user') || '{}');\r\n\r\n        if (!userData?.user?.id) {\r\n            setError('No user data found. Please log in.');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        try {\r\n            const response = await axios.get('https://nextalk-u0y1.onrender.com/profile', {\r\n                headers: {\r\n                    Authorization: `Bearer ${userData.user.id}`,\r\n                },\r\n            });\r\n            const fetchedProfile = response.data.user || response.data;\r\n            setProfile(fetchedProfile);\r\n            setTempProfile(fetchedProfile);\r\n            setLoading(false);\r\n        } catch (err) {\r\n            const errorMessage = err.response?.data?.message || 'Failed to load profile.';\r\n            setError(errorMessage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (typeof window !== 'undefined') {\r\n            fetchProfile();\r\n        }\r\n    }, []);\r\n\r\n\r\n    const pathname = usePathname();\r\n    const [brandText, setBrandText] = useState(\"Nextalk\");\r\n    useEffect(() => {\r\n        if (pathname === \"/Dashboard/Profile\") {\r\n            setBrandText(profile?.name || \"User\");\r\n        } else {\r\n            setBrandText(\"Nextalk\");\r\n        }\r\n    }, [pathname, profile]);\r\n\r\n\r\n    const [showConfirm, setShowConfirm] = useState(false);\r\n    const handleLogout = () => {\r\n        setShowConfirm(true);\r\n    };\r\n\r\n    const handleConfirmUpload = async (e) => {\r\n        sessionStorage.removeItem(\"user\");\r\n        router.push(\"/\");\r\n    }\r\n\r\n    const [pendingCount, setPendingCount] = useState(0);\r\n\r\n    useEffect(() => {\r\n        const fetchPendingRequests = async () => {\r\n            try {\r\n                const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n                const sessionId = storedUser?.user?.id;\r\n\r\n                if (!sessionId) {\r\n                    console.log(\"No session ID found, skipping pending requests fetch\");\r\n                    return;\r\n                }\r\n\r\n                // Add timeout and better error handling\r\n                const controller = new AbortController();\r\n                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout\r\n\r\n                const res = await axios.get(\r\n                    `https://nextalk-u0y1.onrender.com/pending-follow-requests/${sessionId}`,\r\n                    {\r\n                        signal: controller.signal,\r\n                        timeout: 10000,\r\n                        headers: {\r\n                            'Content-Type': 'application/json'\r\n                        }\r\n                    }\r\n                );\r\n\r\n                clearTimeout(timeoutId);\r\n                setPendingCount(res.data.count || 0);\r\n                console.log(\"✅ Pending requests fetched successfully:\", res.data.count);\r\n\r\n            } catch (err) {\r\n                if (err.name === 'AbortError') {\r\n                    console.log(\"⏰ Request timeout - skipping pending requests\");\r\n                } else if (err.code === 'NETWORK_ERROR' || err.message.includes('Network Error')) {\r\n                    console.log(\"🌐 Network error - will retry later\");\r\n                } else {\r\n                    console.error(\"❌ Failed to fetch pending request count:\", err.message);\r\n                }\r\n                // Set default count on error\r\n                setPendingCount(0);\r\n            }\r\n        };\r\n\r\n        // Initial fetch with delay to avoid immediate network issues\r\n        const initialTimeout = setTimeout(() => {\r\n            fetchPendingRequests();\r\n        }, 2000);\r\n\r\n        // OPTIONAL: Poll every 60s for updates (increased interval to reduce network load)\r\n        const interval = setInterval(fetchPendingRequests, 60000);\r\n\r\n        return () => {\r\n            clearTimeout(initialTimeout);\r\n            clearInterval(interval);\r\n        };\r\n    }, []);\r\n\r\n    const [users, setUsers] = useState([]);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [searchResults, setSearchResults] = useState([]);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                const response = await axios.post(\r\n                    \"https://nextalk-u0y1.onrender.com/displayusersProfile\",\r\n                    {},\r\n                    {\r\n                        headers: { 'Content-Type': 'application/json' },\r\n                        withCredentials: true\r\n                    }\r\n                );\r\n\r\n                const allUsers = response.data;\r\n                const filtered = allUsers.filter(user => user._id !== sessionId);\r\n                setUsers(filtered);\r\n            } catch (error) {\r\n                console.error(\"Error fetching users:\", error);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        if (searchTerm.trim() === '') {\r\n            setSearchResults([]);\r\n            return;\r\n        }\r\n\r\n        const results = users.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setSearchResults(results);\r\n    }, [searchTerm, users]);\r\n\r\n\r\n    return (\r\n        <div className=\"dashboard-wrapper\" style={{ background: currentThemeStyles.background, color: currentThemeStyles.color }}>\r\n            {/* Mobile Navbar */}\r\n            <nav className={`navbar sleek-navbar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} d-lg-none`}>\r\n                <div className=\"container-fluid\">\r\n                    <Link className=\"navbar-brand fw-bold sleek-brand\" style={{ textDecoration: \"none\" }} href=\"/dashboard/profile\">{brandText}</Link>\r\n                    <button\r\n                        className={`navbar-toggler sleek-toggler ${theme === 'dark' ? 'dark-toggler' : 'light-toggler'}`}\r\n                        type=\"button\"\r\n                        onClick={() => setIsSidebarOpen(!isSidebarOpen)}\r\n                    >\r\n                        <span className=\"navbar-toggler-icon\"></span>\r\n                    </button>\r\n                </div>\r\n            </nav>\r\n\r\n            {/* Sidebar */}\r\n            <aside className={`sidebar sleek-sidebar ${theme === 'dark' ? 'bg-dark' : 'bg-light'} ${isSidebarOpen ? 'open' : ''}`}>\r\n                <div className=\"sidebar-header sleek-header\">\r\n                    <h4 className=\"p-3 fw-bold text-uppercase d-none d-lg-block\">{brandText}</h4>\r\n                </div>\r\n\r\n                <ul className=\"nav flex-column p-3 sleek-nav\">\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard\">\r\n                            <i className=\"bi bi-house-fill me-2\"></i>Home\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <button className=\"nav-link sleek-nav-link w-100\" data-bs-toggle=\"offcanvas\" data-bs-target=\"#Search\" style={{ textDecoration: \"none\" }}>\r\n                            <i className=\"bi bi-search me-2\"></i>Search\r\n                        </button>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-box me-2\"></i>Chat with Random\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-plus-square me-2\"></i>Create\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Messages\">\r\n                            <i className=\"bi bi-chat-fill me-2\"></i>Messages\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Chats\">\r\n                            <i className=\"bi bi-cpu me-2\"></i>Chat with NexTalk\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item ot-but\">\r\n                        <Link className=\"nav-link sleek-nav-link\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Settings\">\r\n                            <i className=\"bi bi-gear-wide-connected me-2\"></i>Settings\r\n                        </Link>\r\n                    </li>\r\n                    <li className=\"nav-item\">\r\n                        <Link className=\"nav-link sleek-nav-link justify-content-between\" onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} href=\"/Dashboard/Notification\">\r\n                            <div>\r\n                                <i className=\"bi bi-heart me-2\"></i>Notification\r\n                            </div>\r\n                            {pendingCount > 0 && (\r\n                                <span\r\n                                    style={{\r\n                                        backgroundColor: \"#008080\",\r\n                                        color: \"white\",\r\n                                        fontSize: \"0.7rem\",\r\n                                        padding: \"6px 12px\",\r\n                                        borderRadius: \"50%\",\r\n                                        top: \"10px\",\r\n                                        right: \"10px\",\r\n                                    }}\r\n                                >\r\n                                    {pendingCount}\r\n                                </span>\r\n                            )}\r\n                        </Link>\r\n                    </li><br />\r\n                    <li className='nav-item'>\r\n                        <Link href=\"\"\r\n                            className=\"btn btn-primary w-100 p-2 d-lg-none\" style={{ textDecoration: \"none\" }}\r\n                            type=\"button\"\r\n                            onClick={() => setIsSidebarOpen(false)}\r\n                        >Close </Link></li>\r\n                </ul>\r\n\r\n\r\n                <div className=\"sidebar-footer p-3 sleek-footer\">\r\n                    {loading ? (\r\n                        <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ background: \"darkgray\", textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                            <div\r\n                                className=\"skeleton\"\r\n                                style={{\r\n                                    width: \"45px\",\r\n                                    height: \"45px\",\r\n                                    borderRadius: \"50%\",\r\n                                }}\r\n                            ></div>\r\n                            <div>\r\n                                <div\r\n                                    className=\"skeleton\"\r\n                                    style={{\r\n                                        width: \"120px\",\r\n                                        height: \"16px\",\r\n                                        borderRadius: \"4px\",\r\n                                        marginBottom: \"8px\",\r\n                                    }}\r\n                                ></div>\r\n                                <span className=\"sleek-status online\">Online</span>\r\n                            </div>\r\n                        </Link>\r\n                    ) :\r\n                        (\r\n                            <Link href='/Dashboard/Profile' onClick={() => setIsSidebarOpen(false)} style={{ textDecoration: \"none\" }} className=\"user-profile sleek-profile nav-link d-flex align-items-center gap-3\">\r\n                                {profile && (\r\n                                    <Image\r\n                                        key={profile.image}\r\n                                        src={profile.image || \"/Images/predefine.webp\"}\r\n                                        width={45}\r\n                                        height={45}\r\n                                        alt=\"User\"\r\n                                        className=\"rounded-circle sleek-avatar\"\r\n                                    />\r\n                                )}\r\n                                <div>\r\n                                    <span className=\"d-block fw-semibold sleek-username\">{profile.name || \"Guest\"}</span>\r\n                                    <span className=\"sleek-status online\">Online</span>\r\n                                </div>\r\n                            </Link>\r\n                        )\r\n                    }\r\n                    <div className=\"mt-4 sleek-actions\">\r\n                        <button\r\n                            onClick={toggleTheme}\r\n                            className=\"btn sleek-btn theme-toggle w-100 mb-2\"\r\n                        >\r\n                            {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}\r\n                        </button>\r\n                        <button\r\n                            onClick={handleLogout}\r\n                            className=\"btn sleek-btn logout-btn w-100\"\r\n                        >\r\n                            Log Out\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </aside>\r\n\r\n            {/* Main Content */}\r\n            <main className=\"main-content sleek-main\" style={{ background: currentThemeStyles.chatBackground }}>\r\n                <div className=\"container-fluid p-1 sleek-container\">\r\n                    {children} {/* NOT Outlet! */}\r\n                </div>\r\n                {showConfirm && (\r\n                    <div className=\"modal-overlay\">\r\n                        <div className=\"modal-content\">\r\n                            <h4 className=\"modal-title\">Log Out?</h4>\r\n                            <p className=\"modal-text\">\r\n                                Are you sure you want to log out? You will be signed out of your account and redirected to the login page.\r\n                            </p>\r\n\r\n                            <div className=\"modal-actions\">\r\n                                <button className=\"modal-btn modal-btn-success\" onClick={handleConfirmUpload}>\r\n                                    ✅ Confirm !\r\n                                </button>\r\n                                <button className=\"modal-btn modal-btn-cancel\" onClick={() => setShowConfirm(false)}>\r\n                                    ❌ Cancel\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                )}\r\n                <div className=\"offcanvas offcanvas-start\" style={{ background: currentThemeStyles.cardBg, color: currentThemeStyles.color }} id=\"Search\">\r\n                    <div className=\"offcanvas-header\">\r\n                        <h3 className=\"offcanvas-title\">Search</h3>\r\n                        <button type=\"button\" className=\"btn-close bg-danger\" data-bs-dismiss=\"offcanvas\"></button>\r\n                    </div>\r\n                    <div className=\"offcanvas-body\">\r\n                        <input\r\n                            type=\"search\"\r\n                            name=\"search\"\r\n                            id=\"search\"\r\n                            className=\"form-control mb-3\"\r\n                            placeholder=\"Search users...\"\r\n                            value={searchTerm}\r\n                            style={{\r\n                                backgroundColor: \"white\",\r\n                                transition: \"background 0.3s\",\r\n                                gap: \"10px\",\r\n                                border: \"1px solid #333\"\r\n                            }}\r\n                            onChange={(e) => setSearchTerm(e.target.value)}\r\n                            onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                            onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                        />\r\n                        {\r\n                            loading ? (\r\n                                <div className='d-flex gap-4'>\r\n                                    <div\r\n                                        className=\"skeleton\"\r\n                                        style={{\r\n                                            width: \"45px\",\r\n                                            height: \"45px\",\r\n                                            borderRadius: \"50%\",\r\n                                        }}\r\n                                    ></div>\r\n                                    <div>\r\n                                        <div\r\n                                            className=\"skeleton\"\r\n                                            style={{\r\n                                                width: \"120px\",\r\n                                                height: \"16px\",\r\n                                                borderRadius: \"4px\",\r\n                                                marginBottom: \"8px\",\r\n                                            }}\r\n                                        ></div>\r\n                                    </div>\r\n                                </div>\r\n                            ) : (\r\n                                <div>\r\n                                    {searchResults.map(user => (\r\n                                        <div\r\n                                            key={user._id}\r\n                                            className=\"d-flex gap-4 align-items-center user-result mb-2 p-2 rounded\"\r\n                                            style={{\r\n                                                cursor: \"pointer\",\r\n                                            }}\r\n                                        >\r\n                                            <Image\r\n                                                src={user.image || predefine}\r\n                                                alt={user.name}\r\n                                                width={60}\r\n                                                height={60}\r\n                                                className=\"rounded-circle\"\r\n                                                style={{ objectFit: \"cover\" }}\r\n                                            />\r\n                                            <div>\r\n                                                <strong>{user.username}</strong><br />\r\n                                                <span>{user.name}</span>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                    ))}\r\n\r\n                                    {searchResults.length === 0 && searchTerm && (\r\n                                        <div className=\"text-center mt-4 fade-in\">\r\n                                            <svg\r\n                                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                                width=\"64\"\r\n                                                height=\"64\"\r\n                                                fill=\"gray\"\r\n                                                viewBox=\"0 0 24 24\"\r\n                                                style={{ opacity: 0.5 }}\r\n                                            >\r\n                                                <path d=\"M10 2a8 8 0 015.293 13.707l5 5a1 1 0 01-1.414 1.414l-5-5A8 8 0 1110 2zm0 2a6 6 0 100 12A6 6 0 0010 4z\" />\r\n                                            </svg>\r\n                                            <p className=\"mt-2\">No users found</p>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            )\r\n                        }\r\n                    </div>\r\n                </div>\r\n\r\n            </main>\r\n        </div>\r\n    );\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA,cAAc,2CAA2C;;;;;;;;AAU1C,SAAS,gBAAgB,EAAE,QAAQ,EAAE;;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,8HAAA,CAAA,YAAS,AAAD,KAAK,iBAAiB;IAE7C,MAAM,cAAc;QAChB,MAAM,WAAW,UAAU,SAAS,UAAU;QAC9C,iBAAiB;IACrB;IAEA,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBAAE,YAAY;gBAAW,OAAO;gBAAW,gBAAgB;gBAAW,QAAQ;YAAoD;QAC7I;QACA,OAAO;YAAE,YAAY;YAAW,OAAO;YAAW,gBAAgB;QAAU;IAChF;IAEA,MAAM,qBAAqB;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,MAAM,aAAa,eAAe,OAAO,CAAC;YAC1C,IAAI,CAAC,YAAY;gBACb,OAAO,IAAI,CAAC;YAChB,OAAO;gBACH,IAAI;oBACA,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,QAAQ,OAAO,IAAI;gBACvB,EAAE,OAAO,KAAK;oBACV,QAAQ,KAAK,CAAC,sCAAsC;oBACpD,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;oCAAG;QAAC;KAAO;IAEX,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe;QACjB,WAAW;QACX,MAAM,WAAW,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,WAAW;QAE9D,IAAI,CAAC,UAAU,MAAM,IAAI;YACrB,SAAS;YACT,WAAW;YACX;QACJ;QAEA,IAAI;YACA,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAK,CAAC,GAAG,CAAC,6CAA6C;gBAC1E,SAAS;oBACL,eAAe,CAAC,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE;gBAC/C;YACJ;YACA,MAAM,iBAAiB,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YAC1D,WAAW;YACX,eAAe;YACf,WAAW;QACf,EAAE,OAAO,KAAK;YACV,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YACT,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,wCAAmC;gBAC/B;YACJ;QACJ;oCAAG,EAAE;IAGL,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,IAAI,aAAa,sBAAsB;gBACnC,aAAa,SAAS,QAAQ;YAClC,OAAO;gBACH,aAAa;YACjB;QACJ;oCAAG;QAAC;QAAU;KAAQ;IAGtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;QACjB,eAAe;IACnB;IAEA,MAAM,sBAAsB,OAAO;QAC/B,eAAe,UAAU,CAAC;QAC1B,OAAO,IAAI,CAAC;IAChB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,MAAM;kEAAuB;oBACzB,IAAI;wBACA,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;wBACrD,MAAM,YAAY,YAAY,MAAM;wBAEpC,IAAI,CAAC,WAAW;4BACZ,QAAQ,GAAG,CAAC;4BACZ;wBACJ;wBAEA,wCAAwC;wBACxC,MAAM,aAAa,IAAI;wBACvB,MAAM,YAAY;wFAAW,IAAM,WAAW,KAAK;uFAAI,QAAQ,oBAAoB;wBAEnF,MAAM,MAAM,MAAM,gHAAA,CAAA,UAAK,CAAC,GAAG,CACvB,CAAC,0DAA0D,EAAE,WAAW,EACxE;4BACI,QAAQ,WAAW,MAAM;4BACzB,SAAS;4BACT,SAAS;gCACL,gBAAgB;4BACpB;wBACJ;wBAGJ,aAAa;wBACb,gBAAgB,IAAI,IAAI,CAAC,KAAK,IAAI;wBAClC,QAAQ,GAAG,CAAC,4CAA4C,IAAI,IAAI,CAAC,KAAK;oBAE1E,EAAE,OAAO,KAAK;wBACV,IAAI,IAAI,IAAI,KAAK,cAAc;4BAC3B,QAAQ,GAAG,CAAC;wBAChB,OAAO,IAAI,IAAI,IAAI,KAAK,mBAAmB,IAAI,OAAO,CAAC,QAAQ,CAAC,kBAAkB;4BAC9E,QAAQ,GAAG,CAAC;wBAChB,OAAO;4BACH,QAAQ,KAAK,CAAC,4CAA4C,IAAI,OAAO;wBACzE;wBACA,6BAA6B;wBAC7B,gBAAgB;oBACpB;gBACJ;;YAEA,6DAA6D;YAC7D,MAAM,iBAAiB;4DAAW;oBAC9B;gBACJ;2DAAG;YAEH,mFAAmF;YACnF,MAAM,WAAW,YAAY,sBAAsB;YAEnD;6CAAO;oBACH,aAAa;oBACb,cAAc;gBAClB;;QACJ;oCAAG,EAAE;IAEL,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,MAAM;wDAAa;oBACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;oBACrD,MAAM,YAAY,YAAY,MAAM;oBACpC,iBAAiB;oBAEjB,IAAI;wBACA,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAK,CAAC,IAAI,CAC7B,yDACA,CAAC,GACD;4BACI,SAAS;gCAAE,gBAAgB;4BAAmB;4BAC9C,iBAAiB;wBACrB;wBAGJ,MAAM,WAAW,SAAS,IAAI;wBAC9B,MAAM,WAAW,SAAS,MAAM;6EAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;wBACtD,SAAS;oBACb,EAAE,OAAO,OAAO;wBACZ,QAAQ,KAAK,CAAC,yBAAyB;oBAC3C;gBACJ;;YAEA;QACJ;oCAAG,EAAE;IAEL,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;qCAAE;YACN,IAAI,WAAW,IAAI,OAAO,IAAI;gBAC1B,iBAAiB,EAAE;gBACnB;YACJ;YAEA,MAAM,UAAU,MAAM,MAAM;qDAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAG3D,iBAAiB;QACrB;oCAAG;QAAC;QAAY;KAAM;IAGtB,qBACI,0JAAC;QAAI,WAAU;QAAoB,OAAO;YAAE,YAAY,mBAAmB,UAAU;YAAE,OAAO,mBAAmB,KAAK;QAAC;;0BAEnH,0JAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,UAAU,SAAS,YAAY,WAAW,UAAU,CAAC;0BACxF,cAAA,0JAAC;oBAAI,WAAU;;sCACX,0JAAC,wHAAA,CAAA,UAAI;4BAAC,WAAU;4BAAmC,OAAO;gCAAE,gBAAgB;4BAAO;4BAAG,MAAK;sCAAsB;;;;;;sCACjH,0JAAC;4BACG,WAAW,CAAC,6BAA6B,EAAE,UAAU,SAAS,iBAAiB,iBAAiB;4BAChG,MAAK;4BACL,SAAS,IAAM,iBAAiB,CAAC;sCAEjC,cAAA,0JAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5B,0JAAC;gBAAM,WAAW,CAAC,sBAAsB,EAAE,UAAU,SAAS,YAAY,WAAW,CAAC,EAAE,gBAAgB,SAAS,IAAI;;kCACjH,0JAAC;wBAAI,WAAU;kCACX,cAAA,0JAAC;4BAAG,WAAU;sCAAgD;;;;;;;;;;;kCAGlE,0JAAC;wBAAG,WAAU;;0CACV,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;;;;;;0CAGjD,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC;oCAAO,WAAU;oCAAgC,kBAAe;oCAAY,kBAAe;oCAAU,OAAO;wCAAE,gBAAgB;oCAAO;;sDAClI,0JAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;0CAG7C,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAA6B;;;;;;;;;;;;0CAGlD,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAA2B;;;;;;;;;;;;0CAGhD,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAAqB;;;;;;;;;;;;0CAG1C,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAA0B,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDACtH,0JAAC;4CAAE,WAAU;;;;;;wCAAqC;;;;;;;;;;;;0CAG1D,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,WAAU;oCAAkD,SAAS,IAAM,iBAAiB;oCAAQ,OAAO;wCAAE,gBAAgB;oCAAO;oCAAG,MAAK;;sDAC9I,0JAAC;;8DACG,0JAAC;oDAAE,WAAU;;;;;;gDAAuB;;;;;;;wCAEvC,eAAe,mBACZ,0JAAC;4CACG,OAAO;gDACH,iBAAiB;gDACjB,OAAO;gDACP,UAAU;gDACV,SAAS;gDACT,cAAc;gDACd,KAAK;gDACL,OAAO;4CACX;sDAEC;;;;;;;;;;;;;;;;;0CAIZ,0JAAC;;;;;0CACN,0JAAC;gCAAG,WAAU;0CACV,cAAA,0JAAC,wHAAA,CAAA,UAAI;oCAAC,MAAK;oCACP,WAAU;oCAAsC,OAAO;wCAAE,gBAAgB;oCAAO;oCAChF,MAAK;oCACL,SAAS,IAAM,iBAAiB;8CACnC;;;;;;;;;;;;;;;;;kCAIT,0JAAC;wBAAI,WAAU;;4BACV,wBACG,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,YAAY;oCAAY,gBAAgB;gCAAO;gCAAG,WAAU;;kDACzI,0JAAC;wCACG,WAAU;wCACV,OAAO;4CACH,OAAO;4CACP,QAAQ;4CACR,cAAc;wCAClB;;;;;;kDAEJ,0JAAC;;0DACG,0JAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;oDACd,cAAc;gDAClB;;;;;;0DAEJ,0JAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;qDAK1C,0JAAC,wHAAA,CAAA,UAAI;gCAAC,MAAK;gCAAqB,SAAS,IAAM,iBAAiB;gCAAQ,OAAO;oCAAE,gBAAgB;gCAAO;gCAAG,WAAU;;oCAChH,yBACG,0JAAC,yHAAA,CAAA,UAAK;wCAEF,KAAK,QAAQ,KAAK,IAAI;wCACtB,OAAO;wCACP,QAAQ;wCACR,KAAI;wCACJ,WAAU;uCALL,QAAQ,KAAK;;;;;kDAQ1B,0JAAC;;0DACG,0JAAC;gDAAK,WAAU;0DAAsC,QAAQ,IAAI,IAAI;;;;;;0DACtE,0JAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAKtD,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCACG,SAAS;wCACT,WAAU;kDAET,UAAU,SAAS,eAAe;;;;;;kDAEvC,0JAAC;wCACG,SAAS;wCACT,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,0JAAC;gBAAK,WAAU;gBAA0B,OAAO;oBAAE,YAAY,mBAAmB,cAAc;gBAAC;;kCAC7F,0JAAC;wBAAI,WAAU;;4BACV;4BAAS;;;;;;;oBAEb,6BACG,0JAAC;wBAAI,WAAU;kCACX,cAAA,0JAAC;4BAAI,WAAU;;8CACX,0JAAC;oCAAG,WAAU;8CAAc;;;;;;8CAC5B,0JAAC;oCAAE,WAAU;8CAAa;;;;;;8CAI1B,0JAAC;oCAAI,WAAU;;sDACX,0JAAC;4CAAO,WAAU;4CAA8B,SAAS;sDAAqB;;;;;;sDAG9E,0JAAC;4CAAO,WAAU;4CAA6B,SAAS,IAAM,eAAe;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOrG,0JAAC;wBAAI,WAAU;wBAA4B,OAAO;4BAAE,YAAY,mBAAmB,MAAM;4BAAE,OAAO,mBAAmB,KAAK;wBAAC;wBAAG,IAAG;;0CAC7H,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCAAG,WAAU;kDAAkB;;;;;;kDAChC,0JAAC;wCAAO,MAAK;wCAAS,WAAU;wCAAsB,mBAAgB;;;;;;;;;;;;0CAE1E,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCACG,MAAK;wCACL,MAAK;wCACL,IAAG;wCACH,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,OAAO;4CACH,iBAAiB;4CACjB,YAAY;4CACZ,KAAK;4CACL,QAAQ;wCACZ;wCACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wCAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;oCAG3D,wBACI,0JAAC;wCAAI,WAAU;;0DACX,0JAAC;gDACG,WAAU;gDACV,OAAO;oDACH,OAAO;oDACP,QAAQ;oDACR,cAAc;gDAClB;;;;;;0DAEJ,0JAAC;0DACG,cAAA,0JAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;wDACd,cAAc;oDAClB;;;;;;;;;;;;;;;;6DAKZ,0JAAC;;4CACI,cAAc,GAAG,CAAC,CAAA,qBACf,0JAAC;oDAEG,WAAU;oDACV,OAAO;wDACH,QAAQ;oDACZ;;sEAEA,0JAAC,yHAAA,CAAA,UAAK;4DACF,KAAK,KAAK,KAAK,IAAI,wRAAA,CAAA,UAAS;4DAC5B,KAAK,KAAK,IAAI;4DACd,OAAO;4DACP,QAAQ;4DACR,WAAU;4DACV,OAAO;gEAAE,WAAW;4DAAQ;;;;;;sEAEhC,0JAAC;;8EACG,0JAAC;8EAAQ,KAAK,QAAQ;;;;;;8EAAU,0JAAC;;;;;8EACjC,0JAAC;8EAAM,KAAK,IAAI;;;;;;;;;;;;;mDAhBf,KAAK,GAAG;;;;;4CAsBpB,cAAc,MAAM,KAAK,KAAK,4BAC3B,0JAAC;gDAAI,WAAU;;kEACX,0JAAC;wDACG,OAAM;wDACN,OAAM;wDACN,QAAO;wDACP,MAAK;wDACL,SAAQ;wDACR,OAAO;4DAAE,SAAS;wDAAI;kEAEtB,cAAA,0JAAC;4DAAK,GAAE;;;;;;;;;;;kEAEZ,0JAAC;wDAAE,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYhE;GAvdwB;;QAEgB,mHAAA,CAAA,WAAQ;QAC7B,8HAAA,CAAA,YAAS;QAwEP,8HAAA,CAAA,cAAW;;;KA3ER", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/socket.js"], "sourcesContent": ["import { io } from 'socket.io-client';\n\nclass SocketService {\n  constructor() {\n    this.socket = null;\n    this.isConnected = false;\n    this.userId = null;\n    this.messageCallbacks = [];\n    this.onlineStatusCallbacks = [];\n    this.typingCallbacks = [];\n  }\n\n  connect(userId) {\n    if (this.socket && this.isConnected) {\n      return;\n    }\n\n    this.userId = userId;\n\n    // Connect to the server - Smart URL detection\n    let serverUrl;\n\n    // Check if we're on localhost\n    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {\n      serverUrl = 'http://localhost:5000';\n    } else if (process.env.NEXT_PUBLIC_SERVER_URL) {\n      serverUrl = process.env.NEXT_PUBLIC_SERVER_URL;\n    } else {\n      // Default to production server\n      serverUrl = 'https://nextalk-u0y1.onrender.com';\n    }\n\n    console.log('🔗 Connecting to server:', serverUrl);\n    console.log('🌍 Environment:', process.env.NODE_ENV);\n    console.log('🏠 Hostname:', typeof window !== 'undefined' ? window.location.hostname : 'server');\n\n    try {\n      this.socket = io(serverUrl, {\n        transports: ['polling'], // Start with polling only to avoid WebSocket errors\n        withCredentials: true,\n        forceNew: false,\n        timeout: 20000,\n        autoConnect: true,\n        reconnection: true,\n        reconnectionAttempts: 5,\n        reconnectionDelay: 1000\n      });\n    } catch (error) {\n      console.error('❌ Socket connection error:', error);\n      // Fallback to basic connection\n      this.socket = io(serverUrl);\n    }\n\n    this.socket.on('connect', () => {\n      console.log('✅ Connected to server with socket ID:', this.socket.id);\n      this.isConnected = true;\n\n      // Join with user ID\n      if (this.userId) {\n        console.log('🔗 Joining with user ID:', this.userId);\n        this.socket.emit('join', this.userId);\n      }\n    });\n\n    this.socket.on('connect_error', (error) => {\n      console.error('❌ Connection error:', error);\n      console.error('🚨 Failed to connect to:', serverUrl);\n\n      // Check if it's a localhost connection issue\n      if (serverUrl.includes('localhost')) {\n        console.error('🔧 Make sure your backend server is running on http://localhost:5000');\n        console.error('💡 Try running: npm start or node server.js in your backend directory');\n      }\n\n      // Try to reconnect with different transport\n      if (error.message.includes('websocket')) {\n        console.log('🔄 Retrying with polling transport...');\n        this.socket.io.opts.transports = ['polling'];\n      }\n    });\n\n    this.socket.on('reconnect', (attemptNumber) => {\n      console.log('🔄 Reconnected after', attemptNumber, 'attempts');\n    });\n\n    this.socket.on('reconnect_error', (error) => {\n      console.error('❌ Reconnection error:', error);\n    });\n\n    this.socket.on('disconnect', () => {\n      console.log('Disconnected from server');\n      this.isConnected = false;\n    });\n\n    // Handle incoming messages\n    this.socket.on('receiveMessage', (data) => {\n      console.log('📨 Socket received message:', data);\n      this.messageCallbacks.forEach(callback => {\n        console.log('🔄 Calling message callback');\n        callback(data);\n      });\n    });\n\n    // Handle message delivery confirmation\n    this.socket.on('messageDelivered', (data) => {\n      console.log('✅ Message delivered confirmation:', data);\n    });\n\n    // Handle message errors\n    this.socket.on('messageError', (data) => {\n      console.error('Message error:', data);\n    });\n\n    // Handle online users list\n    this.socket.on('onlineUsers', (userIds) => {\n      this.onlineStatusCallbacks.forEach(callback => \n        callback({ type: 'initial', userIds })\n      );\n    });\n\n    // Handle user coming online\n    this.socket.on('userOnline', (userId) => {\n      this.onlineStatusCallbacks.forEach(callback => \n        callback({ type: 'online', userId })\n      );\n    });\n\n    // Handle user going offline\n    this.socket.on('userOffline', (userId) => {\n      this.onlineStatusCallbacks.forEach(callback => \n        callback({ type: 'offline', userId })\n      );\n    });\n\n    // Handle typing indicators\n    this.socket.on('userTyping', (data) => {\n      this.typingCallbacks.forEach(callback => callback(data));\n    });\n\n    // Handle message read status\n    this.socket.on('messageRead', (data) => {\n      console.log('Message read:', data);\n    });\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect();\n      this.socket = null;\n      this.isConnected = false;\n      this.userId = null;\n    }\n  }\n\n  sendMessage(receiverId, message, messageType = 'text') {\n    if (!this.socket || !this.isConnected) {\n      console.error('❌ Socket not connected');\n      return false;\n    }\n\n    if (!this.userId || !receiverId || !message) {\n      console.error('❌ Missing required data:', { userId: this.userId, receiverId, message });\n      return false;\n    }\n\n    const messageData = {\n      senderId: this.userId,\n      receiverId,\n      message,\n      messageType\n    };\n\n    console.log('📤 Sending message:', messageData);\n    this.socket.emit('sendMessage', messageData);\n\n    return true;\n  }\n\n  sendTypingIndicator(receiverId, isTyping) {\n    if (!this.socket || !this.isConnected) {\n      return;\n    }\n\n    this.socket.emit('typing', {\n      senderId: this.userId,\n      receiverId,\n      isTyping\n    });\n  }\n\n  markAsRead(chatId, messageId) {\n    if (!this.socket || !this.isConnected) {\n      return;\n    }\n\n    this.socket.emit('markAsRead', {\n      chatId,\n      messageId,\n      userId: this.userId\n    });\n  }\n\n  // Callback management\n  onMessage(callback) {\n    this.messageCallbacks.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      this.messageCallbacks = this.messageCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  onOnlineStatus(callback) {\n    this.onlineStatusCallbacks.push(callback);\n    \n    return () => {\n      this.onlineStatusCallbacks = this.onlineStatusCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  onTyping(callback) {\n    this.typingCallbacks.push(callback);\n    \n    return () => {\n      this.typingCallbacks = this.typingCallbacks.filter(cb => cb !== callback);\n    };\n  }\n\n  // Get connection status\n  getConnectionStatus() {\n    return {\n      isConnected: this.isConnected,\n      userId: this.userId,\n      socketId: this.socket?.id\n    };\n  }\n}\n\n// Create singleton instance\nconst socketService = new SocketService();\n\nexport default socketService;\n"], "names": [], "mappings": ";;;AAyBe;AAzBf;AAAA;;AAEA,MAAM;IACJ,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAAC,qBAAqB,GAAG,EAAE;QAC/B,IAAI,CAAC,eAAe,GAAG,EAAE;IAC3B;IAEA,QAAQ,MAAM,EAAE;QACd,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;YACnC;QACF;QAEA,IAAI,CAAC,MAAM,GAAG;QAEd,8CAA8C;QAC9C,IAAI;QAEJ,8BAA8B;QAC9B,IAAI,aAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,aAAa;YAC7E,YAAY;QACd,OAAO,IAAI,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE;YAC7C,YAAY,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;QAChD,OAAO;YACL,+BAA+B;YAC/B,YAAY;QACd;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gBAAgB,uCAAgC,OAAO,QAAQ,CAAC,QAAQ;QAEpF,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,2KAAA,CAAA,KAAE,AAAD,EAAE,WAAW;gBAC1B,YAAY;oBAAC;iBAAU;gBACvB,iBAAiB;gBACjB,UAAU;gBACV,SAAS;gBACT,aAAa;gBACb,cAAc;gBACd,sBAAsB;gBACtB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,+BAA+B;YAC/B,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,2KAAA,CAAA,KAAE,AAAD,EAAE;QACnB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,GAAG,CAAC,yCAAyC,IAAI,CAAC,MAAM,CAAC,EAAE;YACnE,IAAI,CAAC,WAAW,GAAG;YAEnB,oBAAoB;YACpB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,QAAQ,GAAG,CAAC,4BAA4B,IAAI,CAAC,MAAM;gBACnD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM;YACtC;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC/B,QAAQ,KAAK,CAAC,uBAAuB;YACrC,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,6CAA6C;YAC7C,IAAI,UAAU,QAAQ,CAAC,cAAc;gBACnC,QAAQ,KAAK,CAAC;gBACd,QAAQ,KAAK,CAAC;YAChB;YAEA,4CAA4C;YAC5C,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;gBACvC,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG;oBAAC;iBAAU;YAC9C;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC;YAC3B,QAAQ,GAAG,CAAC,wBAAwB,eAAe;QACrD;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,mBAAmB,CAAC;YACjC,QAAQ,KAAK,CAAC,yBAAyB;QACzC;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc;YAC3B,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG;QACrB;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,kBAAkB,CAAC;YAChC,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;gBAC5B,QAAQ,GAAG,CAAC;gBACZ,SAAS;YACX;QACF;QAEA,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,oBAAoB,CAAC;YAClC,QAAQ,GAAG,CAAC,qCAAqC;QACnD;QAEA,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC;YAC9B,QAAQ,KAAK,CAAC,kBAAkB;QAClC;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,WACjC,SAAS;oBAAE,MAAM;oBAAW;gBAAQ;QAExC;QAEA,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,WACjC,SAAS;oBAAE,MAAM;oBAAU;gBAAO;QAEtC;QAEA,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YAC7B,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA,WACjC,SAAS;oBAAE,MAAM;oBAAW;gBAAO;QAEvC;QAEA,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS;QACpD;QAEA,6BAA6B;QAC7B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,CAAC;YAC7B,QAAQ,GAAG,CAAC,iBAAiB;QAC/B;IACF;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,MAAM,GAAG;QAChB;IACF;IAEA,YAAY,UAAU,EAAE,OAAO,EAAE,cAAc,MAAM,EAAE;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS;YAC3C,QAAQ,KAAK,CAAC,4BAA4B;gBAAE,QAAQ,IAAI,CAAC,MAAM;gBAAE;gBAAY;YAAQ;YACrF,OAAO;QACT;QAEA,MAAM,cAAc;YAClB,UAAU,IAAI,CAAC,MAAM;YACrB;YACA;YACA;QACF;QAEA,QAAQ,GAAG,CAAC,uBAAuB;QACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe;QAEhC,OAAO;IACT;IAEA,oBAAoB,UAAU,EAAE,QAAQ,EAAE;QACxC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;YACzB,UAAU,IAAI,CAAC,MAAM;YACrB;YACA;QACF;IACF;IAEA,WAAW,MAAM,EAAE,SAAS,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrC;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;YAC7B;YACA;YACA,QAAQ,IAAI,CAAC,MAAM;QACrB;IACF;IAEA,sBAAsB;IACtB,UAAU,QAAQ,EAAE;QAClB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;QAE3B,8BAA8B;QAC9B,OAAO;YACL,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;QACpE;IACF;IAEA,eAAe,QAAQ,EAAE;QACvB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAEhC,OAAO;YACL,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;QAC9E;IACF;IAEA,SAAS,QAAQ,EAAE;QACjB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;QAE1B,OAAO;YACL,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;QAClE;IACF;IAEA,wBAAwB;IACxB,sBAAsB;QACpB,OAAO;YACL,aAAa,IAAI,CAAC,WAAW;YAC7B,QAAQ,IAAI,CAAC,MAAM;YACnB,UAAU,IAAI,CAAC,MAAM,EAAE;QACzB;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB,IAAI;uCAEX", "debugId": null}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/serverCheck.js"], "sourcesContent": ["// Server connectivity check utility\nexport const checkServerConnection = async (serverUrl) => {\n    try {\n        console.log('🔍 Checking server connection to:', serverUrl);\n        \n        const response = await fetch(serverUrl + '/health', {\n            method: 'GET',\n            mode: 'cors',\n            credentials: 'include',\n            headers: {\n                'Content-Type': 'application/json',\n            },\n        });\n        \n        if (response.ok) {\n            console.log('✅ Server is reachable');\n            return true;\n        } else {\n            console.warn('⚠️ Server responded with status:', response.status);\n            return false;\n        }\n    } catch (error) {\n        console.error('❌ Server connection failed:', error.message);\n        \n        if (serverUrl.includes('localhost')) {\n            console.error('🚨 Backend server is not running on localhost:5000');\n            console.error('💡 Please start your backend server:');\n            console.error('   1. Navigate to your backend directory');\n            console.error('   2. Run: npm start or node server.js');\n            console.error('   3. Make sure it\\'s running on port 5000');\n        }\n        \n        return false;\n    }\n};\n\nexport const getServerUrl = () => {\n    // Check if we're on localhost (client-side)\n    if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {\n        return 'http://localhost:5000';\n    } else if (process.env.NEXT_PUBLIC_SERVER_URL) {\n        return process.env.NEXT_PUBLIC_SERVER_URL;\n    } else if (process.env.REACT_APP_API_URL) {\n        return process.env.REACT_APP_API_URL;\n    } else {\n        // Default to production server\n        return 'https://nextalk-u0y1.onrender.com';\n    }\n};\n\nexport const debugServerConnection = async () => {\n    const serverUrl = getServerUrl();\n    console.log('🌐 Current environment:', process.env.NODE_ENV);\n    console.log('🏠 Current hostname:', typeof window !== 'undefined' ? window.location.hostname : 'server');\n    console.log('🔗 Server URL:', serverUrl);\n    \n    const isConnected = await checkServerConnection(serverUrl);\n    \n    if (!isConnected && serverUrl.includes('localhost')) {\n        console.log('📋 Localhost troubleshooting checklist:');\n        console.log('   ✓ Is your backend server running?');\n        console.log('   ✓ Is it running on port 5000?');\n        console.log('   ✓ Can you access http://localhost:5000 in browser?');\n        console.log('   ✓ Are there any CORS issues?');\n        console.log('   ✓ Check backend console for errors');\n    }\n    \n    return isConnected;\n};\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;AAwCrB;AAvCR,MAAM,wBAAwB,OAAO;IACxC,IAAI;QACA,QAAQ,GAAG,CAAC,qCAAqC;QAEjD,MAAM,WAAW,MAAM,MAAM,YAAY,WAAW;YAChD,QAAQ;YACR,MAAM;YACN,aAAa;YACb,SAAS;gBACL,gBAAgB;YACpB;QACJ;QAEA,IAAI,SAAS,EAAE,EAAE;YACb,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,OAAO;YACH,QAAQ,IAAI,CAAC,oCAAoC,SAAS,MAAM;YAChE,OAAO;QACX;IACJ,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,+BAA+B,MAAM,OAAO;QAE1D,IAAI,UAAU,QAAQ,CAAC,cAAc;YACjC,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC;QAClB;QAEA,OAAO;IACX;AACJ;AAEO,MAAM,eAAe;IACxB,4CAA4C;IAC5C,IAAI,aAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,aAAa;QAC3E,OAAO;IACX,OAAO,IAAI,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE;QAC3C,OAAO,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;IAC7C,OAAO,IAAI,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;QACtC,OAAO,yJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,iBAAiB;IACxC,OAAO;QACH,+BAA+B;QAC/B,OAAO;IACX;AACJ;AAEO,MAAM,wBAAwB;IACjC,MAAM,YAAY;IAClB,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,wBAAwB,uCAAgC,OAAO,QAAQ,CAAC,QAAQ;IAC5F,QAAQ,GAAG,CAAC,kBAAkB;IAE9B,MAAM,cAAc,MAAM,sBAAsB;IAEhD,IAAI,CAAC,eAAe,UAAU,QAAQ,CAAC,cAAc;QACjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IAChB;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Dashboard/Messages.js"], "sourcesContent": ["import { useState, useRef, useEffect, useCallback } from \"react\";\r\nimport axios from '../../utils/axiosConfig';\r\nimport Image from \"next/image\";\r\nimport predefine from \"../../public/Images/predefine.webp\";\r\nimport DashboardLayout from '../Components/DashboardLayout';\r\nimport { useRouter } from 'next/router';\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport socketService from '../../utils/socket';\r\nimport { debugServerConnection } from '../../utils/serverCheck';\r\n\r\nexport default function Messages() {\r\n    const [users, setUsers] = useState([]);\r\n    const [sessionUser, setSessionUser] = useState(null);\r\n    const [following, setFollowing] = useState(new Set());\r\n    const [accepted, setAccepted] = useState(new Set());\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [selectedChat, setSelectedChat] = useState(null);\r\n    const [searchTerm, setSearchTerm] = useState('');\r\n    const [displayCount, setDisplayCount] = useState(6);\r\n    const [visibleUsers, setVisibleUsers] = useState([]);\r\n    const [filteredFollowers, setFilteredFollowers] = useState([]);\r\n    const [profile, setProfile] = useState({\r\n        username: 'user123',\r\n        name: 'John Doe',\r\n        email: '<EMAIL>',\r\n        bio: 'No bio yet.',\r\n        avatar: predefine,\r\n        posts: 15,\r\n        followersCount: 250,\r\n        followingCount: 180,\r\n    });\r\n    const { theme } = useTheme();\r\n    const [showOnlineOnly, setShowOnlineOnly] = useState(false);\r\n    const router = useRouter();\r\n\r\n    // Mock last message data and reactions\r\n    const [lastMessages, setLastMessages] = useState({});\r\n    const [reactions, setReactions] = useState({});\r\n    const [messages, setMessages] = useState([\r\n        { id: 1, sender: \"You\", text: \"Hey, how's it going?\", timestamp: \"10:30 AM\" },\r\n        { id: 2, sender: \"Friend\", text: \"Pretty good, thanks! You?\", timestamp: \"10:32 AM\" },\r\n        { id: 3, sender: \"You\", text: \"Hey, how's it going?\", timestamp: \"10:42 AM\" },\r\n        { id: 4, sender: \"Friend\", text: \"Pretty good, thanks! You?\", timestamp: \"10:52 AM\" },\r\n    ]);\r\n    const [newMessage, setNewMessage] = useState(\"\");\r\n    const messagesEndRef = useRef(null);\r\n    const [sessionUserId, setSessionUserId] = useState(null);\r\n    const [selectedUser, setSelectedUser] = useState(null);\r\n    const [chatMessages, setChatMessages] = useState({});\r\n    const [onlineUsers, setOnlineUsers] = useState(new Set());\r\n    const [typingUsers, setTypingUsers] = useState(new Set());\r\n    const [isTyping, setIsTyping] = useState(false);\r\n    const [unreadCounts, setUnreadCounts] = useState({});\r\n    const [deliveredMessages, setDeliveredMessages] = useState(new Set());\r\n    const [showMobileModal, setShowMobileModal] = useState(false);\r\n    const [chatSizes, setChatSizes] = useState({});\r\n    const [deletedChats, setDeletedChats] = useState(new Set());\r\n    const [userLastSeen, setUserLastSeen] = useState({});\r\n    const [lastSeenUpdateInterval, setLastSeenUpdateInterval] = useState(null);\r\n    const [messageReactions, setMessageReactions] = useState({});\r\n    const [showMobileReactionPopup, setShowMobileReactionPopup] = useState(false);\r\n    const [selectedMessageForReaction, setSelectedMessageForReaction] = useState(null);\r\n    const [reactionPopupPosition, setReactionPopupPosition] = useState({ x: 0, y: 0 });\r\n    const [showScrollToBottom, setShowScrollToBottom] = useState(false);\r\n    const typingTimeoutRef = useRef(null);\r\n    const chatContainerRef = useRef(null);\r\n\r\n    // Chat size and deletion functions (using local storage)\r\n\r\n    const deleteChat = (chatId, userId1, userId2) => {\r\n        try {\r\n            // Mark chat as deleted\r\n            setDeletedChats(prev => new Set([...prev, `${userId1}_${userId2}`]));\r\n\r\n            // Clear local messages\r\n            setChatMessages(prev => ({\r\n                ...prev,\r\n                [`${userId1}_${userId2}`]: []\r\n            }));\r\n\r\n            // Clear local storage\r\n            localStorage.removeItem(`chat_${userId1}_${userId2}`);\r\n            localStorage.removeItem(`unread_${userId1}_${userId2}`);\r\n            localStorage.removeItem(`lastmsg_${userId1}_${userId2}`);\r\n\r\n            // Reset chat size\r\n            setChatSizes(prev => ({\r\n                ...prev,\r\n                [`${userId1}_${userId2}`]: {\r\n                    sizeInBytes: 0,\r\n                    sizeFormatted: '0 Bytes',\r\n                    exceedsLimit: false,\r\n                    chatId: `local_${userId1}_${userId2}`\r\n                }\r\n            }));\r\n\r\n            console.log('✅ Chat deleted successfully');\r\n            return true;\r\n        } catch (error) {\r\n            console.error('❌ Error deleting chat:', error);\r\n            return false;\r\n        }\r\n    };\r\n\r\n    const restoreChat = (chatId, userId1, userId2) => {\r\n        try {\r\n            // Remove from deleted chats\r\n            setDeletedChats(prev => {\r\n                const newSet = new Set(prev);\r\n                newSet.delete(`${userId1}_${userId2}`);\r\n                return newSet;\r\n            });\r\n\r\n            console.log('✅ Chat restored successfully');\r\n            return true;\r\n        } catch (error) {\r\n            console.error('❌ Error restoring chat:', error);\r\n            return false;\r\n        }\r\n    };\r\n\r\n\r\n\r\n    // Helper function to format bytes\r\n    const formatBytes = (bytes) => {\r\n        if (bytes === 0) return '0 Bytes';\r\n        const k = 1024;\r\n        const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    };\r\n\r\n    // Format last seen time (12-hour format) - Real-time updates\r\n    const formatLastSeen = (lastSeen, isOnline) => {\r\n        if (isOnline) return 'Online';\r\n\r\n        const now = new Date();\r\n        const lastSeenDate = new Date(lastSeen);\r\n        const diffInMinutes = Math.floor((now - lastSeenDate) / (1000 * 60));\r\n\r\n        if (diffInMinutes < 1) return 'Just now';\r\n        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\r\n\r\n        const diffInHours = Math.floor(diffInMinutes / 60);\r\n        if (diffInHours < 24) return `${diffInHours}h ago`;\r\n\r\n        // Format as 12-hour time with AM/PM\r\n        const options = {\r\n            hour: 'numeric',\r\n            minute: 'numeric',\r\n            hour12: true\r\n        };\r\n\r\n        const diffInDays = Math.floor(diffInHours / 24);\r\n        if (diffInDays === 1) {\r\n            return `Yesterday ${lastSeenDate.toLocaleTimeString('en-US', options)}`;\r\n        } else if (diffInDays < 7) {\r\n            return `${diffInDays}d ago`;\r\n        } else {\r\n            return lastSeenDate.toLocaleDateString('en-US', {\r\n                month: 'short',\r\n                day: 'numeric',\r\n                hour: 'numeric',\r\n                minute: 'numeric',\r\n                hour12: true\r\n            });\r\n        }\r\n    };\r\n\r\n    // Update last seen times in real-time\r\n    const updateLastSeenTimes = () => {\r\n        setUsers(prevUsers =>\r\n            prevUsers.map(user => ({\r\n                ...user,\r\n                lastSeenFormatted: formatLastSeen(user.lastSeen, onlineUsers.has(user._id))\r\n            }))\r\n        );\r\n    };\r\n\r\n    // Message Reactions Feature - One reaction per user\r\n    const addReaction = (messageId, emoji) => {\r\n        const currentUserId = sessionUserId;\r\n\r\n        setMessageReactions(prev => {\r\n            const messageReactions = prev[messageId] || {};\r\n            const newReactions = { ...messageReactions };\r\n\r\n            // Remove user's previous reaction if exists\r\n            Object.keys(newReactions).forEach(existingEmoji => {\r\n                if (newReactions[existingEmoji] && newReactions[existingEmoji][currentUserId]) {\r\n                    delete newReactions[existingEmoji][currentUserId];\r\n                    // Remove emoji if no users left\r\n                    if (Object.keys(newReactions[existingEmoji]).length === 0) {\r\n                        delete newReactions[existingEmoji];\r\n                    }\r\n                }\r\n            });\r\n\r\n            // Add new reaction\r\n            if (!newReactions[emoji]) {\r\n                newReactions[emoji] = {};\r\n            }\r\n            newReactions[emoji][currentUserId] = {\r\n                userId: currentUserId,\r\n                userName: sessionUser?.name || 'You',\r\n                timestamp: new Date().toISOString()\r\n            };\r\n\r\n            return {\r\n                ...prev,\r\n                [messageId]: newReactions\r\n            };\r\n        });\r\n\r\n        // Save to localStorage\r\n        const reactionsKey = `reactions_${sessionUserId}_${selectedUser?._id}`;\r\n        setTimeout(() => {\r\n            const updatedReactions = messageReactions;\r\n            localStorage.setItem(reactionsKey, JSON.stringify(updatedReactions));\r\n        }, 100);\r\n\r\n        // Close mobile popup after reaction\r\n        setShowMobileReactionPopup(false);\r\n        setSelectedMessageForReaction(null);\r\n    };\r\n\r\n    // Mobile reaction popup handler\r\n    const handleMobileReactionPopup = (messageId, element) => {\r\n        const rect = element.getBoundingClientRect();\r\n        setReactionPopupPosition({\r\n            x: rect.left + rect.width / 2,\r\n            y: rect.top - 60\r\n        });\r\n        setSelectedMessageForReaction(messageId);\r\n        setShowMobileReactionPopup(true);\r\n\r\n        // Vibrate if supported\r\n        if (navigator.vibrate) {\r\n            navigator.vibrate(50);\r\n        }\r\n    };\r\n\r\n    // Scroll functions\r\n    const scrollToBottom = () => {\r\n        if (chatContainerRef.current) {\r\n            chatContainerRef.current.scrollTo({\r\n                top: chatContainerRef.current.scrollHeight,\r\n                behavior: 'smooth'\r\n            });\r\n        }\r\n    };\r\n\r\n    const handleScroll = () => {\r\n        if (chatContainerRef.current) {\r\n            const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;\r\n            const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;\r\n            setShowScrollToBottom(!isNearBottom);\r\n        }\r\n    };\r\n\r\n    // Auto-scroll when new message is sent or received\r\n    useEffect(() => {\r\n        scrollToBottom();\r\n    }, [chatMessages, selectedChat]);\r\n\r\n\r\n\r\n    // Enhanced Local Storage utility functions\r\n    const getChatHistory = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return [];\r\n            const chatKey = `chat_${sessionUserId}_${userId}`;\r\n            const stored = localStorage.getItem(chatKey);\r\n            return stored ? JSON.parse(stored) : [];\r\n        } catch (error) {\r\n            console.error('Error loading chat history:', error);\r\n            return [];\r\n        }\r\n    };\r\n\r\n    const saveChatHistory = (userId, messages) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const chatKey = `chat_${sessionUserId}_${userId}`;\r\n            localStorage.setItem(chatKey, JSON.stringify(messages));\r\n            console.log('💾 Chat history saved for user:', userId);\r\n        } catch (error) {\r\n            console.error('Error saving chat history:', error);\r\n        }\r\n    };\r\n\r\n    const getUnreadCount = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return 0;\r\n            const unreadKey = `unread_${sessionUserId}_${userId}`;\r\n            const stored = localStorage.getItem(unreadKey);\r\n            return stored ? parseInt(stored) : 0;\r\n        } catch (error) {\r\n            console.error('Error loading unread count:', error);\r\n            return 0;\r\n        }\r\n    };\r\n\r\n    const saveUnreadCount = (userId, count) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const unreadKey = `unread_${sessionUserId}_${userId}`;\r\n            localStorage.setItem(unreadKey, count.toString());\r\n            setUnreadCounts(prev => ({\r\n                ...prev,\r\n                [userId]: count\r\n            }));\r\n        } catch (error) {\r\n            console.error('Error saving unread count:', error);\r\n        }\r\n    };\r\n\r\n    const getLastMessage = (userId) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return '';\r\n            const lastMsgKey = `lastmsg_${sessionUserId}_${userId}`;\r\n            return localStorage.getItem(lastMsgKey) || '';\r\n        } catch (error) {\r\n            console.error('Error loading last message:', error);\r\n            return '';\r\n        }\r\n    };\r\n\r\n    const saveLastMessage = (userId, message) => {\r\n        try {\r\n            if (!sessionUserId || !userId) return;\r\n            const lastMsgKey = `lastmsg_${sessionUserId}_${userId}`;\r\n            const truncatedMessage = message.length > 30 ? message.substring(0, 30) + '...' : message;\r\n            localStorage.setItem(lastMsgKey, truncatedMessage);\r\n            setLastMessages(prev => ({\r\n                ...prev,\r\n                [userId]: truncatedMessage\r\n            }));\r\n        } catch (error) {\r\n            console.error('Error saving last message:', error);\r\n        }\r\n    };\r\n\r\n    // Handle back to chat list on mobile\r\n    const handleBackToList = () => {\r\n        console.log('🔙 Going back to chat list');\r\n        setSelectedChat(null);\r\n        setSelectedUser(null);\r\n        setShowMobileModal(false);\r\n        // Update URL only on desktop\r\n        if (window.innerWidth >= 1024) {\r\n            router.replace('/Dashboard/Messages', undefined, { shallow: true });\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        const fetchData = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem('user'));\r\n            const sessionId = storedUser?.user?.id;\r\n\r\n            if (!sessionId) {\r\n                setError('No session found.');\r\n                setLoading(false);\r\n                return;\r\n            }\r\n\r\n            try {\r\n                // Check server connection first\r\n                console.log('🔍 Checking server connection...');\r\n                await debugServerConnection();\r\n\r\n                // Fetch all users (using existing endpoint)\r\n                console.log('📡 Fetching users...');\r\n                const response = await axios.post('/displayusersProfile');\r\n\r\n                const personally = response.data;\r\n                const filteredUsers = personally.filter(user => user._id !== sessionId);\r\n                const sessionUser = personally.find(user => user._id === sessionId);\r\n                setSessionUser(sessionUser);\r\n\r\n                // Fetch follow status\r\n                const followRes = await axios.get(`/follow-status/${sessionId}`);\r\n                const followData = followRes.data;\r\n                setFollowing(new Set(followData.following));\r\n                setAccepted(new Set(followData.accepted));\r\n\r\n                // Mock last messages and reactions\r\n                const mockLastMessages = filteredUsers.reduce((acc, user) => ({\r\n                    ...acc,\r\n                    [user._id]: user.lastMessage || `Hey, what's up? ${new Date().toLocaleTimeString()}`,\r\n                }), {});\r\n                const mockReactions = filteredUsers.reduce((acc, user) => ({\r\n                    ...acc,\r\n                    [user._id]: user.reaction || (Math.random() > 0.5 ? '❤️' : null),\r\n                }), {});\r\n                setLastMessages(mockLastMessages);\r\n                setReactions(mockReactions);\r\n\r\n                setUsers(filteredUsers);\r\n                setLoading(false);\r\n            } catch (err) {\r\n                console.error('❌ Error fetching data:', err);\r\n\r\n                if (err.message === 'Network Error' || err.code === 'ECONNREFUSED') {\r\n                    setError('Cannot connect to server. Please make sure your backend is running on http://localhost:5000');\r\n                    console.error('🚨 Backend server connection failed!');\r\n                    console.error('💡 Make sure to start your backend server:');\r\n                    console.error('   1. Navigate to your backend directory');\r\n                    console.error('   2. Run: npm start or node server.js');\r\n                    console.error('   3. Verify it\\'s running on port 5000');\r\n                } else {\r\n                    setError('Failed to load data: ' + err.message);\r\n                }\r\n\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchData();\r\n    }, []);\r\n\r\n    // Real-time last seen updates\r\n    useEffect(() => {\r\n        // Update last seen times every minute\r\n        const interval = setInterval(() => {\r\n            updateLastSeenTimes();\r\n        }, 60000); // Update every 1 minute\r\n\r\n        setLastSeenUpdateInterval(interval);\r\n\r\n        return () => {\r\n            if (interval) clearInterval(interval);\r\n        };\r\n    }, [users, onlineUsers]);\r\n\r\n    // Load chat history and unread counts when users are loaded\r\n    useEffect(() => {\r\n        if (users.length > 0 && sessionUserId) {\r\n            console.log('📚 Loading chat history for all users...');\r\n            const loadedChats = {};\r\n            const loadedUnreadCounts = {};\r\n            const loadedLastMessages = {};\r\n\r\n            users.forEach(user => {\r\n                if (accepted.has(user._id)) {\r\n                    // Load chat history\r\n                    const history = getChatHistory(user._id);\r\n                    if (history.length > 0) {\r\n                        loadedChats[user._id] = history;\r\n                        console.log(`📖 Loaded ${history.length} messages for user:`, user.name);\r\n                    }\r\n\r\n                    // Load unread count\r\n                    const unreadCount = getUnreadCount(user._id);\r\n                    if (unreadCount > 0) {\r\n                        loadedUnreadCounts[user._id] = unreadCount;\r\n                    }\r\n\r\n                    // Load last message\r\n                    const lastMsg = getLastMessage(user._id);\r\n                    if (lastMsg) {\r\n                        loadedLastMessages[user._id] = lastMsg;\r\n                    }\r\n\r\n                    // Calculate chat size from loaded history\r\n                    if (history.length > 0) {\r\n                        const totalSize = history.reduce((size, msg) => {\r\n                            return size + (msg.text ? msg.text.length * 2 : 0) + 200;\r\n                        }, 0);\r\n\r\n                        setChatSizes(prev => ({\r\n                            ...prev,\r\n                            [`${sessionUserId}_${user._id}`]: {\r\n                                sizeInBytes: totalSize,\r\n                                sizeFormatted: formatBytes(totalSize),\r\n                                exceedsLimit: totalSize >= (10 * 1024 * 1024),\r\n                                chatId: `local_${sessionUserId}_${user._id}`\r\n                            }\r\n                        }));\r\n                    }\r\n\r\n                    // Load reactions from localStorage\r\n                    const reactionsKey = `reactions_${sessionUserId}_${user._id}`;\r\n                    const savedReactions = localStorage.getItem(reactionsKey);\r\n                    if (savedReactions) {\r\n                        try {\r\n                            const reactions = JSON.parse(savedReactions);\r\n                            setMessageReactions(prev => ({\r\n                                ...prev,\r\n                                ...reactions\r\n                            }));\r\n                        } catch (error) {\r\n                            console.error('Error loading reactions:', error);\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n\r\n            setChatMessages(loadedChats);\r\n            setUnreadCounts(loadedUnreadCounts);\r\n            setLastMessages(loadedLastMessages);\r\n            console.log('✅ All chat data loaded from cache');\r\n        }\r\n    }, [users, sessionUserId, accepted]);\r\n\r\n    // Socket.IO connection and real-time functionality\r\n    useEffect(() => {\r\n        if (sessionUserId) {\r\n            // Connect to Socket.IO\r\n            socketService.connect(sessionUserId);\r\n\r\n            // Listen for incoming messages\r\n            const unsubscribeMessages = socketService.onMessage((data) => {\r\n                console.log('📨 Received message:', data);\r\n                console.log('🔍 Current sessionUserId:', sessionUserId);\r\n                console.log('🔍 Current selectedChat:', selectedChat);\r\n                const { senderId, receiverId, message, timestamp, messageId } = data;\r\n\r\n                // Add message to chat if it's for current user or current chat\r\n                if (receiverId === sessionUserId || senderId === sessionUserId) {\r\n                    console.log('✅ Message is for current user, adding to chat');\r\n                    const chatPartnerId = receiverId === sessionUserId ? senderId : receiverId;\r\n                    const isIncomingMessage = receiverId === sessionUserId;\r\n\r\n                    const newMessage = {\r\n                        id: messageId,\r\n                        sender: senderId === sessionUserId ? \"You\" : \"Friend\",\r\n                        text: message,\r\n                        timestamp: new Date(timestamp).toLocaleTimeString([], {\r\n                            hour: '2-digit',\r\n                            minute: '2-digit',\r\n                            hour12: true\r\n                        }),\r\n                        delivered: true,\r\n                        read: false\r\n                    };\r\n\r\n                    // Update chat messages\r\n                    setChatMessages(prev => {\r\n                        const updatedMessages = [...(prev[chatPartnerId] || []), newMessage];\r\n                        // Save to local storage\r\n                        saveChatHistory(chatPartnerId, updatedMessages);\r\n                        return {\r\n                            ...prev,\r\n                            [chatPartnerId]: updatedMessages\r\n                        };\r\n                    });\r\n\r\n                    // Update last message\r\n                    saveLastMessage(chatPartnerId, message);\r\n\r\n                    // Handle unread count for incoming messages\r\n                    if (isIncomingMessage) {\r\n                        const currentUnread = getUnreadCount(chatPartnerId);\r\n                        const newUnreadCount = currentUnread + 1;\r\n                        saveUnreadCount(chatPartnerId, newUnreadCount);\r\n                        console.log(`📬 Unread count for ${chatPartnerId}: ${newUnreadCount}`);\r\n                    }\r\n                }\r\n            });\r\n\r\n            // Listen for online status updates\r\n            const unsubscribeOnlineStatus = socketService.onOnlineStatus((data) => {\r\n                console.log('👥 Online status update:', data);\r\n                if (data.type === 'initial') {\r\n                    console.log('📋 Setting initial online users:', data.userIds);\r\n                    setOnlineUsers(new Set(data.userIds));\r\n                } else if (data.type === 'online') {\r\n                    console.log('🟢 User came online:', data.userId);\r\n                    setOnlineUsers(prev => new Set([...prev, data.userId]));\r\n                } else if (data.type === 'offline') {\r\n                    console.log('🔴 User went offline:', data.userId);\r\n                    setOnlineUsers(prev => {\r\n                        const newSet = new Set(prev);\r\n                        newSet.delete(data.userId);\r\n                        return newSet;\r\n                    });\r\n                }\r\n            });\r\n\r\n            // Listen for typing indicators\r\n            const unsubscribeTyping = socketService.onTyping((data) => {\r\n                console.log('⌨️ Typing indicator:', data);\r\n                const { userId, isTyping } = data;\r\n                setTypingUsers(prev => {\r\n                    const newSet = new Set(prev);\r\n                    if (isTyping) {\r\n                        console.log('⌨️ User started typing:', userId);\r\n                        newSet.add(userId);\r\n                    } else {\r\n                        console.log('⌨️ User stopped typing:', userId);\r\n                        newSet.delete(userId);\r\n                    }\r\n                    return newSet;\r\n                });\r\n\r\n                // Clear typing indicator after 3 seconds\r\n                if (isTyping) {\r\n                    setTimeout(() => {\r\n                        setTypingUsers(prev => {\r\n                            const newSet = new Set(prev);\r\n                            newSet.delete(userId);\r\n                            return newSet;\r\n                        });\r\n                    }, 3000);\r\n                }\r\n            });\r\n\r\n            // Cleanup on unmount\r\n            return () => {\r\n                unsubscribeMessages();\r\n                unsubscribeOnlineStatus();\r\n                unsubscribeTyping();\r\n                socketService.disconnect();\r\n            };\r\n        }\r\n    }, [sessionUserId]);\r\n\r\n    // Handle direct URL access\r\n    useEffect(() => {\r\n        if (router.isReady && users.length > 0 && !selectedChat && sessionUserId) {\r\n            const { userId } = router.query;\r\n            if (userId && userId !== 'undefined' && userId !== 'null') {\r\n                const userToSelect = users.find(u => u._id === userId);\r\n                if (userToSelect && accepted.has(userToSelect._id)) {\r\n                    setSelectedChat(userToSelect._id);\r\n                    setSelectedUser(userToSelect);\r\n                    // Load chat history for this user\r\n                    const history = getChatHistory(userToSelect._id);\r\n                    setChatMessages(prev => ({\r\n                        ...prev,\r\n                        [userToSelect._id]: history\r\n                    }));\r\n                }\r\n            }\r\n        }\r\n    }, [router.isReady, users, accepted, selectedChat, sessionUserId]);\r\n\r\n    // Auto-scroll to bottom when new messages are added\r\n    useEffect(() => {\r\n        if (messagesEndRef.current) {\r\n            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [chatMessages, selectedChat]);\r\n\r\n    useEffect(() => {\r\n        const fetchUsers = async () => {\r\n            const storedUser = JSON.parse(sessionStorage.getItem(\"user\"));\r\n            const sessionId = storedUser?.user?.id;\r\n            setSessionUserId(sessionId);\r\n\r\n            try {\r\n                // Check server connection first\r\n                console.log('🔍 Checking server connection for users...');\r\n                await debugServerConnection();\r\n\r\n                console.log('📡 Fetching users list...');\r\n                const response = await axios.post(\"/displayusersProfile\");\r\n\r\n                const allUsers = response.data.filter(user => user._id !== sessionId);\r\n                setUsers(allUsers);\r\n                setVisibleUsers(allUsers.slice(0, 6)); // first 6\r\n                setTimeout(() => setLoading(false), 1000); // optional fake delay\r\n            } catch (error) {\r\n                console.error(\"❌ Error fetching users:\", error);\r\n\r\n                if (error.message === 'Network Error' || error.code === 'ECONNREFUSED') {\r\n                    setError('Cannot connect to server. Please make sure your backend is running on http://localhost:5000');\r\n                    console.error('🚨 Backend server connection failed!');\r\n                } else {\r\n                    setError('Failed to load users: ' + error.message);\r\n                }\r\n\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        fetchUsers();\r\n    }, []);\r\n\r\n    // Filter users based on search query and online status\r\n    const filteredUsers = users\r\n        .filter(user => accepted.has(user._id))\r\n        .filter(user => {\r\n            // Use searchTerm for consistency\r\n            const searchValue = searchTerm.toLowerCase();\r\n            return searchValue === '' ||\r\n                user.name.toLowerCase().includes(searchValue) ||\r\n                user.username.toLowerCase().includes(searchValue);\r\n        })\r\n        .filter(user =>\r\n            showOnlineOnly ? user.isOnline : true\r\n        );\r\n\r\n    const getThemeStyles = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',\r\n                color: '#e2e8f0',\r\n                cardBg: 'rgba(255, 255, 255, 0.1)',\r\n                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',\r\n                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',\r\n                notificationBg: 'rgba(51, 65, 85, 0.9)',\r\n            };\r\n        }\r\n        return {\r\n            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',\r\n            color: '#1e293b',\r\n            cardBg: 'rgba(255, 255, 255, 0.8)',\r\n            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',\r\n            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)',\r\n        };\r\n    };\r\n\r\n    const styles = getThemeStyles();\r\n\r\n    const getThemeStyless = () => {\r\n        if (theme === 'dark') {\r\n            return {\r\n                background: '#1e293b',\r\n                color: '#e2e8f0',\r\n                inputBg: '#334155',\r\n                inputColor: '#e2e8f0',\r\n                messageBgYou: '#3b82f6',\r\n                messageBgFriend: '#4b5563'\r\n            };\r\n        }\r\n        // Default to light styles for 'homeback' or any other theme\r\n        return {\r\n            background: '#ffffff',\r\n            color: '#1e293b',\r\n            inputBg: '#f1f5f9',\r\n            inputColor: '#1e293b',\r\n            messageBgYou: '#3b82f6',\r\n            messageBgFriend: '#e5e7eb'\r\n        };\r\n    };\r\n\r\n    const currentThemeStyles = getThemeStyless();\r\n\r\n    const handleSendMessage = (e) => {\r\n        e.preventDefault();\r\n        console.log('🚀 Attempting to send message:', {\r\n            newMessage: newMessage.trim(),\r\n            selectedChat,\r\n            sessionUserId,\r\n            socketConnected: socketService.getConnectionStatus()\r\n        });\r\n\r\n        if (newMessage.trim() && selectedChat && sessionUserId) {\r\n            // Send message via Socket.IO for real-time delivery\r\n            console.log('📤 Sending message to:', selectedChat, 'from:', sessionUserId);\r\n            console.log('📤 Message content:', newMessage.trim());\r\n            const success = socketService.sendMessage(selectedChat, newMessage.trim());\r\n            console.log('📤 Socket send result:', success);\r\n            console.log('📤 Socket connection status:', socketService.getConnectionStatus());\r\n\r\n            if (success) {\r\n                // Add message to local state immediately for instant UI update\r\n                const newMsg = {\r\n                    id: Date.now(),\r\n                    sender: \"You\",\r\n                    text: newMessage.trim(),\r\n                    timestamp: new Date().toLocaleTimeString([], {\r\n                        hour: '2-digit',\r\n                        minute: '2-digit',\r\n                        hour12: true\r\n                    }),\r\n                    delivered: onlineUsers.has(selectedChat), // Mark as delivered if user is online\r\n                    read: false\r\n                };\r\n\r\n                const currentMessages = chatMessages[selectedChat] || [];\r\n                const updatedMessages = [...currentMessages, newMsg];\r\n\r\n                // Update chat messages state and save to local storage\r\n                setChatMessages(prev => {\r\n                    const updated = {\r\n                        ...prev,\r\n                        [selectedChat]: updatedMessages\r\n                    };\r\n                    // Save to local storage\r\n                    saveChatHistory(selectedChat, updatedMessages);\r\n                    return updated;\r\n                });\r\n\r\n                // Update last message\r\n                saveLastMessage(selectedChat, newMsg.text);\r\n\r\n                // Clear input\r\n                setNewMessage(\"\");\r\n\r\n                // Stop typing indicator\r\n                if (isTyping) {\r\n                    setIsTyping(false);\r\n                    socketService.sendTypingIndicator(selectedChat, false);\r\n                }\r\n            } else {\r\n                console.error('Failed to send message - Socket not connected');\r\n            }\r\n        }\r\n    };\r\n\r\n    // Handle typing indicator\r\n    const handleTyping = useCallback((value) => {\r\n        setNewMessage(value);\r\n\r\n        if (selectedChat && sessionUserId) {\r\n            if (value.trim() && !isTyping) {\r\n                setIsTyping(true);\r\n                socketService.sendTypingIndicator(selectedChat, true);\r\n            } else if (!value.trim() && isTyping) {\r\n                setIsTyping(false);\r\n                socketService.sendTypingIndicator(selectedChat, false);\r\n            }\r\n\r\n            // Clear typing timeout\r\n            if (typingTimeoutRef.current) {\r\n                clearTimeout(typingTimeoutRef.current);\r\n            }\r\n\r\n            // Set new timeout to stop typing indicator\r\n            typingTimeoutRef.current = setTimeout(() => {\r\n                if (isTyping) {\r\n                    setIsTyping(false);\r\n                    socketService.sendTypingIndicator(selectedChat, false);\r\n                }\r\n            }, 2000);\r\n        }\r\n    }, [selectedChat, sessionUserId, isTyping]);\r\n\r\n    useEffect(() => {\r\n        if (!profile || !Array.isArray(profile.followers)) return;\r\n\r\n        // Extract follower user IDs from the populated followers array\r\n        const followersArray = profile.followers.map(f => f._id?.toString());\r\n\r\n        // Filter only users who are followers\r\n        const followedUsers = users.filter(user =>\r\n            followersArray.includes(user._id?.toString())\r\n        );\r\n\r\n        // Filter for search\r\n        const filtered = followedUsers.filter(user =>\r\n            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n            user.username.toLowerCase().includes(searchTerm.toLowerCase())\r\n        );\r\n\r\n        setFilteredFollowers(filtered); // useful for Load More check\r\n\r\n        // Control visible users based on search or default count\r\n        if (searchTerm.trim() === '') {\r\n            setVisibleUsers(followedUsers.slice(0, displayCount));\r\n        } else {\r\n            setVisibleUsers(filtered.slice(0, displayCount));\r\n        }\r\n\r\n    }, [searchTerm, users, displayCount, profile]);\r\n\r\n\r\n    const handleLoadMore = () => {\r\n        const prevCount = displayCount;\r\n        const newCount = prevCount + 6;\r\n        setDisplayCount(newCount);\r\n\r\n        // Scroll to the previous 6th user (after DOM update)\r\n        setTimeout(() => {\r\n            const userElems = document.querySelectorAll(\".user-result\");\r\n            if (userElems[prevCount]) {\r\n                userElems[prevCount].scrollIntoView({\r\n                    behavior: \"smooth\",\r\n                    block: \"start\"\r\n                });\r\n            }\r\n        }, 100); // wait a moment for new DOM elements to render\r\n    };\r\n    return (\r\n        <DashboardLayout>\r\n            {loading ? (\r\n                <div className=\"custom-loader-overlay\">\r\n                    <svg viewBox=\"0 0 100 100\">\r\n                        <g fill=\"none\" stroke=\"#fff\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"6\">\r\n                            {/* left line */}\r\n                            <path d=\"M 21 40 V 59\">\r\n                                <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 21 59; 180 21 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* right line */}\r\n                            <path d=\"M 79 40 V 59\">\r\n                                <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 79 59; -180 79 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* top line */}\r\n                            <path d=\"M 50 21 V 40\">\r\n                                <animate attributeName=\"d\" values=\"M 50 21 V 40; M 50 59 V 40\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* bottom line */}\r\n                            <path d=\"M 50 60 V 79\">\r\n                                <animate attributeName=\"d\" values=\"M 50 60 V 79; M 50 98 V 79\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* top box */}\r\n                            <path d=\"M 50 21 L 79 40 L 50 60 L 21 40 Z\">\r\n                                <animate attributeName=\"stroke\" values=\"rgba(255,255,255,1); rgba(100,100,100,0)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            {/* mid box */}\r\n                            <path d=\"M 50 40 L 79 59 L 50 79 L 21 59 Z\" />\r\n                            {/* bottom box */}\r\n                            <path d=\"M 50 59 L 79 78 L 50 98 L 21 78 Z\">\r\n                                <animate attributeName=\"stroke\" values=\"rgba(100,100,100,0); rgba(255,255,255,1)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                            </path>\r\n                            <animateTransform attributeName=\"transform\" type=\"translate\" values=\"0 0; 0 -19\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </g>\r\n                    </svg>\r\n                </div>\r\n            ) : error ? (\r\n                <div className=\"error\">{error}</div>\r\n            ) : filteredUsers.length === 0 ? (\r\n                <div className=\"chat-container\" style={{ background: styles.background, color: styles.color }}>\r\n                    {/* Left Sidebar - Empty State */}\r\n                    <div className=\"chat-list\">\r\n                        {/* Session User Name - Hidden on mobile */}\r\n                        <h2 className=\"chat-title d-none d-md-block\">\r\n                            {sessionUser?.name || sessionUser?.username || 'User'}\r\n                        </h2>\r\n\r\n                        {/* Search Input - Full width with Bootstrap 5 */}\r\n                        <div className=\"mb-3 px-3\">\r\n                            <div className=\"position-relative\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    className=\"form-control ps-5\"\r\n                                    placeholder=\"Search messages...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    style={{\r\n                                        background: currentThemeStyles.inputBg,\r\n                                        color: currentThemeStyles.inputColor,\r\n                                        border: '1px solid rgba(0,0,0,0.1)',\r\n                                        borderRadius: '25px',\r\n                                        padding: '12px 20px 12px 45px'\r\n                                    }}\r\n                                />\r\n                                <i\r\n                                    className=\"bi bi-search position-absolute\"\r\n                                    style={{\r\n                                        left: '15px',\r\n                                        top: '50%',\r\n                                        transform: 'translateY(-50%)',\r\n                                        color: currentThemeStyles.inputColor,\r\n                                        opacity: 0.6\r\n                                    }}\r\n                                ></i>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* No Friends Message */}\r\n                        <div className=\"text-center py-5\">\r\n                            <i className=\"bi bi-chat-dots\" style={{ fontSize: '3rem', opacity: 0.5 }}></i>\r\n                            <h4 className=\"mt-3\">No friends to message</h4>\r\n                            <p className=\"text-muted\">Add friends to start chatting!</p>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Right Panel - Default Instructions */}\r\n                    <div className=\"chat-panel d-none d-lg-flex\">\r\n                        <div className=\"instructions text-center\">\r\n                            <div className=\"instruction-content\">\r\n                                <i className=\"bi bi-chat-heart\" style={{ fontSize: '4rem', opacity: 0.3, marginBottom: '20px' }}></i>\r\n                                <h3 className=\"instruction-title\">Your messages</h3>\r\n                                <p className=\"instruction-text\">Send a message to start a chat.</p>\r\n                                <button\r\n                                    className=\"btn btn-primary\"\r\n                                    onClick={() => {\r\n                                        // You can add functionality to open friend list or search\r\n                                        console.log('Add friends button clicked');\r\n                                    }}\r\n                                    style={{\r\n                                        borderRadius: '25px',\r\n                                        padding: '10px 30px',\r\n                                        fontWeight: '500'\r\n                                    }}\r\n                                >\r\n                                    <i className=\"bi bi-person-plus me-2\"></i>\r\n                                    Add Friends\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            ) : (\r\n                <div className=\"chat-container\" style={{ background: styles.background, color: styles.color }}>\r\n                    {/* Left Sidebar for Chat List */}\r\n                    <div className={`chat-list ${selectedChat ? 'chat-list-with-panel' : ''}`}>\r\n                        {/* Session User Name - Hidden on mobile */}\r\n                        <h2 className=\"chat-title d-none d-md-block\">\r\n                            {sessionUser?.name || sessionUser?.username || 'User'}\r\n                        </h2>\r\n\r\n                        {/* Search Input - Full width with Bootstrap 5 */}\r\n                        <div className=\"mb-3 px-3\">\r\n                            <div className=\"position-relative\">\r\n                                <input\r\n                                    type=\"text\"\r\n                                    placeholder=\"Search messages...\"\r\n                                    value={searchTerm}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    className=\"form-control\"\r\n                                    style={{\r\n                                        borderRadius: '25px',\r\n                                        paddingLeft: '20px',\r\n                                        paddingRight: '50px'\r\n                                    }}\r\n                                />\r\n                                <span className=\"position-absolute\" style={{\r\n                                    right: '15px',\r\n                                    top: '50%',\r\n                                    transform: 'translateY(-50%)',\r\n                                    color: '#6c757d'\r\n                                }}>\r\n                                    <i className=\"bi bi-search\"></i>\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Messages Label */}\r\n                        <span className=\"fw-bold mb-2 d-block px-3\">Messages</span>\r\n                        {filteredUsers.map(user => (\r\n                            <div\r\n                                key={user._id}\r\n                                className=\"chat-item\"\r\n                                onClick={() => {\r\n                                    setSelectedChat(user._id);\r\n                                    setSelectedUser(user);\r\n\r\n                                    // Mark messages as read and clear unread count\r\n                                    saveUnreadCount(user._id, 0);\r\n\r\n                                    // Check if mobile view\r\n                                    const isMobile = window.innerWidth < 1024;\r\n\r\n                                    if (isMobile) {\r\n                                        // Open modal on mobile\r\n                                        setShowMobileModal(true);\r\n                                    } else {\r\n                                        // Update URL on desktop\r\n                                        router.replace(`/Dashboard/Messages?userId=${user._id}`, undefined, { shallow: true });\r\n                                    }\r\n\r\n                                    // Load chat history for this user\r\n                                    const history = getChatHistory(user._id);\r\n                                    if (history.length > 0) {\r\n                                        setChatMessages(prev => ({\r\n                                            ...prev,\r\n                                            [user._id]: history\r\n                                        }));\r\n                                        console.log(`📖 Loaded ${history.length} messages for chat with:`, user.name);\r\n                                    }\r\n                                }}\r\n                            >\r\n                                <div className=\"avatar-container\">\r\n                                    <Image\r\n                                        src={user.image || predefine}\r\n                                        alt={user.name}\r\n                                        width={80}\r\n                                        height={80}\r\n                                        className=\"avatar\"\r\n                                    />\r\n                                    <span\r\n                                        className={`status-indicator ${onlineUsers.has(user._id) ? 'online' : 'offline'}`}\r\n                                        title={onlineUsers.has(user._id) ? 'Online' : 'Offline'}\r\n                                    ></span>\r\n                                </div>\r\n                                <div className=\"chat-details\">\r\n                                    <div className=\"chat-header\">\r\n                                        <span className=\"user-name\">{user.name}</span>\r\n                                        <span className=\"timestamp\">\r\n                                            {onlineUsers.has(user._id)\r\n                                                ? 'Online'\r\n                                                : formatLastSeen(user.lastSeen || new Date(), false)\r\n                                            }\r\n                                        </span>\r\n                                    </div>\r\n                                    <p className=\"last-message\">\r\n                                        {typingUsers.has(user._id) ? (\r\n                                            <span className=\"typing-text\">\r\n                                                <span className=\"typing-indicator\">\r\n                                                    <span></span>\r\n                                                    <span></span>\r\n                                                    <span></span>\r\n                                                </span>\r\n                                                typing...\r\n                                            </span>\r\n                                        ) : (\r\n                                            lastMessages[user._id] || 'No messages yet'\r\n                                        )}\r\n                                    </p>\r\n                                </div>\r\n\r\n                                {/* Unread Message Count Badge */}\r\n                                {unreadCounts[user._id] > 0 && (\r\n                                    <div className=\"unread-badge\">\r\n                                        {unreadCounts[user._id] > 4 ? '4+' : unreadCounts[user._id]}\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Right Panel for Chat or Instructions - Desktop Only */}\r\n                    <div className=\"chat-panel d-none d-lg-flex\">\r\n                        {selectedChat ? (\r\n                            <div className=\"chat-window\">\r\n                                {/* Chat Header with Back Button for Mobile */}\r\n                                <div className=\"chat-header d-flex align-items-center justify-content-between w-100\">\r\n                                    <div className=\"d-flex align-items-center gap-3\">\r\n                                        <Image\r\n                                            src={filteredUsers.find(u => u._id === selectedChat)?.image || predefine}\r\n                                            alt={filteredUsers.find(u => u._id === selectedChat)?.name}\r\n                                            width={50}\r\n                                            height={50}\r\n                                            className=\"avatar\"\r\n                                            style={{ borderRadius: '50%' }}\r\n                                        />\r\n                                        <div>\r\n                                            <h3 className=\"mb-0\" style={{ fontSize: '1.2rem' }}>\r\n                                                {filteredUsers.find(u => u._id === selectedChat)?.name}\r\n                                            </h3>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    {/* Back Button - Right side on mobile */}\r\n                                    <button\r\n                                        type=\"button\"\r\n                                        className=\"btn d-md-none\"\r\n                                        onClick={() => {\r\n                                            console.log('🔙 Back button clicked');\r\n                                            setSelectedChat(null);\r\n                                            setSelectedUser(null);\r\n                                        }}\r\n                                        style={{\r\n                                            fontSize: '1.5rem',\r\n                                            color: getThemeStyles().color,\r\n                                            background: 'none',\r\n                                            border: 'none',\r\n                                            padding: '8px',\r\n                                            minWidth: '44px',\r\n                                            height: '44px',\r\n                                            display: 'flex',\r\n                                            alignItems: 'center',\r\n                                            justifyContent: 'center'\r\n                                        }}\r\n                                    >\r\n                                        <i className=\"bi bi-x-lg\"></i>\r\n                                    </button>\r\n                                </div>\r\n                                <div\r\n                                    className=\"\"\r\n                                    style={{\r\n                                        background: currentThemeStyles.background,\r\n                                        color: currentThemeStyles.color\r\n                                    }}\r\n                                >\r\n                                    {/* Chat Messages Container with Fixed Height and Scrolling */}\r\n                                    <div\r\n                                        ref={chatContainerRef}\r\n                                        className=\"chat-messages position-relative\"\r\n                                        style={{\r\n                                            height: 'calc(100vh - 300px)',\r\n                                            overflowY: 'auto',\r\n                                            padding: '20px',\r\n                                            display: 'flex',\r\n                                            flexDirection: 'column',\r\n                                            gap: '10px'\r\n                                        }}\r\n                                        onScroll={handleScroll}\r\n                                    >\r\n                                        {chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (\r\n                                            <>\r\n                                                {chatMessages[selectedChat].map((msg) => (\r\n                                                    <div\r\n                                                        key={msg.id}\r\n                                                        className={`message ${msg.sender === \"You\" ? \"message-you\" : \"message-friend\"}`}\r\n                                                    >\r\n                                                        <div\r\n                                                            className=\"message-bubble\"\r\n                                                            style={{\r\n                                                                background: msg.sender === \"You\"\r\n                                                                    ? currentThemeStyles.messageBgYou || '#3b82f6'\r\n                                                                    : 'rgba(255, 255, 255, 0.9)',\r\n                                                                color: msg.sender === \"You\" ? '#ffffff' : '#333'\r\n                                                            }}\r\n                                                            onTouchStart={(e) => {\r\n                                                                // Mobile: Start 2-second hold timer for both sender and receiver\r\n                                                                const timer = setTimeout(() => {\r\n                                                                    handleMobileReactionPopup(msg.id, e.currentTarget);\r\n                                                                }, 2000);\r\n                                                                e.currentTarget.dataset.holdTimer = timer;\r\n                                                            }}\r\n                                                            onTouchEnd={(e) => {\r\n                                                                // Clear timer if touch ends early\r\n                                                                if (e.currentTarget.dataset.holdTimer) {\r\n                                                                    clearTimeout(e.currentTarget.dataset.holdTimer);\r\n                                                                    delete e.currentTarget.dataset.holdTimer;\r\n                                                                }\r\n                                                            }}\r\n                                                        >\r\n                                                            <span className=\"message-text\">{msg.text}</span>\r\n                                                            <div className=\"message-footer\">\r\n                                                                <span className=\"message-timestamp\">{msg.timestamp}</span>\r\n                                                                {msg.sender === \"You\" && (\r\n                                                                    <span className=\"delivery-status\">\r\n                                                                        {msg.delivered ? (\r\n                                                                            <span>Seen</span>\r\n                                                                        ) : (\r\n                                                                            <span>Sent</span>\r\n                                                                        )}\r\n                                                                    </span>\r\n                                                                )}\r\n                                                            </div>\r\n                                                        </div>\r\n\r\n                                                        {/* Desktop Message Reactions Display */}\r\n                                                        {messageReactions[msg.id] && Object.keys(messageReactions[msg.id]).length > 0 && (\r\n                                                            <div style={{\r\n                                                                fontSize: '12px',\r\n                                                                marginBottom: '4px',\r\n                                                                textAlign: msg.sender === \"You\" ? 'right' : 'left',\r\n                                                                marginTop: '4px'\r\n                                                            }}>\r\n                                                                {Object.entries(messageReactions[msg.id]).map(([emoji, users]) => {\r\n                                                                    const userCount = Object.keys(users).length;\r\n                                                                    const userNames = Object.values(users).map(u => u.userName).join(', ');\r\n\r\n                                                                    return (\r\n                                                                        <span\r\n                                                                            key={emoji}\r\n                                                                            style={{\r\n                                                                                marginRight: '4px',\r\n                                                                                backgroundColor: 'rgba(0,123,255,0.1)',\r\n                                                                                padding: '3px 8px',\r\n                                                                                borderRadius: '12px',\r\n                                                                                border: '1px solid rgba(0,123,255,0.2)',\r\n                                                                                cursor: 'pointer',\r\n                                                                                display: 'inline-block'\r\n                                                                            }}\r\n                                                                            title={`${userNames} reacted with ${emoji}`}\r\n                                                                            onClick={() => addReaction(msg.id, emoji)}\r\n                                                                        >\r\n                                                                            {emoji} {userCount}\r\n                                                                        </span>\r\n                                                                    );\r\n                                                                })}\r\n                                                            </div>\r\n                                                        )}\r\n\r\n                                                        {/* Desktop: Always show reaction options for both sender and receiver */}\r\n                                                        <div\r\n                                                            className=\"d-none d-md-block\"\r\n                                                            style={{\r\n                                                                fontSize: '14px',\r\n                                                                marginBottom: '2px',\r\n                                                                textAlign: msg.sender === \"You\" ? 'right' : 'left',\r\n                                                                opacity: 0.7,\r\n                                                                marginTop: '4px'\r\n                                                            }}\r\n                                                        >\r\n                                                            <div style={{\r\n                                                                display: 'inline-block',\r\n                                                                backgroundColor: 'rgba(255,255,255,0.9)',\r\n                                                                padding: '4px 8px',\r\n                                                                borderRadius: '15px',\r\n                                                                border: '1px solid rgba(0,0,0,0.1)',\r\n                                                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n                                                            }}>\r\n                                                                {['❤️', '😂', '👍', '😮', '😢', '😡'].map(emoji => (\r\n                                                                    <span\r\n                                                                        key={emoji}\r\n                                                                        style={{\r\n                                                                            cursor: 'pointer',\r\n                                                                            marginRight: '6px',\r\n                                                                            padding: '4px',\r\n                                                                            borderRadius: '6px',\r\n                                                                            transition: 'all 0.2s',\r\n                                                                            display: 'inline-block'\r\n                                                                        }}\r\n                                                                        onClick={() => addReaction(msg.id, emoji)}\r\n                                                                        onMouseEnter={(e) => {\r\n                                                                            e.target.style.transform = 'scale(1.2)';\r\n                                                                            e.target.style.backgroundColor = 'rgba(0,123,255,0.1)';\r\n                                                                        }}\r\n                                                                        onMouseLeave={(e) => {\r\n                                                                            e.target.style.transform = 'scale(1)';\r\n                                                                            e.target.style.backgroundColor = 'transparent';\r\n                                                                        }}\r\n                                                                        title={`React with ${emoji}`}\r\n                                                                    >\r\n                                                                        {emoji}\r\n                                                                    </span>\r\n                                                                ))}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                ))}\r\n\r\n                                                {/* Typing Indicator */}\r\n                                                {typingUsers.has(selectedChat) && (\r\n                                                    <div className=\"message message-friend\">\r\n                                                        <div\r\n                                                            className=\"message-bubble\"\r\n                                                            style={{\r\n                                                                background: 'rgba(255, 255, 255, 0.9)',\r\n                                                                color: '#333'\r\n                                                            }}\r\n                                                        >\r\n                                                            <span className=\"typing-indicator\">\r\n                                                                <span></span>\r\n                                                                <span></span>\r\n                                                                <span></span>\r\n                                                            </span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                )}\r\n\r\n                                                {/* Auto-scroll anchor */}\r\n                                                <div ref={messagesEndRef} />\r\n                                            </>\r\n                                        ) : (\r\n                                            <div className=\"text-center p-4\" style={{\r\n                                                display: 'flex',\r\n                                                flexDirection: 'column',\r\n                                                alignItems: 'center',\r\n                                                justifyContent: 'center',\r\n                                                height: '100%'\r\n                                            }}>\r\n                                                <i className=\"bi bi-chat-dots\" style={{ fontSize: '3rem', opacity: 0.5 }}></i>\r\n                                                <h4 className=\"mt-3\">No messages yet</h4>\r\n                                                <p className=\"\">Start the conversation by sending a message!</p>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Desktop Scroll to Bottom Button */}\r\n                                        {showScrollToBottom && (\r\n                                            <div\r\n                                                style={{\r\n                                                    position: 'absolute',\r\n                                                    bottom: '80px',\r\n                                                    right: '30px',\r\n                                                    zIndex: 1000\r\n                                                }}\r\n                                            >\r\n                                                <button\r\n                                                    onClick={scrollToBottom}\r\n                                                    style={{\r\n                                                        width: '45px',\r\n                                                        height: '45px',\r\n                                                        borderRadius: '50%',\r\n                                                        backgroundColor: '#007bff',\r\n                                                        border: 'none',\r\n                                                        color: 'white',\r\n                                                        boxShadow: '0 4px 12px rgba(0,123,255,0.3)',\r\n                                                        cursor: 'pointer',\r\n                                                        display: 'flex',\r\n                                                        alignItems: 'center',\r\n                                                        justifyContent: 'center',\r\n                                                        transition: 'all 0.3s ease',\r\n                                                        fontSize: '18px'\r\n                                                    }}\r\n                                                    onMouseEnter={(e) => {\r\n                                                        e.target.style.transform = 'scale(1.1)';\r\n                                                        e.target.style.backgroundColor = '#0056b3';\r\n                                                    }}\r\n                                                    onMouseLeave={(e) => {\r\n                                                        e.target.style.transform = 'scale(1)';\r\n                                                        e.target.style.backgroundColor = '#007bff';\r\n                                                    }}\r\n                                                    title=\"Scroll to bottom\"\r\n                                                >\r\n                                                    ↓\r\n                                                </button>\r\n                                            </div>\r\n                                        )}\r\n                                    </div>\r\n                                    <form className=\"chat-input-form\" onSubmit={handleSendMessage}>\r\n                                        <div className=\"input-group\">\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                className=\"form-control\"\r\n                                                placeholder=\"Type a message...\"\r\n                                                value={newMessage}\r\n                                                onChange={(e) => handleTyping(e.target.value)}\r\n                                                onKeyDown={(e) => {\r\n                                                    if (e.key === 'Enter' && !e.shiftKey) {\r\n                                                        e.preventDefault();\r\n                                                        handleSendMessage(e);\r\n                                                    }\r\n                                                }}\r\n                                                style={{\r\n                                                    background: currentThemeStyles.inputBg,\r\n                                                    color: currentThemeStyles.inputColor,\r\n                                                    border: 'none',\r\n                                                    borderRadius: '25px',\r\n                                                    padding: '12px 20px'\r\n                                                }}\r\n                                            />\r\n                                            <button\r\n                                                type=\"submit\"\r\n                                                className=\"chat-send-btn btn btn-primary\"\r\n                                                onClick={handleSendMessage}\r\n                                                style={{\r\n                                                    borderRadius: '50%',\r\n                                                    width: '45px',\r\n                                                    height: '45px',\r\n                                                    marginLeft: '10px',\r\n                                                    display: 'flex',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center'\r\n                                                }}\r\n                                            >\r\n                                                <i className=\"bi bi-send-fill\"></i>\r\n                                            </button>\r\n                                        </div>\r\n                                    </form>\r\n                                </div>\r\n                            </div>\r\n                        ) : (\r\n                            <div className=\"instructions text-center\">\r\n                                <div className=\"instruction-content\">\r\n                                    <i className=\"bi bi-chat-heart\" style={{ fontSize: '4rem', opacity: 0.3, marginBottom: '20px' }}></i>\r\n                                    <h3 className=\"instruction-title\">Your messages</h3>\r\n                                    <p className=\"instruction-text\">Send a message to start a chat.</p>\r\n                                    <button\r\n                                        className=\"btn btn-primary\"\r\n                                        data-bs-toggle=\"modal\"\r\n                                        data-bs-target=\"#followers\"\r\n                                        style={{\r\n                                            borderRadius: '25px',\r\n                                            padding: '10px 30px',\r\n                                            fontWeight: '500'\r\n                                        }}\r\n                                    >\r\n                                        <i className=\"bi bi-person-plus me-2\"></i>\r\n                                        Send Message\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            )}\r\n            <div className=\"modal\" id=\"followers\">\r\n                <div className=\"modal-dialog\">\r\n                    <div className=\"modal-content\">\r\n                        <div className='d-flex justify-content-between'>\r\n                            <div>\r\n                                <h5>Followers</h5>\r\n                            </div>\r\n                            <div>\r\n                                <button type=\"button\" className=\"btn-close bg-primary\" data-bs-dismiss=\"modal\"></button>\r\n                            </div>\r\n                        </div><hr />\r\n                        <div className=\"\">\r\n                            <div>\r\n                                <input\r\n                                    type=\"search\"\r\n                                    name=\"search\"\r\n                                    id=\"search\"\r\n                                    className=\"form-control mb-3\"\r\n                                    placeholder=\"Search users...\"\r\n                                    value={searchTerm}\r\n                                    style={{\r\n                                        backgroundColor: \"white\",\r\n                                        transition: \"background 0.3s\",\r\n                                        gap: \"10px\",\r\n                                        border: \"1px solid #333\"\r\n                                    }}\r\n                                    onChange={(e) => setSearchTerm(e.target.value)}\r\n                                    onMouseEnter={e => e.currentTarget.style.backgroundColor = \"white\"}\r\n                                    onMouseLeave={e => e.currentTarget.style.backgroundColor = \"whitesmock\"}\r\n                                />\r\n\r\n                                {\r\n                                    loading ? (\r\n                                        <div className='d-flex gap-4'>\r\n                                            <div\r\n                                                className=\"skeleton\"\r\n                                                style={{\r\n                                                    width: \"45px\",\r\n                                                    height: \"45px\",\r\n                                                    borderRadius: \"50%\",\r\n                                                }}\r\n                                            ></div>\r\n                                            <div>\r\n                                                <div\r\n                                                    className=\"skeleton\"\r\n                                                    style={{\r\n                                                        width: \"120px\",\r\n                                                        height: \"16px\",\r\n                                                        borderRadius: \"4px\",\r\n                                                        marginBottom: \"8px\",\r\n                                                    }}\r\n                                                ></div>\r\n                                            </div>\r\n                                        </div>\r\n                                    ) : (\r\n                                        <>\r\n                                            {visibleUsers.length > 0 ? (\r\n                                                visibleUsers.map(user => (\r\n                                                    <div key={user._id} className='d-flex align-items-center mb-2 p-2 rounded user-result' style={{ justifyContent: \"space-between\" }}>\r\n                                                        <div\r\n                                                            className=\"d-flex gap-4 align-items-center\"\r\n                                                            style={{\r\n                                                                cursor: \"pointer\",\r\n                                                            }}\r\n                                                        >\r\n                                                            <Image\r\n                                                                src={user.image || predefine}\r\n                                                                alt={user.name}\r\n                                                                width={60}\r\n                                                                height={60}\r\n                                                                className=\"rounded-circle\"\r\n                                                                style={{ objectFit: \"cover\" }}\r\n                                                            />\r\n                                                            <div>\r\n                                                                <strong>{user.username}</strong><br />\r\n                                                                <span>{user.name}</span>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div>\r\n                                                            <button\r\n                                                                className='btn btn-primary btn-sm'\r\n                                                                onClick={() => handleRemoveFollower(user._id)}\r\n                                                            >\r\n                                                                Message\r\n                                                            </button>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                ))\r\n                                            ) : (\r\n                                                <div className=\"no-followers-container text-center mt-5\">\r\n                                                    <div className=\"icon-wrapper mb-3\">\r\n                                                        <i className=\"bi bi-person-x\" style={{ fontSize: \"3rem\", color: \"#6c757d\" }}></i>\r\n                                                    </div>\r\n                                                    <h5 style={{ color: \"#6c757d\" }}>No Followers Found</h5>\r\n                                                </div>\r\n\r\n                                            )}\r\n                                            {/* Show \"Load More\" button only if there are more followed users to show */}\r\n                                            {visibleUsers.length < filteredFollowers.length && (\r\n                                                searchTerm.trim() === ''\r\n                                                    ? (profile.followers.length || 0)\r\n                                                    : users.filter(user =>\r\n                                                        profile.followers.includes(user._id) &&\r\n                                                        (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                                                            user.username.toLowerCase().includes(searchTerm.toLowerCase()))\r\n                                                    ).length\r\n                                            ) && (\r\n                                                    <div className=\"text-center mt-3\">\r\n                                                        <button className=\"btn w-100 btn-outline-primary\" onClick={handleLoadMore}>\r\n                                                            Load More\r\n                                                        </button>\r\n                                                    </div>\r\n                                                )}\r\n\r\n\r\n                                        </>\r\n\r\n\r\n                                    )\r\n                                }\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Bootstrap 5 Modal for Mobile Chat */}\r\n            <div\r\n                className={`modal fade ${showMobileModal ? 'show' : ''}`}\r\n                id=\"mobileChat\"\r\n                tabIndex=\"-1\"\r\n                aria-labelledby=\"mobileChatLabel\"\r\n                aria-hidden={!showMobileModal}\r\n                style={{\r\n                    display: showMobileModal ? 'block' : 'none',\r\n                    backgroundColor: showMobileModal ? 'rgba(0,0,0,0.5)' : 'transparent'\r\n                }}\r\n            >\r\n                <div className=\"modal-dialog modal-fullscreen\">\r\n                    <div className=\"modal-content\" style={{ background: styles.background, color: styles.color }}>\r\n                        {/* Modal Header - Default Styling */}\r\n                        <div className=\"modal-header border-bottom d-flex justify-content-between\">\r\n                            <div className=\"d-flex align-items-center gap-3\">\r\n                                <div className=\"position-relative\">\r\n                                    <Image\r\n                                        src={selectedUser?.image || predefine}\r\n                                        alt={selectedUser?.name || 'User'}\r\n                                        width={40}\r\n                                        height={40}\r\n                                        className=\"rounded-circle\"\r\n                                    />\r\n                                    {/* Online Status Indicator - Fixed */}\r\n                                    <span\r\n                                        className=\"position-absolute\"\r\n                                        style={{\r\n                                            bottom: '1px',\r\n                                            right: '1px',\r\n                                            width: '12px',\r\n                                            height: '12px',\r\n                                            backgroundColor: onlineUsers.has(selectedUser?._id) ? '#0FC953' : '#E81C1C',\r\n                                            borderRadius: '50%',\r\n                                            boxShadow: '0 2px 0 2px rgba(9, 50, 11, 0.27)'\r\n                                        }}\r\n                                    ></span>\r\n                                </div>\r\n                                <div>\r\n                                    <h6 className=\"mb-0 fw-bold\">\r\n                                        {selectedUser?.name || 'User'}\r\n                                    </h6>\r\n                                    <small>\r\n                                        {onlineUsers.has(selectedUser?._id)\r\n                                            ? 'Online'\r\n                                            : formatLastSeen(selectedUser?.lastSeen || new Date(), false)\r\n                                        }\r\n                                    </small>\r\n                                </div>\r\n                            </div>\r\n                            <div className=\"d-flex align-items-center\">\r\n                                {/* Chat Size Display Only */}\r\n                                {selectedUser && chatSizes[`${sessionUserId}_${selectedUser._id}`] && (\r\n                                    <div className=\"me-2 text-center\">\r\n                                        <small style={{\r\n                                            fontSize: '11px',\r\n                                            color: chatSizes[`${sessionUserId}_${selectedUser._id}`]?.exceedsLimit ? '#dc3545' : '#6c757d'\r\n                                        }}>\r\n                                            {chatSizes[`${sessionUserId}_${selectedUser._id}`]?.sizeFormatted}\r\n                                        </small>\r\n                                    </div>\r\n                                )}\r\n                                <button\r\n                                    type=\"button\"\r\n                                    className=\"btn btn-close bg-warning\"\r\n                                    onClick={handleBackToList}\r\n                                    aria-label=\"Close\"\r\n                                ></button>\r\n                            </div>\r\n                        </div>\r\n\r\n                        {/* Modal Body - Chat Messages */}\r\n                        <div className=\"modal-body d-flex flex-column\" style={{ height: 'calc(100vh - 120px)' }}>\r\n                            <div\r\n                                className=\"flex-grow-1 overflow-auto position-relative\"\r\n                                style={{\r\n                                    maxHeight: 'calc(100vh - 200px)',\r\n                                    padding: '12px 6px'\r\n                                }}\r\n                                onScroll={handleScroll}\r\n                            >\r\n                                {selectedChat && chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (\r\n                                    <>\r\n                                        {chatMessages[selectedChat].map((msg) => (\r\n                                            <div key={msg.id} className=\"mb-2\">\r\n                                                {/* Fixed: Sender RIGHT, Receiver LEFT */}\r\n                                                <div\r\n                                                    className={`d-flex ${msg.sender === \"You\" ? \"justify-content-end\" : \"justify-content-start\"}`}\r\n                                                    style={{ width: '100%' }}\r\n                                                >\r\n                                                    <div\r\n                                                        style={{\r\n                                                            backgroundColor: msg.sender === \"You\"\r\n                                                                ? '#007bff'\r\n                                                                : '#f1f1f1',\r\n                                                            color: msg.sender === \"You\" ? 'white' : '#333',\r\n                                                            padding: '10px 14px',\r\n                                                            borderRadius: msg.sender === \"You\"\r\n                                                                ? '18px 18px 4px 18px'\r\n                                                                : '18px 18px 18px 4px',\r\n                                                            maxWidth: '80%',\r\n                                                            wordWrap: 'break-word',\r\n                                                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\r\n                                                            position: 'relative'\r\n                                                        }}\r\n                                                        onTouchStart={(e) => {\r\n                                                            // Mobile: Start 2-second hold timer for both sender and receiver\r\n                                                            const timer = setTimeout(() => {\r\n                                                                handleMobileReactionPopup(msg.id, e.currentTarget);\r\n                                                            }, 2000);\r\n                                                            e.currentTarget.dataset.holdTimer = timer;\r\n                                                        }}\r\n                                                        onTouchEnd={(e) => {\r\n                                                            // Clear timer if touch ends early\r\n                                                            if (e.currentTarget.dataset.holdTimer) {\r\n                                                                clearTimeout(e.currentTarget.dataset.holdTimer);\r\n                                                                delete e.currentTarget.dataset.holdTimer;\r\n                                                            }\r\n                                                        }}\r\n                                                        className=\"d-md-none\" // Only on mobile\r\n                                                    >\r\n                                                        <div style={{\r\n                                                            fontSize: '15px',\r\n                                                            lineHeight: '1.4',\r\n                                                            marginBottom: '4px',\r\n                                                            textAlign: msg.sender === \"You\" ? 'right' : 'left'\r\n                                                        }}>\r\n                                                            {msg.text}\r\n                                                        </div>\r\n\r\n                                                        {/* Message Reactions Display */}\r\n                                                        {messageReactions[msg.id] && Object.keys(messageReactions[msg.id]).length > 0 && (\r\n                                                            <div style={{\r\n                                                                fontSize: '12px',\r\n                                                                marginBottom: '4px',\r\n                                                                textAlign: msg.sender === \"You\" ? 'right' : 'left'\r\n                                                            }}>\r\n                                                                {Object.entries(messageReactions[msg.id]).map(([emoji, users]) => {\r\n                                                                    const userCount = Object.keys(users).length;\r\n                                                                    const userNames = Object.values(users).map(u => u.userName).join(', ');\r\n\r\n                                                                    return (\r\n                                                                        <span\r\n                                                                            key={emoji}\r\n                                                                            style={{\r\n                                                                                marginRight: '4px',\r\n                                                                                backgroundColor: 'rgba(0,123,255,0.1)',\r\n                                                                                padding: '3px 8px',\r\n                                                                                borderRadius: '12px',\r\n                                                                                border: '1px solid rgba(0,123,255,0.2)',\r\n                                                                                cursor: 'pointer',\r\n                                                                                display: 'inline-block'\r\n                                                                            }}\r\n                                                                            title={`${userNames} reacted with ${emoji}`}\r\n                                                                            onClick={() => addReaction(msg.id, emoji)}\r\n                                                                        >\r\n                                                                            {emoji} {userCount}\r\n                                                                        </span>\r\n                                                                    );\r\n                                                                })}\r\n                                                            </div>\r\n                                                        )}\r\n\r\n                                                        {/* Desktop: Always show reaction options for both sender and receiver */}\r\n                                                        <div\r\n                                                            className=\"d-none d-md-block\"\r\n                                                            style={{\r\n                                                                fontSize: '14px',\r\n                                                                marginBottom: '2px',\r\n                                                                textAlign: msg.sender === \"You\" ? 'right' : 'left',\r\n                                                                opacity: 0.7\r\n                                                            }}\r\n                                                        >\r\n                                                            <div style={{\r\n                                                                display: 'inline-block',\r\n                                                                backgroundColor: 'rgba(255,255,255,0.9)',\r\n                                                                padding: '4px 8px',\r\n                                                                borderRadius: '15px',\r\n                                                                border: '1px solid rgba(0,0,0,0.1)',\r\n                                                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n                                                            }}>\r\n                                                                {['❤️', '😂', '👍', '😮', '😢', '😡'].map(emoji => (\r\n                                                                    <span\r\n                                                                        key={emoji}\r\n                                                                        style={{\r\n                                                                            cursor: 'pointer',\r\n                                                                            marginRight: '6px',\r\n                                                                            padding: '4px',\r\n                                                                            borderRadius: '6px',\r\n                                                                            transition: 'all 0.2s',\r\n                                                                            display: 'inline-block'\r\n                                                                        }}\r\n                                                                        onClick={() => addReaction(msg.id, emoji)}\r\n                                                                        onMouseEnter={(e) => {\r\n                                                                            e.target.style.transform = 'scale(1.2)';\r\n                                                                            e.target.style.backgroundColor = 'rgba(0,123,255,0.1)';\r\n                                                                        }}\r\n                                                                        onMouseLeave={(e) => {\r\n                                                                            e.target.style.transform = 'scale(1)';\r\n                                                                            e.target.style.backgroundColor = 'transparent';\r\n                                                                        }}\r\n                                                                        title={`React with ${emoji}`}\r\n                                                                    >\r\n                                                                        {emoji}\r\n                                                                    </span>\r\n                                                                ))}\r\n                                                            </div>\r\n                                                        </div>\r\n                                                        <div style={{\r\n                                                            fontSize: '11px',\r\n                                                            opacity: 0.7,\r\n                                                            textAlign: msg.sender === \"You\" ? 'right' : 'left',\r\n                                                            marginTop: '2px'\r\n                                                        }}>\r\n                                                            {msg.timestamp}\r\n                                                            {msg.sender === \"You\" && (\r\n                                                                <span style={{ marginLeft: '5px' }}>\r\n                                                                    {msg.delivered ? \"Seen\" : \"Sent\"}\r\n                                                                </span>\r\n                                                            )}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        ))}\r\n\r\n                                        {/* Chat Size Warning and Delete Option */}\r\n                                        {selectedUser && chatSizes[`${sessionUserId}_${selectedUser._id}`] &&\r\n                                         chatSizes[`${sessionUserId}_${selectedUser._id}`]?.exceedsLimit && (\r\n                                            <div className=\"mb-3\">\r\n                                                <div className=\"d-flex justify-content-center\">\r\n                                                    <div\r\n                                                        style={{\r\n                                                            backgroundColor: '#fff3cd',\r\n                                                            color: '#856404',\r\n                                                            padding: '12px 6px',\r\n                                                            borderRadius: '15px',\r\n                                                            border: '1px solid #ffeaa7',\r\n                                                            textAlign: 'center',\r\n                                                            maxWidth: '90%'\r\n                                                        }}\r\n                                                    >\r\n                                                        <button\r\n                                                            className=\"btn btn-sm btn-danger\"\r\n                                                            style={{\r\n                                                                fontSize: '12px',\r\n                                                                padding: '6px 12px',\r\n                                                                borderRadius: '20px'\r\n                                                            }}\r\n                                                            onClick={() => {\r\n                                                                const chatData = chatSizes[`${sessionUserId}_${selectedUser._id}`];\r\n                                                                if (confirm('⚠️ This will permanently delete all messages in this chat. Are you sure?')) {\r\n                                                                    deleteChat(chatData.chatId, sessionUserId, selectedUser._id);\r\n                                                                }\r\n                                                            }}\r\n                                                        >\r\n                                                            🗑️ Delete Chat\r\n                                                        </button>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Typing Indicator - Left side with light green background */}\r\n                                        {typingUsers.has(selectedChat) && (\r\n                                            <div className=\"mb-2\">\r\n                                                <div className=\"d-flex justify-content-start\" style={{ width: '100%' }}>\r\n                                                    <div\r\n                                                        style={{\r\n                                                            backgroundColor: '#d4edda',\r\n                                                            color: '#155724',\r\n                                                            padding: '10px 14px',\r\n                                                            borderRadius: '18px 18px 18px 4px',\r\n                                                            maxWidth: '80%',\r\n                                                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\r\n                                                            border: '1px solid #c3e6cb'\r\n                                                        }}\r\n                                                    >\r\n                                                        <div className=\"d-flex align-items-center\">\r\n                                                            <span className=\"typing-indicator me-2\">\r\n                                                                <span style={{ backgroundColor: '#28a745' }}></span>\r\n                                                                <span style={{ backgroundColor: '#28a745' }}></span>\r\n                                                                <span style={{ backgroundColor: '#28a745' }}></span>\r\n                                                            </span>\r\n                                                            <small style={{\r\n                                                                color: '#155724',\r\n                                                                fontSize: '12px',\r\n                                                                fontStyle: 'italic'\r\n                                                            }}>\r\n                                                                typing...\r\n                                                            </small>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        {/* Scroll to Bottom Button */}\r\n                                        {showScrollToBottom && (\r\n                                            <div\r\n                                                style={{\r\n                                                    position: 'absolute',\r\n                                                    bottom: '20px',\r\n                                                    right: '20px',\r\n                                                    zIndex: 1000\r\n                                                }}\r\n                                            >\r\n                                                <button\r\n                                                    onClick={scrollToBottom}\r\n                                                    style={{\r\n                                                        width: '45px',\r\n                                                        height: '45px',\r\n                                                        borderRadius: '50%',\r\n                                                        backgroundColor: '#007bff',\r\n                                                        border: 'none',\r\n                                                        color: 'white',\r\n                                                        boxShadow: '0 4px 12px rgba(0,123,255,0.3)',\r\n                                                        cursor: 'pointer',\r\n                                                        display: 'flex',\r\n                                                        alignItems: 'center',\r\n                                                        justifyContent: 'center',\r\n                                                        transition: 'all 0.3s ease',\r\n                                                        fontSize: '18px'\r\n                                                    }}\r\n                                                    onMouseEnter={(e) => {\r\n                                                        e.target.style.transform = 'scale(1.1)';\r\n                                                        e.target.style.backgroundColor = '#0056b3';\r\n                                                    }}\r\n                                                    onMouseLeave={(e) => {\r\n                                                        e.target.style.transform = 'scale(1)';\r\n                                                        e.target.style.backgroundColor = '#007bff';\r\n                                                    }}\r\n                                                    title=\"Scroll to bottom\"\r\n                                                >\r\n                                                    ↓\r\n                                                </button>\r\n                                            </div>\r\n                                        )}\r\n\r\n                                        <div ref={messagesEndRef} />\r\n                                    </>\r\n                                ) : (\r\n                                    <div className=\"text-center py-5\">\r\n                                        <i className=\"bi bi-chat-dots\" style={{ fontSize: '3rem', opacity: 0.5 }}></i>\r\n                                        <h4 className=\"mt-3\">No messages yet</h4>\r\n                                        <p className=\"\">Start the conversation by sending a message!</p>\r\n                                    </div>\r\n                                )}\r\n                            </div>\r\n\r\n                            {/* Chat Input - Full Width */}\r\n                            <div className=\"w-100\" style={{ padding: '12px 2px'}}>\r\n                                {deletedChats.has(`${sessionUserId}_${selectedUser?._id}`) ? (\r\n                                    <div className=\"text-center py-3\">\r\n                                        <p className=\"text-muted mb-2\">This chat has been deleted</p>\r\n                                        <button\r\n                                            className=\"btn btn-sm btn-outline-primary\"\r\n                                            onClick={() => {\r\n                                                const chatData = chatSizes[`${sessionUserId}_${selectedUser._id}`];\r\n                                                if (chatData) {\r\n                                                    restoreChat(chatData.chatId, sessionUserId, selectedUser._id);\r\n                                                }\r\n                                            }}\r\n                                        >\r\n                                            Restore Chat\r\n                                        </button>\r\n                                    </div>\r\n                                ) : (\r\n                                    <form onSubmit={handleSendMessage}>\r\n                                        <div className=\"d-flex align-items-center\" style={{ gap: '8px' }}>\r\n                                            <input\r\n                                                type=\"text\"\r\n                                                className=\"form-control\"\r\n                                                placeholder=\"Message...\"\r\n                                                value={newMessage}\r\n                                                onChange={(e) => handleTyping(e.target.value)}\r\n                                                onKeyDown={(e) => {\r\n                                                    if (e.key === 'Enter' && !e.shiftKey) {\r\n                                                        e.preventDefault();\r\n                                                        handleSendMessage(e);\r\n                                                    }\r\n                                                }}\r\n                                                style={{\r\n                                                    borderRadius: '25px',\r\n                                                    padding: '12px 16px',\r\n                                                    border: '1px solid #e0e0e0',\r\n                                                    fontSize: '15px',\r\n                                                    backgroundColor: '#f8f9fa',\r\n                                                    flex: '1'\r\n                                                }}\r\n                                            />\r\n\r\n                                            <button\r\n                                                type=\"submit\"\r\n                                                className=\"btn btn-primary\"\r\n                                                onClick={handleSendMessage}\r\n                                                disabled={!newMessage.trim()}\r\n                                                style={{\r\n                                                    borderRadius: '50%',\r\n                                                    width: '44px',\r\n                                                    height: '44px',\r\n                                                    display: 'flex',\r\n                                                    alignItems: 'center',\r\n                                                    justifyContent: 'center',\r\n                                                    padding: '0',\r\n                                                    backgroundColor: '#007bff',\r\n                                                    border: 'none',\r\n                                                    opacity: !newMessage.trim() ? 0.5 : 1\r\n                                                }}\r\n                                            >\r\n                                                <i className=\"bi bi-send-fill\" style={{ fontSize: '16px' }}></i>\r\n                                            </button>\r\n                                        </div>\r\n                                    </form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Mobile Reaction Popup */}\r\n            {showMobileReactionPopup && (\r\n                <div\r\n                    style={{\r\n                        position: 'fixed',\r\n                        left: Math.max(10, Math.min(reactionPopupPosition.x - 150, window.innerWidth - 320)),\r\n                        top: Math.max(10, reactionPopupPosition.y - 10),\r\n                        zIndex: 9999,\r\n                        backgroundColor: 'white',\r\n                        borderRadius: '30px',\r\n                        padding: '12px 16px',\r\n                        boxShadow: '0 8px 32px rgba(0,0,0,0.3)',\r\n                        display: 'flex',\r\n                        gap: '12px',\r\n                        border: '1px solid #e0e0e0',\r\n                        animation: 'fadeInUp 0.3s ease-out'\r\n                    }}\r\n                    onClick={(e) => e.stopPropagation()}\r\n                >\r\n                    {['❤️', '😂', '👍', '😮', '😢', '😡'].map(emoji => (\r\n                        <span\r\n                            key={emoji}\r\n                            style={{\r\n                                fontSize: '28px',\r\n                                cursor: 'pointer',\r\n                                padding: '8px',\r\n                                borderRadius: '50%',\r\n                                transition: 'all 0.2s',\r\n                                display: 'flex',\r\n                                alignItems: 'center',\r\n                                justifyContent: 'center',\r\n                                width: '44px',\r\n                                height: '44px'\r\n                            }}\r\n                            onClick={() => addReaction(selectedMessageForReaction, emoji)}\r\n                            onTouchStart={(e) => {\r\n                                e.currentTarget.style.transform = 'scale(1.3)';\r\n                                e.currentTarget.style.backgroundColor = 'rgba(0,123,255,0.1)';\r\n                            }}\r\n                            onTouchEnd={(e) => {\r\n                                e.currentTarget.style.transform = 'scale(1)';\r\n                                e.currentTarget.style.backgroundColor = 'transparent';\r\n                            }}\r\n                        >\r\n                            {emoji}\r\n                        </span>\r\n                    ))}\r\n                </div>\r\n            )}\r\n\r\n            {/* Mobile Reaction Popup Overlay */}\r\n            {showMobileReactionPopup && (\r\n                <div\r\n                    style={{\r\n                        position: 'fixed',\r\n                        top: 0,\r\n                        left: 0,\r\n                        right: 0,\r\n                        bottom: 0,\r\n                        zIndex: 9998,\r\n                        backgroundColor: 'transparent'\r\n                    }}\r\n                    onClick={() => {\r\n                        setShowMobileReactionPopup(false);\r\n                        setSelectedMessageForReaction(null);\r\n                    }}\r\n                />\r\n            )}\r\n        </DashboardLayout>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEe,SAAS;;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;QACV,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ,wRAAA,CAAA,UAAS;QACjB,OAAO;QACP,gBAAgB;QAChB,gBAAgB;IACpB;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IAEvB,uCAAuC;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YAAE,IAAI;YAAG,QAAQ;YAAO,MAAM;YAAwB,WAAW;QAAW;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAU,MAAM;YAA6B,WAAW;QAAW;QACpF;YAAE,IAAI;YAAG,QAAQ;YAAO,MAAM;YAAwB,WAAW;QAAW;QAC5E;YAAE,IAAI;YAAG,QAAQ;YAAU,MAAM;YAA6B,WAAW;QAAW;KACvF;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAClD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC1D,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,yDAAyD;IAEzD,MAAM,aAAa,CAAC,QAAQ,SAAS;QACjC,IAAI;YACA,uBAAuB;YACvB,gBAAgB,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM,GAAG,QAAQ,CAAC,EAAE,SAAS;iBAAC;YAElE,uBAAuB;YACvB,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE;gBACjC,CAAC;YAED,sBAAsB;YACtB,aAAa,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,SAAS;YACpD,aAAa,UAAU,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,EAAE,SAAS;YACtD,aAAa,UAAU,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,SAAS;YAEvD,kBAAkB;YAClB,aAAa,CAAA,OAAQ,CAAC;oBAClB,GAAG,IAAI;oBACP,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;wBACvB,aAAa;wBACb,eAAe;wBACf,cAAc;wBACd,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,SAAS;oBACzC;gBACJ,CAAC;YAED,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACX;IACJ;IAEA,MAAM,cAAc,CAAC,QAAQ,SAAS;QAClC,IAAI;YACA,4BAA4B;YAC5B,gBAAgB,CAAA;gBACZ,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS;gBACrC,OAAO;YACX;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACX,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACX;IACJ;IAIA,kCAAkC;IAClC,MAAM,cAAc,CAAC;QACjB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,6DAA6D;IAC7D,MAAM,iBAAiB,CAAC,UAAU;QAC9B,IAAI,UAAU,OAAO;QAErB,MAAM,MAAM,IAAI;QAChB,MAAM,eAAe,IAAI,KAAK;QAC9B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,YAAY,IAAI,CAAC,OAAO,EAAE;QAElE,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;QAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;QAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;QAElD,oCAAoC;QACpC,MAAM,UAAU;YACZ,MAAM;YACN,QAAQ;YACR,QAAQ;QACZ;QAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,IAAI,eAAe,GAAG;YAClB,OAAO,CAAC,UAAU,EAAE,aAAa,kBAAkB,CAAC,SAAS,UAAU;QAC3E,OAAO,IAAI,aAAa,GAAG;YACvB,OAAO,GAAG,WAAW,KAAK,CAAC;QAC/B,OAAO;YACH,OAAO,aAAa,kBAAkB,CAAC,SAAS;gBAC5C,OAAO;gBACP,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACZ;QACJ;IACJ;IAEA,sCAAsC;IACtC,MAAM,sBAAsB;QACxB,SAAS,CAAA,YACL,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,mBAAmB,eAAe,KAAK,QAAQ,EAAE,YAAY,GAAG,CAAC,KAAK,GAAG;gBAC7E,CAAC;IAET;IAEA,oDAAoD;IACpD,MAAM,cAAc,CAAC,WAAW;QAC5B,MAAM,gBAAgB;QAEtB,oBAAoB,CAAA;YAChB,MAAM,mBAAmB,IAAI,CAAC,UAAU,IAAI,CAAC;YAC7C,MAAM,eAAe;gBAAE,GAAG,gBAAgB;YAAC;YAE3C,4CAA4C;YAC5C,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;gBAC9B,IAAI,YAAY,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc,CAAC,cAAc,EAAE;oBAC3E,OAAO,YAAY,CAAC,cAAc,CAAC,cAAc;oBACjD,gCAAgC;oBAChC,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,KAAK,GAAG;wBACvD,OAAO,YAAY,CAAC,cAAc;oBACtC;gBACJ;YACJ;YAEA,mBAAmB;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBACtB,YAAY,CAAC,MAAM,GAAG,CAAC;YAC3B;YACA,YAAY,CAAC,MAAM,CAAC,cAAc,GAAG;gBACjC,QAAQ;gBACR,UAAU,aAAa,QAAQ;gBAC/B,WAAW,IAAI,OAAO,WAAW;YACrC;YAEA,OAAO;gBACH,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;YACjB;QACJ;QAEA,uBAAuB;QACvB,MAAM,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,cAAc,KAAK;QACtE,WAAW;YACP,MAAM,mBAAmB;YACzB,aAAa,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC;QACtD,GAAG;QAEH,oCAAoC;QACpC,2BAA2B;QAC3B,8BAA8B;IAClC;IAEA,gCAAgC;IAChC,MAAM,4BAA4B,CAAC,WAAW;QAC1C,MAAM,OAAO,QAAQ,qBAAqB;QAC1C,yBAAyB;YACrB,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;YAC5B,GAAG,KAAK,GAAG,GAAG;QAClB;QACA,8BAA8B;QAC9B,2BAA2B;QAE3B,uBAAuB;QACvB,IAAI,UAAU,OAAO,EAAE;YACnB,UAAU,OAAO,CAAC;QACtB;IACJ;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACnB,IAAI,iBAAiB,OAAO,EAAE;YAC1B,iBAAiB,OAAO,CAAC,QAAQ,CAAC;gBAC9B,KAAK,iBAAiB,OAAO,CAAC,YAAY;gBAC1C,UAAU;YACd;QACJ;IACJ;IAEA,MAAM,eAAe;QACjB,IAAI,iBAAiB,OAAO,EAAE;YAC1B,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,iBAAiB,OAAO;YAC1E,MAAM,eAAe,eAAe,YAAY,eAAe;YAC/D,sBAAsB,CAAC;QAC3B;IACJ;IAEA,mDAAmD;IACnD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN;QACJ;6BAAG;QAAC;QAAc;KAAa;IAI/B,2CAA2C;IAC3C,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO,EAAE;YACxC,MAAM,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ;YACjD,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,EAAE;QAC3C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACb;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,QAAQ;YACjD,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;YAC7C,QAAQ,GAAG,CAAC,mCAAmC;QACnD,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO;YACtC,MAAM,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,QAAQ;YACrD,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,SAAS,UAAU;QACvC,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACX;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,QAAQ;YACrD,aAAa,OAAO,CAAC,WAAW,MAAM,QAAQ;YAC9C,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;gBACd,CAAC;QACL,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,MAAM,iBAAiB,CAAC;QACpB,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO;YACtC,MAAM,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,QAAQ;YACvD,OAAO,aAAa,OAAO,CAAC,eAAe;QAC/C,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACX;IACJ;IAEA,MAAM,kBAAkB,CAAC,QAAQ;QAC7B,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YAC/B,MAAM,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,QAAQ;YACvD,MAAM,mBAAmB,QAAQ,MAAM,GAAG,KAAK,QAAQ,SAAS,CAAC,GAAG,MAAM,QAAQ;YAClF,aAAa,OAAO,CAAC,YAAY;YACjC,gBAAgB,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;gBACd,CAAC;QACL,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAChD;IACJ;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACrB,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,6BAA6B;QAC7B,IAAI,OAAO,UAAU,IAAI,MAAM;YAC3B,OAAO,OAAO,CAAC,uBAAuB,WAAW;gBAAE,SAAS;YAAK;QACrE;IACJ;IAEA,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,MAAM;gDAAY;oBACd,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;oBACrD,MAAM,YAAY,YAAY,MAAM;oBAEpC,IAAI,CAAC,WAAW;wBACZ,SAAS;wBACT,WAAW;wBACX;oBACJ;oBAEA,IAAI;wBACA,gCAAgC;wBAChC,QAAQ,GAAG,CAAC;wBACZ,MAAM,CAAA,GAAA,gHAAA,CAAA,wBAAqB,AAAD;wBAE1B,4CAA4C;wBAC5C,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAK,CAAC,IAAI,CAAC;wBAElC,MAAM,aAAa,SAAS,IAAI;wBAChC,MAAM,gBAAgB,WAAW,MAAM;0EAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;wBAC7D,MAAM,cAAc,WAAW,IAAI;wEAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;wBACzD,eAAe;wBAEf,sBAAsB;wBACtB,MAAM,YAAY,MAAM,gHAAA,CAAA,UAAK,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW;wBAC/D,MAAM,aAAa,UAAU,IAAI;wBACjC,aAAa,IAAI,IAAI,WAAW,SAAS;wBACzC,YAAY,IAAI,IAAI,WAAW,QAAQ;wBAEvC,mCAAmC;wBACnC,MAAM,mBAAmB,cAAc,MAAM;6EAAC,CAAC,KAAK,OAAS,CAAC;oCAC1D,GAAG,GAAG;oCACN,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,gBAAgB,EAAE,IAAI,OAAO,kBAAkB,IAAI;gCACxF,CAAC;4EAAG,CAAC;wBACL,MAAM,gBAAgB,cAAc,MAAM;0EAAC,CAAC,KAAK,OAAS,CAAC;oCACvD,GAAG,GAAG;oCACN,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,OAAO,IAAI;gCACnE,CAAC;yEAAG,CAAC;wBACL,gBAAgB;wBAChB,aAAa;wBAEb,SAAS;wBACT,WAAW;oBACf,EAAE,OAAO,KAAK;wBACV,QAAQ,KAAK,CAAC,0BAA0B;wBAExC,IAAI,IAAI,OAAO,KAAK,mBAAmB,IAAI,IAAI,KAAK,gBAAgB;4BAChE,SAAS;4BACT,QAAQ,KAAK,CAAC;4BACd,QAAQ,KAAK,CAAC;4BACd,QAAQ,KAAK,CAAC;4BACd,QAAQ,KAAK,CAAC;4BACd,QAAQ,KAAK,CAAC;wBAClB,OAAO;4BACH,SAAS,0BAA0B,IAAI,OAAO;wBAClD;wBAEA,WAAW;oBACf;gBACJ;;YAEA;QACJ;6BAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,sCAAsC;YACtC,MAAM,WAAW;+CAAY;oBACzB;gBACJ;8CAAG,QAAQ,wBAAwB;YAEnC,0BAA0B;YAE1B;sCAAO;oBACH,IAAI,UAAU,cAAc;gBAChC;;QACJ;6BAAG;QAAC;QAAO;KAAY;IAEvB,4DAA4D;IAC5D,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,MAAM,MAAM,GAAG,KAAK,eAAe;gBACnC,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,CAAC;gBACrB,MAAM,qBAAqB,CAAC;gBAC5B,MAAM,qBAAqB,CAAC;gBAE5B,MAAM,OAAO;0CAAC,CAAA;wBACV,IAAI,SAAS,GAAG,CAAC,KAAK,GAAG,GAAG;4BACxB,oBAAoB;4BACpB,MAAM,UAAU,eAAe,KAAK,GAAG;4BACvC,IAAI,QAAQ,MAAM,GAAG,GAAG;gCACpB,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG;gCACxB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,MAAM,CAAC,mBAAmB,CAAC,EAAE,KAAK,IAAI;4BAC3E;4BAEA,oBAAoB;4BACpB,MAAM,cAAc,eAAe,KAAK,GAAG;4BAC3C,IAAI,cAAc,GAAG;gCACjB,kBAAkB,CAAC,KAAK,GAAG,CAAC,GAAG;4BACnC;4BAEA,oBAAoB;4BACpB,MAAM,UAAU,eAAe,KAAK,GAAG;4BACvC,IAAI,SAAS;gCACT,kBAAkB,CAAC,KAAK,GAAG,CAAC,GAAG;4BACnC;4BAEA,0CAA0C;4BAC1C,IAAI,QAAQ,MAAM,GAAG,GAAG;gCACpB,MAAM,YAAY,QAAQ,MAAM;oEAAC,CAAC,MAAM;wCACpC,OAAO,OAAO,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI;oCACzD;mEAAG;gCAEH;0DAAa,CAAA,OAAQ,CAAC;4CAClB,GAAG,IAAI;4CACP,CAAC,GAAG,cAAc,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;gDAC9B,aAAa;gDACb,eAAe,YAAY;gDAC3B,cAAc,aAAc,KAAK,OAAO;gDACxC,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,KAAK,GAAG,EAAE;4CAChD;wCACJ,CAAC;;4BACL;4BAEA,mCAAmC;4BACnC,MAAM,eAAe,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,KAAK,GAAG,EAAE;4BAC7D,MAAM,iBAAiB,aAAa,OAAO,CAAC;4BAC5C,IAAI,gBAAgB;gCAChB,IAAI;oCACA,MAAM,YAAY,KAAK,KAAK,CAAC;oCAC7B;8DAAoB,CAAA,OAAQ,CAAC;gDACzB,GAAG,IAAI;gDACP,GAAG,SAAS;4CAChB,CAAC;;gCACL,EAAE,OAAO,OAAO;oCACZ,QAAQ,KAAK,CAAC,4BAA4B;gCAC9C;4BACJ;wBACJ;oBACJ;;gBAEA,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,QAAQ,GAAG,CAAC;YAChB;QACJ;6BAAG;QAAC;QAAO;QAAe;KAAS;IAEnC,mDAAmD;IACnD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,eAAe;gBACf,uBAAuB;gBACvB,2GAAA,CAAA,UAAa,CAAC,OAAO,CAAC;gBAEtB,+BAA+B;gBAC/B,MAAM,sBAAsB,2GAAA,CAAA,UAAa,CAAC,SAAS;8DAAC,CAAC;wBACjD,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,QAAQ,GAAG,CAAC,6BAA6B;wBACzC,QAAQ,GAAG,CAAC,4BAA4B;wBACxC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;wBAEhE,+DAA+D;wBAC/D,IAAI,eAAe,iBAAiB,aAAa,eAAe;4BAC5D,QAAQ,GAAG,CAAC;4BACZ,MAAM,gBAAgB,eAAe,gBAAgB,WAAW;4BAChE,MAAM,oBAAoB,eAAe;4BAEzC,MAAM,aAAa;gCACf,IAAI;gCACJ,QAAQ,aAAa,gBAAgB,QAAQ;gCAC7C,MAAM;gCACN,WAAW,IAAI,KAAK,WAAW,kBAAkB,CAAC,EAAE,EAAE;oCAClD,MAAM;oCACN,QAAQ;oCACR,QAAQ;gCACZ;gCACA,WAAW;gCACX,MAAM;4BACV;4BAEA,uBAAuB;4BACvB;0EAAgB,CAAA;oCACZ,MAAM,kBAAkB;2CAAK,IAAI,CAAC,cAAc,IAAI,EAAE;wCAAG;qCAAW;oCACpE,wBAAwB;oCACxB,gBAAgB,eAAe;oCAC/B,OAAO;wCACH,GAAG,IAAI;wCACP,CAAC,cAAc,EAAE;oCACrB;gCACJ;;4BAEA,sBAAsB;4BACtB,gBAAgB,eAAe;4BAE/B,4CAA4C;4BAC5C,IAAI,mBAAmB;gCACnB,MAAM,gBAAgB,eAAe;gCACrC,MAAM,iBAAiB,gBAAgB;gCACvC,gBAAgB,eAAe;gCAC/B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,cAAc,EAAE,EAAE,gBAAgB;4BACzE;wBACJ;oBACJ;;gBAEA,mCAAmC;gBACnC,MAAM,0BAA0B,2GAAA,CAAA,UAAa,CAAC,cAAc;kEAAC,CAAC;wBAC1D,QAAQ,GAAG,CAAC,4BAA4B;wBACxC,IAAI,KAAK,IAAI,KAAK,WAAW;4BACzB,QAAQ,GAAG,CAAC,oCAAoC,KAAK,OAAO;4BAC5D,eAAe,IAAI,IAAI,KAAK,OAAO;wBACvC,OAAO,IAAI,KAAK,IAAI,KAAK,UAAU;4BAC/B,QAAQ,GAAG,CAAC,wBAAwB,KAAK,MAAM;4BAC/C;8EAAe,CAAA,OAAQ,IAAI,IAAI;2CAAI;wCAAM,KAAK,MAAM;qCAAC;;wBACzD,OAAO,IAAI,KAAK,IAAI,KAAK,WAAW;4BAChC,QAAQ,GAAG,CAAC,yBAAyB,KAAK,MAAM;4BAChD;8EAAe,CAAA;oCACX,MAAM,SAAS,IAAI,IAAI;oCACvB,OAAO,MAAM,CAAC,KAAK,MAAM;oCACzB,OAAO;gCACX;;wBACJ;oBACJ;;gBAEA,+BAA+B;gBAC/B,MAAM,oBAAoB,2GAAA,CAAA,UAAa,CAAC,QAAQ;4DAAC,CAAC;wBAC9C,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;wBAC7B;oEAAe,CAAA;gCACX,MAAM,SAAS,IAAI,IAAI;gCACvB,IAAI,UAAU;oCACV,QAAQ,GAAG,CAAC,2BAA2B;oCACvC,OAAO,GAAG,CAAC;gCACf,OAAO;oCACH,QAAQ,GAAG,CAAC,2BAA2B;oCACvC,OAAO,MAAM,CAAC;gCAClB;gCACA,OAAO;4BACX;;wBAEA,yCAAyC;wBACzC,IAAI,UAAU;4BACV;wEAAW;oCACP;gFAAe,CAAA;4CACX,MAAM,SAAS,IAAI,IAAI;4CACvB,OAAO,MAAM,CAAC;4CACd,OAAO;wCACX;;gCACJ;uEAAG;wBACP;oBACJ;;gBAEA,qBAAqB;gBACrB;0CAAO;wBACH;wBACA;wBACA;wBACA,2GAAA,CAAA,UAAa,CAAC,UAAU;oBAC5B;;YACJ;QACJ;6BAAG;QAAC;KAAc;IAElB,2BAA2B;IAC3B,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,OAAO,OAAO,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,gBAAgB,eAAe;gBACtE,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,KAAK;gBAC/B,IAAI,UAAU,WAAW,eAAe,WAAW,QAAQ;oBACvD,MAAM,eAAe,MAAM,IAAI;2DAAC,CAAA,IAAK,EAAE,GAAG,KAAK;;oBAC/C,IAAI,gBAAgB,SAAS,GAAG,CAAC,aAAa,GAAG,GAAG;wBAChD,gBAAgB,aAAa,GAAG;wBAChC,gBAAgB;wBAChB,kCAAkC;wBAClC,MAAM,UAAU,eAAe,aAAa,GAAG;wBAC/C;kDAAgB,CAAA,OAAQ,CAAC;oCACrB,GAAG,IAAI;oCACP,CAAC,aAAa,GAAG,CAAC,EAAE;gCACxB,CAAC;;oBACL;gBACJ;YACJ;QACJ;6BAAG;QAAC,OAAO,OAAO;QAAE;QAAO;QAAU;QAAc;KAAc;IAEjE,oDAAoD;IACpD,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,eAAe,OAAO,EAAE;gBACxB,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC/D;QACJ;6BAAG;QAAC;QAAc;KAAa;IAE/B,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,MAAM;iDAAa;oBACf,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC;oBACrD,MAAM,YAAY,YAAY,MAAM;oBACpC,iBAAiB;oBAEjB,IAAI;wBACA,gCAAgC;wBAChC,QAAQ,GAAG,CAAC;wBACZ,MAAM,CAAA,GAAA,gHAAA,CAAA,wBAAqB,AAAD;wBAE1B,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAK,CAAC,IAAI,CAAC;wBAElC,MAAM,WAAW,SAAS,IAAI,CAAC,MAAM;sEAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;wBAC3D,SAAS;wBACT,gBAAgB,SAAS,KAAK,CAAC,GAAG,KAAK,UAAU;wBACjD;6DAAW,IAAM,WAAW;4DAAQ,OAAO,sBAAsB;oBACrE,EAAE,OAAO,OAAO;wBACZ,QAAQ,KAAK,CAAC,2BAA2B;wBAEzC,IAAI,MAAM,OAAO,KAAK,mBAAmB,MAAM,IAAI,KAAK,gBAAgB;4BACpE,SAAS;4BACT,QAAQ,KAAK,CAAC;wBAClB,OAAO;4BACH,SAAS,2BAA2B,MAAM,OAAO;wBACrD;wBAEA,WAAW;oBACf;gBACJ;;YAEA;QACJ;6BAAG,EAAE;IAEL,uDAAuD;IACvD,MAAM,gBAAgB,MACjB,MAAM,CAAC,CAAA,OAAQ,SAAS,GAAG,CAAC,KAAK,GAAG,GACpC,MAAM,CAAC,CAAA;QACJ,iCAAiC;QACjC,MAAM,cAAc,WAAW,WAAW;QAC1C,OAAO,gBAAgB,MACnB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;IAC7C,GACC,MAAM,CAAC,CAAA,OACJ,iBAAiB,KAAK,QAAQ,GAAG;IAGzC,MAAM,iBAAiB;QACnB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,aAAa;gBACb,gBAAgB;YACpB;QACJ;QACA,OAAO;YACH,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,aAAa;QACjB;IACJ;IAEA,MAAM,SAAS;IAEf,MAAM,kBAAkB;QACpB,IAAI,UAAU,QAAQ;YAClB,OAAO;gBACH,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,cAAc;gBACd,iBAAiB;YACrB;QACJ;QACA,4DAA4D;QAC5D,OAAO;YACH,YAAY;YACZ,OAAO;YACP,SAAS;YACT,YAAY;YACZ,cAAc;YACd,iBAAiB;QACrB;IACJ;IAEA,MAAM,qBAAqB;IAE3B,MAAM,oBAAoB,CAAC;QACvB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,kCAAkC;YAC1C,YAAY,WAAW,IAAI;YAC3B;YACA;YACA,iBAAiB,2GAAA,CAAA,UAAa,CAAC,mBAAmB;QACtD;QAEA,IAAI,WAAW,IAAI,MAAM,gBAAgB,eAAe;YACpD,oDAAoD;YACpD,QAAQ,GAAG,CAAC,0BAA0B,cAAc,SAAS;YAC7D,QAAQ,GAAG,CAAC,uBAAuB,WAAW,IAAI;YAClD,MAAM,UAAU,2GAAA,CAAA,UAAa,CAAC,WAAW,CAAC,cAAc,WAAW,IAAI;YACvE,QAAQ,GAAG,CAAC,0BAA0B;YACtC,QAAQ,GAAG,CAAC,gCAAgC,2GAAA,CAAA,UAAa,CAAC,mBAAmB;YAE7E,IAAI,SAAS;gBACT,+DAA+D;gBAC/D,MAAM,SAAS;oBACX,IAAI,KAAK,GAAG;oBACZ,QAAQ;oBACR,MAAM,WAAW,IAAI;oBACrB,WAAW,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;wBACzC,MAAM;wBACN,QAAQ;wBACR,QAAQ;oBACZ;oBACA,WAAW,YAAY,GAAG,CAAC;oBAC3B,MAAM;gBACV;gBAEA,MAAM,kBAAkB,YAAY,CAAC,aAAa,IAAI,EAAE;gBACxD,MAAM,kBAAkB;uBAAI;oBAAiB;iBAAO;gBAEpD,uDAAuD;gBACvD,gBAAgB,CAAA;oBACZ,MAAM,UAAU;wBACZ,GAAG,IAAI;wBACP,CAAC,aAAa,EAAE;oBACpB;oBACA,wBAAwB;oBACxB,gBAAgB,cAAc;oBAC9B,OAAO;gBACX;gBAEA,sBAAsB;gBACtB,gBAAgB,cAAc,OAAO,IAAI;gBAEzC,cAAc;gBACd,cAAc;gBAEd,wBAAwB;gBACxB,IAAI,UAAU;oBACV,YAAY;oBACZ,2GAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;gBACpD;YACJ,OAAO;gBACH,QAAQ,KAAK,CAAC;YAClB;QACJ;IACJ;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC9B,cAAc;YAEd,IAAI,gBAAgB,eAAe;gBAC/B,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU;oBAC3B,YAAY;oBACZ,2GAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;gBACpD,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,UAAU;oBAClC,YAAY;oBACZ,2GAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;gBACpD;gBAEA,uBAAuB;gBACvB,IAAI,iBAAiB,OAAO,EAAE;oBAC1B,aAAa,iBAAiB,OAAO;gBACzC;gBAEA,2CAA2C;gBAC3C,iBAAiB,OAAO,GAAG;0DAAW;wBAClC,IAAI,UAAU;4BACV,YAAY;4BACZ,2GAAA,CAAA,UAAa,CAAC,mBAAmB,CAAC,cAAc;wBACpD;oBACJ;yDAAG;YACP;QACJ;6CAAG;QAAC;QAAc;QAAe;KAAS;IAE1C,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;8BAAE;YACN,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,QAAQ,SAAS,GAAG;YAEnD,+DAA+D;YAC/D,MAAM,iBAAiB,QAAQ,SAAS,CAAC,GAAG;qDAAC,CAAA,IAAK,EAAE,GAAG,EAAE;;YAEzD,sCAAsC;YACtC,MAAM,gBAAgB,MAAM,MAAM;oDAAC,CAAA,OAC/B,eAAe,QAAQ,CAAC,KAAK,GAAG,EAAE;;YAGtC,oBAAoB;YACpB,MAAM,WAAW,cAAc,MAAM;+CAAC,CAAA,OAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;YAG/D,qBAAqB,WAAW,6BAA6B;YAE7D,yDAAyD;YACzD,IAAI,WAAW,IAAI,OAAO,IAAI;gBAC1B,gBAAgB,cAAc,KAAK,CAAC,GAAG;YAC3C,OAAO;gBACH,gBAAgB,SAAS,KAAK,CAAC,GAAG;YACtC;QAEJ;6BAAG;QAAC;QAAY;QAAO;QAAc;KAAQ;IAG7C,MAAM,iBAAiB;QACnB,MAAM,YAAY;QAClB,MAAM,WAAW,YAAY;QAC7B,gBAAgB;QAEhB,qDAAqD;QACrD,WAAW;YACP,MAAM,YAAY,SAAS,gBAAgB,CAAC;YAC5C,IAAI,SAAS,CAAC,UAAU,EAAE;gBACtB,SAAS,CAAC,UAAU,CAAC,cAAc,CAAC;oBAChC,UAAU;oBACV,OAAO;gBACX;YACJ;QACJ,GAAG,MAAM,+CAA+C;IAC5D;IACA,qBACI,0JAAC,kIAAA,CAAA,UAAe;;YACX,wBACG,0JAAC;gBAAI,WAAU;0BACX,cAAA,0JAAC;oBAAI,SAAQ;8BACT,cAAA,0JAAC;wBAAE,MAAK;wBAAO,QAAO;wBAAO,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;;0CAElF,0JAAC;gCAAK,GAAE;0CACJ,cAAA,0JAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAqB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG/G,0JAAC;gCAAK,GAAE;0CACJ,cAAA,0JAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAsB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGhH,0JAAC;gCAAK,GAAE;0CACJ,cAAA,0JAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,0JAAC;gCAAK,GAAE;0CACJ,cAAA,0JAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,0JAAC;gCAAK,GAAE;0CACJ,cAAA,0JAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG3G,0JAAC;gCAAK,GAAE;;;;;;0CAER,0JAAC;gCAAK,GAAE;0CACJ,cAAA,0JAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAE3G,0JAAC;gCAAiB,eAAc;gCAAY,MAAK;gCAAY,QAAO;gCAAa,KAAI;gCAAK,aAAY;;;;;;;;;;;;;;;;;;;;;uBAIlH,sBACA,0JAAC;gBAAI,WAAU;0BAAS;;;;;uBACxB,cAAc,MAAM,KAAK,kBACzB,0JAAC;gBAAI,WAAU;gBAAiB,OAAO;oBAAE,YAAY,OAAO,UAAU;oBAAE,OAAO,OAAO,KAAK;gBAAC;;kCAExF,0JAAC;wBAAI,WAAU;;0CAEX,0JAAC;gCAAG,WAAU;0CACT,aAAa,QAAQ,aAAa,YAAY;;;;;;0CAInD,0JAAC;gCAAI,WAAU;0CACX,cAAA,0JAAC;oCAAI,WAAU;;sDACX,0JAAC;4CACG,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,OAAO;gDACH,YAAY,mBAAmB,OAAO;gDACtC,OAAO,mBAAmB,UAAU;gDACpC,QAAQ;gDACR,cAAc;gDACd,SAAS;4CACb;;;;;;sDAEJ,0JAAC;4CACG,WAAU;4CACV,OAAO;gDACH,MAAM;gDACN,KAAK;gDACL,WAAW;gDACX,OAAO,mBAAmB,UAAU;gDACpC,SAAS;4CACb;;;;;;;;;;;;;;;;;0CAMZ,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCAAE,WAAU;wCAAkB,OAAO;4CAAE,UAAU;4CAAQ,SAAS;wCAAI;;;;;;kDACvE,0JAAC;wCAAG,WAAU;kDAAO;;;;;;kDACrB,0JAAC;wCAAE,WAAU;kDAAa;;;;;;;;;;;;;;;;;;kCAKlC,0JAAC;wBAAI,WAAU;kCACX,cAAA,0JAAC;4BAAI,WAAU;sCACX,cAAA,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCAAE,WAAU;wCAAmB,OAAO;4CAAE,UAAU;4CAAQ,SAAS;4CAAK,cAAc;wCAAO;;;;;;kDAC9F,0JAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,0JAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,0JAAC;wCACG,WAAU;wCACV,SAAS;4CACL,0DAA0D;4CAC1D,QAAQ,GAAG,CAAC;wCAChB;wCACA,OAAO;4CACH,cAAc;4CACd,SAAS;4CACT,YAAY;wCAChB;;0DAEA,0JAAC;gDAAE,WAAU;;;;;;4CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAQ9D,0JAAC;gBAAI,WAAU;gBAAiB,OAAO;oBAAE,YAAY,OAAO,UAAU;oBAAE,OAAO,OAAO,KAAK;gBAAC;;kCAExF,0JAAC;wBAAI,WAAW,CAAC,UAAU,EAAE,eAAe,yBAAyB,IAAI;;0CAErE,0JAAC;gCAAG,WAAU;0CACT,aAAa,QAAQ,aAAa,YAAY;;;;;;0CAInD,0JAAC;gCAAI,WAAU;0CACX,cAAA,0JAAC;oCAAI,WAAU;;sDACX,0JAAC;4CACG,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;4CACV,OAAO;gDACH,cAAc;gDACd,aAAa;gDACb,cAAc;4CAClB;;;;;;sDAEJ,0JAAC;4CAAK,WAAU;4CAAoB,OAAO;gDACvC,OAAO;gDACP,KAAK;gDACL,WAAW;gDACX,OAAO;4CACX;sDACI,cAAA,0JAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAMzB,0JAAC;gCAAK,WAAU;0CAA4B;;;;;;4BAC3C,cAAc,GAAG,CAAC,CAAA,qBACf,0JAAC;oCAEG,WAAU;oCACV,SAAS;wCACL,gBAAgB,KAAK,GAAG;wCACxB,gBAAgB;wCAEhB,+CAA+C;wCAC/C,gBAAgB,KAAK,GAAG,EAAE;wCAE1B,uBAAuB;wCACvB,MAAM,WAAW,OAAO,UAAU,GAAG;wCAErC,IAAI,UAAU;4CACV,uBAAuB;4CACvB,mBAAmB;wCACvB,OAAO;4CACH,wBAAwB;4CACxB,OAAO,OAAO,CAAC,CAAC,2BAA2B,EAAE,KAAK,GAAG,EAAE,EAAE,WAAW;gDAAE,SAAS;4CAAK;wCACxF;wCAEA,kCAAkC;wCAClC,MAAM,UAAU,eAAe,KAAK,GAAG;wCACvC,IAAI,QAAQ,MAAM,GAAG,GAAG;4CACpB,gBAAgB,CAAA,OAAQ,CAAC;oDACrB,GAAG,IAAI;oDACP,CAAC,KAAK,GAAG,CAAC,EAAE;gDAChB,CAAC;4CACD,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,MAAM,CAAC,wBAAwB,CAAC,EAAE,KAAK,IAAI;wCAChF;oCACJ;;sDAEA,0JAAC;4CAAI,WAAU;;8DACX,0JAAC,yHAAA,CAAA,UAAK;oDACF,KAAK,KAAK,KAAK,IAAI,wRAAA,CAAA,UAAS;oDAC5B,KAAK,KAAK,IAAI;oDACd,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;8DAEd,0JAAC;oDACG,WAAW,CAAC,iBAAiB,EAAE,YAAY,GAAG,CAAC,KAAK,GAAG,IAAI,WAAW,WAAW;oDACjF,OAAO,YAAY,GAAG,CAAC,KAAK,GAAG,IAAI,WAAW;;;;;;;;;;;;sDAGtD,0JAAC;4CAAI,WAAU;;8DACX,0JAAC;oDAAI,WAAU;;sEACX,0JAAC;4DAAK,WAAU;sEAAa,KAAK,IAAI;;;;;;sEACtC,0JAAC;4DAAK,WAAU;sEACX,YAAY,GAAG,CAAC,KAAK,GAAG,IACnB,WACA,eAAe,KAAK,QAAQ,IAAI,IAAI,QAAQ;;;;;;;;;;;;8DAI1D,0JAAC;oDAAE,WAAU;8DACR,YAAY,GAAG,CAAC,KAAK,GAAG,kBACrB,0JAAC;wDAAK,WAAU;;0EACZ,0JAAC;gEAAK,WAAU;;kFACZ,0JAAC;;;;;kFACD,0JAAC;;;;;kFACD,0JAAC;;;;;;;;;;;4DACE;;;;;;+DAIX,YAAY,CAAC,KAAK,GAAG,CAAC,IAAI;;;;;;;;;;;;wCAMrC,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,mBACtB,0JAAC;4CAAI,WAAU;sDACV,YAAY,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,OAAO,YAAY,CAAC,KAAK,GAAG,CAAC;;;;;;;mCAzE9D,KAAK,GAAG;;;;;;;;;;;kCAiFzB,0JAAC;wBAAI,WAAU;kCACV,6BACG,0JAAC;4BAAI,WAAU;;8CAEX,0JAAC;oCAAI,WAAU;;sDACX,0JAAC;4CAAI,WAAU;;8DACX,0JAAC,yHAAA,CAAA,UAAK;oDACF,KAAK,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe,SAAS,wRAAA,CAAA,UAAS;oDACxE,KAAK,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe;oDACtD,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,OAAO;wDAAE,cAAc;oDAAM;;;;;;8DAEjC,0JAAC;8DACG,cAAA,0JAAC;wDAAG,WAAU;wDAAO,OAAO;4DAAE,UAAU;wDAAS;kEAC5C,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,eAAe;;;;;;;;;;;;;;;;;sDAM9D,0JAAC;4CACG,MAAK;4CACL,WAAU;4CACV,SAAS;gDACL,QAAQ,GAAG,CAAC;gDACZ,gBAAgB;gDAChB,gBAAgB;4CACpB;4CACA,OAAO;gDACH,UAAU;gDACV,OAAO,iBAAiB,KAAK;gDAC7B,YAAY;gDACZ,QAAQ;gDACR,SAAS;gDACT,UAAU;gDACV,QAAQ;gDACR,SAAS;gDACT,YAAY;gDACZ,gBAAgB;4CACpB;sDAEA,cAAA,0JAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAGrB,0JAAC;oCACG,WAAU;oCACV,OAAO;wCACH,YAAY,mBAAmB,UAAU;wCACzC,OAAO,mBAAmB,KAAK;oCACnC;;sDAGA,0JAAC;4CACG,KAAK;4CACL,WAAU;4CACV,OAAO;gDACH,QAAQ;gDACR,WAAW;gDACX,SAAS;gDACT,SAAS;gDACT,eAAe;gDACf,KAAK;4CACT;4CACA,UAAU;;gDAET,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,kBAC/D;;wDACK,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAC7B,0JAAC;gEAEG,WAAW,CAAC,QAAQ,EAAE,IAAI,MAAM,KAAK,QAAQ,gBAAgB,kBAAkB;;kFAE/E,0JAAC;wEACG,WAAU;wEACV,OAAO;4EACH,YAAY,IAAI,MAAM,KAAK,QACrB,mBAAmB,YAAY,IAAI,YACnC;4EACN,OAAO,IAAI,MAAM,KAAK,QAAQ,YAAY;wEAC9C;wEACA,cAAc,CAAC;4EACX,iEAAiE;4EACjE,MAAM,QAAQ,WAAW;gFACrB,0BAA0B,IAAI,EAAE,EAAE,EAAE,aAAa;4EACrD,GAAG;4EACH,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS,GAAG;wEACxC;wEACA,YAAY,CAAC;4EACT,kCAAkC;4EAClC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE;gFACnC,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS;gFAC9C,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS;4EAC5C;wEACJ;;0FAEA,0JAAC;gFAAK,WAAU;0FAAgB,IAAI,IAAI;;;;;;0FACxC,0JAAC;gFAAI,WAAU;;kGACX,0JAAC;wFAAK,WAAU;kGAAqB,IAAI,SAAS;;;;;;oFACjD,IAAI,MAAM,KAAK,uBACZ,0JAAC;wFAAK,WAAU;kGACX,IAAI,SAAS,iBACV,0JAAC;sGAAK;;;;;iHAEN,0JAAC;sGAAK;;;;;;;;;;;;;;;;;;;;;;;oEAQzB,gBAAgB,CAAC,IAAI,EAAE,CAAC,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,mBACxE,0JAAC;wEAAI,OAAO;4EACR,UAAU;4EACV,cAAc;4EACd,WAAW,IAAI,MAAM,KAAK,QAAQ,UAAU;4EAC5C,WAAW;wEACf;kFACK,OAAO,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM;4EACzD,MAAM,YAAY,OAAO,IAAI,CAAC,OAAO,MAAM;4EAC3C,MAAM,YAAY,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;4EAEjE,qBACI,0JAAC;gFAEG,OAAO;oFACH,aAAa;oFACb,iBAAiB;oFACjB,SAAS;oFACT,cAAc;oFACd,QAAQ;oFACR,QAAQ;oFACR,SAAS;gFACb;gFACA,OAAO,GAAG,UAAU,cAAc,EAAE,OAAO;gFAC3C,SAAS,IAAM,YAAY,IAAI,EAAE,EAAE;;oFAElC;oFAAM;oFAAE;;+EAbJ;;;;;wEAgBjB;;;;;;kFAKR,0JAAC;wEACG,WAAU;wEACV,OAAO;4EACH,UAAU;4EACV,cAAc;4EACd,WAAW,IAAI,MAAM,KAAK,QAAQ,UAAU;4EAC5C,SAAS;4EACT,WAAW;wEACf;kFAEA,cAAA,0JAAC;4EAAI,OAAO;gFACR,SAAS;gFACT,iBAAiB;gFACjB,SAAS;gFACT,cAAc;gFACd,QAAQ;gFACR,WAAW;4EACf;sFACK;gFAAC;gFAAM;gFAAM;gFAAM;gFAAM;gFAAM;6EAAK,CAAC,GAAG,CAAC,CAAA,sBACtC,0JAAC;oFAEG,OAAO;wFACH,QAAQ;wFACR,aAAa;wFACb,SAAS;wFACT,cAAc;wFACd,YAAY;wFACZ,SAAS;oFACb;oFACA,SAAS,IAAM,YAAY,IAAI,EAAE,EAAE;oFACnC,cAAc,CAAC;wFACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wFAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;oFACrC;oFACA,cAAc,CAAC;wFACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wFAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;oFACrC;oFACA,OAAO,CAAC,WAAW,EAAE,OAAO;8FAE3B;mFApBI;;;;;;;;;;;;;;;;+DAhGhB,IAAI,EAAE;;;;;wDA6HlB,YAAY,GAAG,CAAC,+BACb,0JAAC;4DAAI,WAAU;sEACX,cAAA,0JAAC;gEACG,WAAU;gEACV,OAAO;oEACH,YAAY;oEACZ,OAAO;gEACX;0EAEA,cAAA,0JAAC;oEAAK,WAAU;;sFACZ,0JAAC;;;;;sFACD,0JAAC;;;;;sFACD,0JAAC;;;;;;;;;;;;;;;;;;;;;sEAOjB,0JAAC;4DAAI,KAAK;;;;;;;iFAGd,0JAAC;oDAAI,WAAU;oDAAkB,OAAO;wDACpC,SAAS;wDACT,eAAe;wDACf,YAAY;wDACZ,gBAAgB;wDAChB,QAAQ;oDACZ;;sEACI,0JAAC;4DAAE,WAAU;4DAAkB,OAAO;gEAAE,UAAU;gEAAQ,SAAS;4DAAI;;;;;;sEACvE,0JAAC;4DAAG,WAAU;sEAAO;;;;;;sEACrB,0JAAC;4DAAE,WAAU;sEAAG;;;;;;;;;;;;gDAKvB,oCACG,0JAAC;oDACG,OAAO;wDACH,UAAU;wDACV,QAAQ;wDACR,OAAO;wDACP,QAAQ;oDACZ;8DAEA,cAAA,0JAAC;wDACG,SAAS;wDACT,OAAO;4DACH,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,iBAAiB;4DACjB,QAAQ;4DACR,OAAO;4DACP,WAAW;4DACX,QAAQ;4DACR,SAAS;4DACT,YAAY;4DACZ,gBAAgB;4DAChB,YAAY;4DACZ,UAAU;wDACd;wDACA,cAAc,CAAC;4DACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4DAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;wDACrC;wDACA,cAAc,CAAC;4DACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4DAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;wDACrC;wDACA,OAAM;kEACT;;;;;;;;;;;;;;;;;sDAMb,0JAAC;4CAAK,WAAU;4CAAkB,UAAU;sDACxC,cAAA,0JAAC;gDAAI,WAAU;;kEACX,0JAAC;wDACG,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,WAAW,CAAC;4DACR,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;gEAClC,EAAE,cAAc;gEAChB,kBAAkB;4DACtB;wDACJ;wDACA,OAAO;4DACH,YAAY,mBAAmB,OAAO;4DACtC,OAAO,mBAAmB,UAAU;4DACpC,QAAQ;4DACR,cAAc;4DACd,SAAS;wDACb;;;;;;kEAEJ,0JAAC;wDACG,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,OAAO;4DACH,cAAc;4DACd,OAAO;4DACP,QAAQ;4DACR,YAAY;4DACZ,SAAS;4DACT,YAAY;4DACZ,gBAAgB;wDACpB;kEAEA,cAAA,0JAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOjC,0JAAC;4BAAI,WAAU;sCACX,cAAA,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCAAE,WAAU;wCAAmB,OAAO;4CAAE,UAAU;4CAAQ,SAAS;4CAAK,cAAc;wCAAO;;;;;;kDAC9F,0JAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,0JAAC;wCAAE,WAAU;kDAAmB;;;;;;kDAChC,0JAAC;wCACG,WAAU;wCACV,kBAAe;wCACf,kBAAe;wCACf,OAAO;4CACH,cAAc;4CACd,SAAS;4CACT,YAAY;wCAChB;;0DAEA,0JAAC;gDAAE,WAAU;;;;;;4CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStE,0JAAC;gBAAI,WAAU;gBAAQ,IAAG;0BACtB,cAAA,0JAAC;oBAAI,WAAU;8BACX,cAAA,0JAAC;wBAAI,WAAU;;0CACX,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;kDACG,cAAA,0JAAC;sDAAG;;;;;;;;;;;kDAER,0JAAC;kDACG,cAAA,0JAAC;4CAAO,MAAK;4CAAS,WAAU;4CAAuB,mBAAgB;;;;;;;;;;;;;;;;;0CAEzE,0JAAC;;;;;0CACP,0JAAC;gCAAI,WAAU;0CACX,cAAA,0JAAC;;sDACG,0JAAC;4CACG,MAAK;4CACL,MAAK;4CACL,IAAG;4CACH,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,OAAO;gDACH,iBAAiB;gDACjB,YAAY;gDACZ,KAAK;gDACL,QAAQ;4CACZ;4CACA,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;4CAC3D,cAAc,CAAA,IAAK,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;;;;;;wCAI3D,wBACI,0JAAC;4CAAI,WAAU;;8DACX,0JAAC;oDACG,WAAU;oDACV,OAAO;wDACH,OAAO;wDACP,QAAQ;wDACR,cAAc;oDAClB;;;;;;8DAEJ,0JAAC;8DACG,cAAA,0JAAC;wDACG,WAAU;wDACV,OAAO;4DACH,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,cAAc;wDAClB;;;;;;;;;;;;;;;;iEAKZ;;gDACK,aAAa,MAAM,GAAG,IACnB,aAAa,GAAG,CAAC,CAAA,qBACb,0JAAC;wDAAmB,WAAU;wDAAyD,OAAO;4DAAE,gBAAgB;wDAAgB;;0EAC5H,0JAAC;gEACG,WAAU;gEACV,OAAO;oEACH,QAAQ;gEACZ;;kFAEA,0JAAC,yHAAA,CAAA,UAAK;wEACF,KAAK,KAAK,KAAK,IAAI,wRAAA,CAAA,UAAS;wEAC5B,KAAK,KAAK,IAAI;wEACd,OAAO;wEACP,QAAQ;wEACR,WAAU;wEACV,OAAO;4EAAE,WAAW;wEAAQ;;;;;;kFAEhC,0JAAC;;0FACG,0JAAC;0FAAQ,KAAK,QAAQ;;;;;;0FAAU,0JAAC;;;;;0FACjC,0JAAC;0FAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;;0EAGxB,0JAAC;0EACG,cAAA,0JAAC;oEACG,WAAU;oEACV,SAAS,IAAM,qBAAqB,KAAK,GAAG;8EAC/C;;;;;;;;;;;;uDAxBC,KAAK,GAAG;;;;8EA+BtB,0JAAC;oDAAI,WAAU;;sEACX,0JAAC;4DAAI,WAAU;sEACX,cAAA,0JAAC;gEAAE,WAAU;gEAAiB,OAAO;oEAAE,UAAU;oEAAQ,OAAO;gEAAU;;;;;;;;;;;sEAE9E,0JAAC;4DAAG,OAAO;gEAAE,OAAO;4DAAU;sEAAG;;;;;;;;;;;;gDAKxC,aAAa,MAAM,GAAG,kBAAkB,MAAM,IAAI,CAC/C,WAAW,IAAI,OAAO,KACf,QAAQ,SAAS,CAAC,MAAM,IAAI,IAC7B,MAAM,MAAM,CAAC,CAAA,OACX,QAAQ,SAAS,CAAC,QAAQ,CAAC,KAAK,GAAG,KACnC,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,GAAG,GACpE,MAAM,AAChB,mBACQ,0JAAC;oDAAI,WAAU;8DACX,cAAA,0JAAC;wDAAO,WAAU;wDAAgC,SAAS;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAmBvH,0JAAC;gBACG,WAAW,CAAC,WAAW,EAAE,kBAAkB,SAAS,IAAI;gBACxD,IAAG;gBACH,UAAS;gBACT,mBAAgB;gBAChB,eAAa,CAAC;gBACd,OAAO;oBACH,SAAS,kBAAkB,UAAU;oBACrC,iBAAiB,kBAAkB,oBAAoB;gBAC3D;0BAEA,cAAA,0JAAC;oBAAI,WAAU;8BACX,cAAA,0JAAC;wBAAI,WAAU;wBAAgB,OAAO;4BAAE,YAAY,OAAO,UAAU;4BAAE,OAAO,OAAO,KAAK;wBAAC;;0CAEvF,0JAAC;gCAAI,WAAU;;kDACX,0JAAC;wCAAI,WAAU;;0DACX,0JAAC;gDAAI,WAAU;;kEACX,0JAAC,yHAAA,CAAA,UAAK;wDACF,KAAK,cAAc,SAAS,wRAAA,CAAA,UAAS;wDACrC,KAAK,cAAc,QAAQ;wDAC3B,OAAO;wDACP,QAAQ;wDACR,WAAU;;;;;;kEAGd,0JAAC;wDACG,WAAU;wDACV,OAAO;4DACH,QAAQ;4DACR,OAAO;4DACP,OAAO;4DACP,QAAQ;4DACR,iBAAiB,YAAY,GAAG,CAAC,cAAc,OAAO,YAAY;4DAClE,cAAc;4DACd,WAAW;wDACf;;;;;;;;;;;;0DAGR,0JAAC;;kEACG,0JAAC;wDAAG,WAAU;kEACT,cAAc,QAAQ;;;;;;kEAE3B,0JAAC;kEACI,YAAY,GAAG,CAAC,cAAc,OACzB,WACA,eAAe,cAAc,YAAY,IAAI,QAAQ;;;;;;;;;;;;;;;;;;kDAKvE,0JAAC;wCAAI,WAAU;;4CAEV,gBAAgB,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,kBAC9D,0JAAC;gDAAI,WAAU;0DACX,cAAA,0JAAC;oDAAM,OAAO;wDACV,UAAU;wDACV,OAAO,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,EAAE,eAAe,YAAY;oDACzF;8DACK,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,EAAE;;;;;;;;;;;0DAIhE,0JAAC;gDACG,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,cAAW;;;;;;;;;;;;;;;;;;0CAMvB,0JAAC;gCAAI,WAAU;gCAAgC,OAAO;oCAAE,QAAQ;gCAAsB;;kDAClF,0JAAC;wCACG,WAAU;wCACV,OAAO;4CACH,WAAW;4CACX,SAAS;wCACb;wCACA,UAAU;kDAET,gBAAgB,YAAY,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,kBAC/E;;gDACK,YAAY,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,oBAC7B,0JAAC;wDAAiB,WAAU;kEAExB,cAAA,0JAAC;4DACG,WAAW,CAAC,OAAO,EAAE,IAAI,MAAM,KAAK,QAAQ,wBAAwB,yBAAyB;4DAC7F,OAAO;gEAAE,OAAO;4DAAO;sEAEvB,cAAA,0JAAC;gEACG,OAAO;oEACH,iBAAiB,IAAI,MAAM,KAAK,QAC1B,YACA;oEACN,OAAO,IAAI,MAAM,KAAK,QAAQ,UAAU;oEACxC,SAAS;oEACT,cAAc,IAAI,MAAM,KAAK,QACvB,uBACA;oEACN,UAAU;oEACV,UAAU;oEACV,WAAW;oEACX,UAAU;gEACd;gEACA,cAAc,CAAC;oEACX,iEAAiE;oEACjE,MAAM,QAAQ,WAAW;wEACrB,0BAA0B,IAAI,EAAE,EAAE,EAAE,aAAa;oEACrD,GAAG;oEACH,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS,GAAG;gEACxC;gEACA,YAAY,CAAC;oEACT,kCAAkC;oEAClC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE;wEACnC,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS;wEAC9C,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS;oEAC5C;gEACJ;gEACA,WAAU,YAAY,iBAAiB;;;kFAEvC,0JAAC;wEAAI,OAAO;4EACR,UAAU;4EACV,YAAY;4EACZ,cAAc;4EACd,WAAW,IAAI,MAAM,KAAK,QAAQ,UAAU;wEAChD;kFACK,IAAI,IAAI;;;;;;oEAIZ,gBAAgB,CAAC,IAAI,EAAE,CAAC,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,mBACxE,0JAAC;wEAAI,OAAO;4EACR,UAAU;4EACV,cAAc;4EACd,WAAW,IAAI,MAAM,KAAK,QAAQ,UAAU;wEAChD;kFACK,OAAO,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM;4EACzD,MAAM,YAAY,OAAO,IAAI,CAAC,OAAO,MAAM;4EAC3C,MAAM,YAAY,OAAO,MAAM,CAAC,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;4EAEjE,qBACI,0JAAC;gFAEG,OAAO;oFACH,aAAa;oFACb,iBAAiB;oFACjB,SAAS;oFACT,cAAc;oFACd,QAAQ;oFACR,QAAQ;oFACR,SAAS;gFACb;gFACA,OAAO,GAAG,UAAU,cAAc,EAAE,OAAO;gFAC3C,SAAS,IAAM,YAAY,IAAI,EAAE,EAAE;;oFAElC;oFAAM;oFAAE;;+EAbJ;;;;;wEAgBjB;;;;;;kFAKR,0JAAC;wEACG,WAAU;wEACV,OAAO;4EACH,UAAU;4EACV,cAAc;4EACd,WAAW,IAAI,MAAM,KAAK,QAAQ,UAAU;4EAC5C,SAAS;wEACb;kFAEA,cAAA,0JAAC;4EAAI,OAAO;gFACR,SAAS;gFACT,iBAAiB;gFACjB,SAAS;gFACT,cAAc;gFACd,QAAQ;gFACR,WAAW;4EACf;sFACK;gFAAC;gFAAM;gFAAM;gFAAM;gFAAM;gFAAM;6EAAK,CAAC,GAAG,CAAC,CAAA,sBACtC,0JAAC;oFAEG,OAAO;wFACH,QAAQ;wFACR,aAAa;wFACb,SAAS;wFACT,cAAc;wFACd,YAAY;wFACZ,SAAS;oFACb;oFACA,SAAS,IAAM,YAAY,IAAI,EAAE,EAAE;oFACnC,cAAc,CAAC;wFACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wFAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;oFACrC;oFACA,cAAc,CAAC;wFACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;wFAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;oFACrC;oFACA,OAAO,CAAC,WAAW,EAAE,OAAO;8FAE3B;mFApBI;;;;;;;;;;;;;;;kFAyBrB,0JAAC;wEAAI,OAAO;4EACR,UAAU;4EACV,SAAS;4EACT,WAAW,IAAI,MAAM,KAAK,QAAQ,UAAU;4EAC5C,WAAW;wEACf;;4EACK,IAAI,SAAS;4EACb,IAAI,MAAM,KAAK,uBACZ,0JAAC;gFAAK,OAAO;oFAAE,YAAY;gFAAM;0FAC5B,IAAI,SAAS,GAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;;uDArIxC,IAAI,EAAE;;;;;gDA+InB,gBAAgB,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,IACjE,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,EAAE,8BAChD,0JAAC;oDAAI,WAAU;8DACX,cAAA,0JAAC;wDAAI,WAAU;kEACX,cAAA,0JAAC;4DACG,OAAO;gEACH,iBAAiB;gEACjB,OAAO;gEACP,SAAS;gEACT,cAAc;gEACd,QAAQ;gEACR,WAAW;gEACX,UAAU;4DACd;sEAEA,cAAA,0JAAC;gEACG,WAAU;gEACV,OAAO;oEACH,UAAU;oEACV,SAAS;oEACT,cAAc;gEAClB;gEACA,SAAS;oEACL,MAAM,WAAW,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC;oEAClE,IAAI,QAAQ,6EAA6E;wEACrF,WAAW,SAAS,MAAM,EAAE,eAAe,aAAa,GAAG;oEAC/D;gEACJ;0EACH;;;;;;;;;;;;;;;;;;;;;gDAShB,YAAY,GAAG,CAAC,+BACb,0JAAC;oDAAI,WAAU;8DACX,cAAA,0JAAC;wDAAI,WAAU;wDAA+B,OAAO;4DAAE,OAAO;wDAAO;kEACjE,cAAA,0JAAC;4DACG,OAAO;gEACH,iBAAiB;gEACjB,OAAO;gEACP,SAAS;gEACT,cAAc;gEACd,UAAU;gEACV,WAAW;gEACX,QAAQ;4DACZ;sEAEA,cAAA,0JAAC;gEAAI,WAAU;;kFACX,0JAAC;wEAAK,WAAU;;0FACZ,0JAAC;gFAAK,OAAO;oFAAE,iBAAiB;gFAAU;;;;;;0FAC1C,0JAAC;gFAAK,OAAO;oFAAE,iBAAiB;gFAAU;;;;;;0FAC1C,0JAAC;gFAAK,OAAO;oFAAE,iBAAiB;gFAAU;;;;;;;;;;;;kFAE9C,0JAAC;wEAAM,OAAO;4EACV,OAAO;4EACP,UAAU;4EACV,WAAW;wEACf;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;gDAUtB,oCACG,0JAAC;oDACG,OAAO;wDACH,UAAU;wDACV,QAAQ;wDACR,OAAO;wDACP,QAAQ;oDACZ;8DAEA,cAAA,0JAAC;wDACG,SAAS;wDACT,OAAO;4DACH,OAAO;4DACP,QAAQ;4DACR,cAAc;4DACd,iBAAiB;4DACjB,QAAQ;4DACR,OAAO;4DACP,WAAW;4DACX,QAAQ;4DACR,SAAS;4DACT,YAAY;4DACZ,gBAAgB;4DAChB,YAAY;4DACZ,UAAU;wDACd;wDACA,cAAc,CAAC;4DACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4DAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;wDACrC;wDACA,cAAc,CAAC;4DACX,EAAE,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG;4DAC3B,EAAE,MAAM,CAAC,KAAK,CAAC,eAAe,GAAG;wDACrC;wDACA,OAAM;kEACT;;;;;;;;;;;8DAMT,0JAAC;oDAAI,KAAK;;;;;;;yEAGd,0JAAC;4CAAI,WAAU;;8DACX,0JAAC;oDAAE,WAAU;oDAAkB,OAAO;wDAAE,UAAU;wDAAQ,SAAS;oDAAI;;;;;;8DACvE,0JAAC;oDAAG,WAAU;8DAAO;;;;;;8DACrB,0JAAC;oDAAE,WAAU;8DAAG;;;;;;;;;;;;;;;;;kDAM5B,0JAAC;wCAAI,WAAU;wCAAQ,OAAO;4CAAE,SAAS;wCAAU;kDAC9C,aAAa,GAAG,CAAC,GAAG,cAAc,CAAC,EAAE,cAAc,KAAK,kBACrD,0JAAC;4CAAI,WAAU;;8DACX,0JAAC;oDAAE,WAAU;8DAAkB;;;;;;8DAC/B,0JAAC;oDACG,WAAU;oDACV,SAAS;wDACL,MAAM,WAAW,SAAS,CAAC,GAAG,cAAc,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC;wDAClE,IAAI,UAAU;4DACV,YAAY,SAAS,MAAM,EAAE,eAAe,aAAa,GAAG;wDAChE;oDACJ;8DACH;;;;;;;;;;;iEAKL,0JAAC;4CAAK,UAAU;sDACZ,cAAA,0JAAC;gDAAI,WAAU;gDAA4B,OAAO;oDAAE,KAAK;gDAAM;;kEAC3D,0JAAC;wDACG,MAAK;wDACL,WAAU;wDACV,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,WAAW,CAAC;4DACR,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;gEAClC,EAAE,cAAc;gEAChB,kBAAkB;4DACtB;wDACJ;wDACA,OAAO;4DACH,cAAc;4DACd,SAAS;4DACT,QAAQ;4DACR,UAAU;4DACV,iBAAiB;4DACjB,MAAM;wDACV;;;;;;kEAGJ,0JAAC;wDACG,MAAK;wDACL,WAAU;wDACV,SAAS;wDACT,UAAU,CAAC,WAAW,IAAI;wDAC1B,OAAO;4DACH,cAAc;4DACd,OAAO;4DACP,QAAQ;4DACR,SAAS;4DACT,YAAY;4DACZ,gBAAgB;4DAChB,SAAS;4DACT,iBAAiB;4DACjB,QAAQ;4DACR,SAAS,CAAC,WAAW,IAAI,KAAK,MAAM;wDACxC;kEAEA,cAAA,0JAAC;4DAAE,WAAU;4DAAkB,OAAO;gEAAE,UAAU;4DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAY5F,yCACG,0JAAC;gBACG,OAAO;oBACH,UAAU;oBACV,MAAM,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,sBAAsB,CAAC,GAAG,KAAK,OAAO,UAAU,GAAG;oBAC/E,KAAK,KAAK,GAAG,CAAC,IAAI,sBAAsB,CAAC,GAAG;oBAC5C,QAAQ;oBACR,iBAAiB;oBACjB,cAAc;oBACd,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,KAAK;oBACL,QAAQ;oBACR,WAAW;gBACf;gBACA,SAAS,CAAC,IAAM,EAAE,eAAe;0BAEhC;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK,CAAC,GAAG,CAAC,CAAA,sBACtC,0JAAC;wBAEG,OAAO;4BACH,UAAU;4BACV,QAAQ;4BACR,SAAS;4BACT,cAAc;4BACd,YAAY;4BACZ,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,OAAO;4BACP,QAAQ;wBACZ;wBACA,SAAS,IAAM,YAAY,4BAA4B;wBACvD,cAAc,CAAC;4BACX,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BAClC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC5C;wBACA,YAAY,CAAC;4BACT,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,GAAG;4BAClC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;wBAC5C;kCAEC;uBAvBI;;;;;;;;;;YA8BpB,yCACG,0JAAC;gBACG,OAAO;oBACH,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,QAAQ;oBACR,iBAAiB;gBACrB;gBACA,SAAS;oBACL,2BAA2B;oBAC3B,8BAA8B;gBAClC;;;;;;;;;;;;AAKpB;GAxgEwB;;QAsBF,mHAAA,CAAA,WAAQ;QAEX,0HAAA,CAAA,YAAS;;;KAxBJ", "debugId": null}}, {"offset": {"line": 5112, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/entry/page-loader.ts"], "sourcesContent": ["const PAGE_PATH = \"/Dashboard/Messages\";\n\n/// <reference types=\"next/client\" />\r\n\r\n// inserted by rust code\r\ndeclare const PAGE_PATH: string\r\n\r\n  // Adapted from https://github.com/vercel/next.js/blob/b7f9f1f98fc8ab602e84825105b5727272b72e7d/packages/next/src/build/webpack/loaders/next-client-pages-loader.ts\r\n;(window.__NEXT_P = window.__NEXT_P || []).push([\r\n  PAGE_PATH,\r\n  () => {\r\n    return require('PAGE')\r\n  },\r\n])\r\n// @ts-expect-error module.hot exists\r\nif (module.hot) {\r\n  // @ts-expect-error module.hot exists\r\n  module.hot.dispose(function () {\r\n    window.__NEXT_P.push([PAGE_PATH])\r\n  })\r\n}\r\n"], "names": [], "mappings": "AAAA,MAAM,YAAY;AAQjB,CAAC,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE,EAAE,IAAI,CAAC;IAC9C;IACA;QACE;IACF;CACD;AACD,qCAAqC;AACrC,IAAI,OAAO,GAAG,EAAE;IACd,qCAAqC;IACrC,OAAO,GAAG,CAAC,OAAO,CAAC;QACjB,OAAO,QAAQ,CAAC,IAAI,CAAC;YAAC;SAAU;IAClC;AACF", "ignoreList": [0], "debugId": null}}]}