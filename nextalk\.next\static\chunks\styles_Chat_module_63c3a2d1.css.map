{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/styles/Chat.module.css"], "sourcesContent": ["/* Chat Container Styles */\n.chatContainer {\n    display: flex;\n    height: 80vh;\n    border-radius: 15px;\n    overflow: hidden;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n    backdrop-filter: blur(10px);\n}\n\n.chatList {\n    width: 350px;\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\n    overflow-y: auto;\n    background: rgba(255, 255, 255, 0.05);\n}\n\n.chatTitle {\n    padding: 20px;\n    margin: 0;\n    font-size: 1.5rem;\n    font-weight: 600;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    text-align: center;\n}\n\n.chatItem {\n    display: flex;\n    align-items: center;\n    padding: 15px 20px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.chatItem:hover {\n    background: rgba(255, 255, 255, 0.1);\n    transform: translateX(5px);\n}\n\n.chatItem.active {\n    background: linear-gradient(45deg, #3b82f6, #60a5fa);\n    color: white;\n}\n\n.avatarContainer {\n    position: relative;\n    margin-right: 15px;\n}\n\n.avatar {\n    border-radius: 50%;\n    object-fit: cover;\n    border: 2px solid rgba(255, 255, 255, 0.2);\n}\n\n.statusIndicator {\n    position: absolute;\n    bottom: 2px;\n    right: 2px;\n    width: 12px;\n    height: 12px;\n    background: #10b981;\n    border-radius: 50%;\n    border: 2px solid white;\n}\n\n.chatDetails {\n    flex: 1;\n    min-width: 0;\n}\n\n.chatHeader {\n    display: flex;\n    justify-content: between;\n    align-items: center;\n    margin-bottom: 5px;\n}\n\n.userName {\n    font-weight: 600;\n    font-size: 1rem;\n}\n\n.timestamp {\n    font-size: 0.75rem;\n    opacity: 0.7;\n    margin-left: auto;\n}\n\n.lastMessage {\n    font-size: 0.85rem;\n    opacity: 0.8;\n    margin: 0;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n/* Chat Panel Styles */\n.chatPanel {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    background: rgba(255, 255, 255, 0.02);\n}\n\n.chatWindow {\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n}\n\n.chatHeader {\n    padding: 20px;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(255, 255, 255, 0.05);\n}\n\n.chatMessages {\n    flex: 1;\n    padding: 20px;\n    overflow-y: auto;\n    max-height: 400px;\n}\n\n.chatMessages::-webkit-scrollbar {\n    width: 6px;\n}\n\n.chatMessages::-webkit-scrollbar-track {\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 3px;\n}\n\n.chatMessages::-webkit-scrollbar-thumb {\n    background: rgba(255, 255, 255, 0.3);\n    border-radius: 3px;\n}\n\n.message {\n    margin-bottom: 15px;\n    display: flex;\n}\n\n.messageYou {\n    justify-content: flex-end;\n}\n\n.messageFriend {\n    justify-content: flex-start;\n}\n\n.messageBubble {\n    max-width: 70%;\n    padding: 12px 16px;\n    border-radius: 18px;\n    display: flex;\n    flex-direction: column;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    animation: messageSlideIn 0.3s ease-out;\n}\n\n@keyframes messageSlideIn {\n    from {\n        opacity: 0;\n        transform: translateY(10px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.messageText {\n    margin-bottom: 4px;\n    line-height: 1.4;\n}\n\n.messageTimestamp {\n    font-size: 0.75rem;\n    opacity: 0.7;\n    align-self: flex-end;\n}\n\n/* Chat Input Styles */\n.chatInputForm {\n    display: flex;\n    padding: 20px;\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\n    background: rgba(255, 255, 255, 0.05);\n    gap: 10px;\n}\n\n.chatInput {\n    flex: 1;\n    padding: 12px 16px;\n    border: 1px solid rgba(255, 255, 255, 0.2);\n    border-radius: 25px;\n    outline: none;\n    font-size: 1rem;\n    transition: all 0.3s ease;\n}\n\n.chatInput:focus {\n    border-color: #3b82f6;\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.chatSendBtn {\n    width: 50px;\n    height: 50px;\n    border: none;\n    border-radius: 50%;\n    background: linear-gradient(45deg, #3b82f6, #60a5fa);\n    color: white;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.chatSendBtn:hover {\n    transform: scale(1.05);\n    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);\n}\n\n/* Instructions Styles */\n.instructions {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    text-align: center;\n    padding: 40px;\n}\n\n.iconContainer {\n    margin-bottom: 20px;\n}\n\n.chatIcon {\n    font-size: 4rem;\n    opacity: 0.6;\n}\n\n.instructionTitle {\n    font-size: 1.5rem;\n    font-weight: 600;\n    margin-bottom: 10px;\n}\n\n.instructionText {\n    opacity: 0.8;\n    margin-bottom: 20px;\n}\n\n.sendButton {\n    padding: 12px 24px;\n    border: none;\n    border-radius: 25px;\n    background: linear-gradient(45deg, #3b82f6, #60a5fa);\n    color: white;\n    font-weight: 600;\n    cursor: pointer;\n    transition: all 0.3s ease;\n}\n\n.sendButton:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);\n}\n\n/* No Messages Styles */\n.noMessages {\n    text-align: center;\n    padding: 40px 20px;\n    opacity: 0.6;\n}\n\n.noMessages i {\n    font-size: 3rem;\n    margin-bottom: 15px;\n    display: block;\n}\n\n.noMessages h4 {\n    margin-bottom: 10px;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n    .chatContainer {\n        flex-direction: column;\n        height: 90vh;\n    }\n    \n    .chatList {\n        width: 100%;\n        max-height: 200px;\n    }\n    \n    .chatItem {\n        padding: 10px 15px;\n    }\n    \n    .chatMessages {\n        max-height: 300px;\n    }\n    \n    .messageBubble {\n        max-width: 85%;\n    }\n}\n\n@media (max-width: 480px) {\n    .chatInputForm {\n        padding: 15px;\n    }\n    \n    .chatInput {\n        padding: 10px 14px;\n        font-size: 0.9rem;\n    }\n    \n    .chatSendBtn {\n        width: 45px;\n        height: 45px;\n    }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;AAOA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAKA;EACI;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAKJ;EACI;;;;EAIA;;;;;EAKA", "debugId": null}}]}