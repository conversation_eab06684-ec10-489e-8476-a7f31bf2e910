{"version": 3, "sources": [], "sections": [{"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/utils/axiosConfig.js"], "sourcesContent": ["import axios from \"axios\";\r\n\r\n// Create an instance with proper server URL\r\nconst axiosInstance = axios.create({\r\n    baseURL: process.env.REACT_APP_API_URL ||\r\n             process.env.NEXT_PUBLIC_SERVER_URL ||\r\n             (process.env.NODE_ENV === 'production'\r\n               ? 'https://nextalk-u0y1.onrender.com'\r\n               : 'http://localhost:5000'),\r\n    withCredentials: true,           // Crucial for sending cookies\r\n    headers: {\r\n        \"Content-Type\": \"application/json\",\r\n    },\r\n});\r\n\r\nexport default axiosInstance;\r\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,4CAA4C;AAC5C,MAAM,gBAAgB,0GAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC/B,SAAS,QAAQ,GAAG,CAAC,iBAAiB,IAC7B,QAAQ,GAAG,CAAC,sBAAsB,IAClC,CAAC,6EAEG,uBAAuB;IACpC,iBAAiB;IACjB,SAAS;QACL,gBAAgB;IACpB;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file://E%3A/Websites/Chat-Application/nextalk/pages/Auth/Login.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/router';\r\nimport axios from '../../utils/axiosConfig';\r\nimport { useTheme } from '../../context/ThemeContext';\r\nimport Head from 'next/head';\r\n\r\nfunction Login() {\r\n    const [formData, setFormData] = useState({ username: '', password: '', agree: false });\r\n    const router = useRouter();\r\n    const [error, setError] = useState('');\r\n    const [success, setSuccess] = useState('');\r\n    const [progress, setProgress] = useState(0);\r\n    const [loading, setLoading] = useState(false);\r\n\r\n    const { theme, handleThemeClick } = useTheme();\r\n\r\n    const handleChange = (e) => {\r\n        const { name, value, type, checked } = e.target;\r\n        setFormData({\r\n            ...formData,\r\n            [name]: type === 'checkbox' ? checked : value,\r\n        });\r\n    };\r\n\r\n    const handleSubmit = async (e) => {\r\n        e.preventDefault();\r\n        setError('');\r\n        setLoading(true);\r\n        setProgress(10);\r\n\r\n        if (!formData.username || !formData.password) {\r\n            setError('Please fill in all required fields!');\r\n            setLoading(false);\r\n            return;\r\n        }\r\n\r\n        const interval = setInterval(() => {\r\n            setProgress((prev) => {\r\n                if (prev >= 80) {\r\n                    clearInterval(interval);\r\n                    return prev;\r\n                }\r\n                return prev + 20;\r\n            });\r\n        }, 200);\r\n\r\n        try {\r\n            const response = await axios.post('https://nextalk-u0y1.onrender.com/login', formData, {\r\n                headers: { 'Content-Type': 'application/json' },\r\n                withCredentials: true,\r\n            });\r\n\r\n            sessionStorage.setItem(\"user\", JSON.stringify(response.data));\r\n            setSuccess(\"Login successful! 🎉\");\r\n\r\n            router.replace('/Dashboard');\r\n        } catch (error) {\r\n            setProgress(0);\r\n            if (error.response) {\r\n                if (error.response.status === 401) {\r\n                    setError('Invalid Username or Password.');\r\n                } else {\r\n                    setError(error.response.data.error || 'Login failed. Please try again.');\r\n                }\r\n            } else {\r\n                setError('Network error. Please try again.');\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const videoSrc = {\r\n        homeback: \"/Images/download.png\",\r\n        homesecond: \"/Images/download2.png\",\r\n        homethird: \"/Images/download3.png\",\r\n        homefourth: \"/Images/download4.png\",\r\n        homefive: \"/Images/download5.png\",\r\n    };\r\n    const gradients = {\r\n        homeback: \"linear-gradient(120deg, #4F1787, #003161)\",\r\n        homesecond: \"linear-gradient(120deg, #7A1CAC, #2E073F)\",\r\n        homethird: \"linear-gradient(120deg, #1F6E8C, #0E2954, #2E8A99)\",\r\n        homefourth: \"linear-gradient(120deg, #790252, #AF0171, violet)\",\r\n        homefive: \"linear-gradient(160deg, #183D3D, green)\",\r\n    };\r\n\r\n    const themeKeys = Object.keys(videoSrc);\r\n\r\n    // Fallback to 'homeback' if theme isn’t in videoSrc\r\n    const currentTheme = themeKeys.includes(theme) ? theme : 'homeback';\r\n\r\n\r\n    const ChangeColor = () => {\r\n        const randomIndex = Math.floor(Math.random() * themeKeys.length);\r\n        const randomTheme = themeKeys[randomIndex];\r\n        handleThemeClick(randomTheme);\r\n    };\r\n\r\n    // Optional: Persist theme in localStorage\r\n    useEffect(() => {\r\n        localStorage.setItem('theme', currentTheme);\r\n    }, [currentTheme]);\r\n\r\n    return (\r\n        <>{loading && !error &&(\r\n            <div className=\"custom-loader-overlay\">\r\n                <svg viewBox=\"0 0 100 100\">\r\n                    <g fill=\"none\" stroke=\"#fff\" strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"6\">\r\n                        {/* left line */}\r\n                        <path d=\"M 21 40 V 59\">\r\n                            <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 21 59; 180 21 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        {/* right line */}\r\n                        <path d=\"M 79 40 V 59\">\r\n                            <animateTransform attributeName=\"transform\" type=\"rotate\" values=\"0 79 59; -180 79 59\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        {/* top line */}\r\n                        <path d=\"M 50 21 V 40\">\r\n                            <animate attributeName=\"d\" values=\"M 50 21 V 40; M 50 59 V 40\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        {/* bottom line */}\r\n                        <path d=\"M 50 60 V 79\">\r\n                            <animate attributeName=\"d\" values=\"M 50 60 V 79; M 50 98 V 79\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        {/* top box */}\r\n                        <path d=\"M 50 21 L 79 40 L 50 60 L 21 40 Z\">\r\n                            <animate attributeName=\"stroke\" values=\"rgba(255,255,255,1); rgba(100,100,100,0)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        {/* mid box */}\r\n                        <path d=\"M 50 40 L 79 59 L 50 79 L 21 59 Z\" />\r\n                        {/* bottom box */}\r\n                        <path d=\"M 50 59 L 79 78 L 50 98 L 21 78 Z\">\r\n                            <animate attributeName=\"stroke\" values=\"rgba(100,100,100,0); rgba(255,255,255,1)\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                        </path>\r\n                        <animateTransform attributeName=\"transform\" type=\"translate\" values=\"0 0; 0 -19\" dur=\"2s\" repeatCount=\"indefinite\" />\r\n                    </g>\r\n                </svg>\r\n            </div>\r\n        )}\r\n            <div className=\"login-container\">\r\n                <Head>\r\n                    <title>Login in NexTalk</title>\r\n                </Head>\r\n                <div className=\"row g-0 shadow-lg login-wrapper\">\r\n                    {/* Login Section */}\r\n                    <div className=\"col-md-6 login-section\">\r\n                        {error && (\r\n                            <div className=\"alert alert-danger\" role=\"alert\">\r\n                                <strong>Alert!</strong> {error}\r\n                            </div>\r\n                        )}\r\n\r\n                        <div className=\"card-body\">\r\n                            <h2 className=\"text-center mb-4 text-primary\">Welcome Back</h2>\r\n                            <form onSubmit={handleSubmit}>\r\n                                <div className=\"mb-3\">\r\n                                    <label htmlFor=\"username\" className=\"form-label fw-bold\">Username</label>\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        className=\"form-control form-control-lg\"\r\n                                        id=\"username\"\r\n                                        name=\"username\"\r\n                                        placeholder=\"Enter your username\"\r\n                                        value={formData.username}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </div>\r\n\r\n                                <div className=\"mb-3\">\r\n                                    <label htmlFor=\"password\" className=\"form-label fw-bold\">Password</label>\r\n                                    <input\r\n                                        type=\"password\"\r\n                                        className=\"form-control form-control-lg\"\r\n                                        id=\"password\"\r\n                                        name=\"password\"\r\n                                        placeholder=\"Enter your password\"\r\n                                        value={formData.password}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </div>\r\n                                <br /><br />\r\n                                <button type=\"submit\" className=\"btn btn-primary w-100 login-btn\">\r\n                                    Login\r\n                                </button>\r\n                                <br /><br />\r\n                                <Link href=\"/Auth/Forgot\" className=\"btn btn-lg btn-primary w-100 login-btn\" style={{ textDecoration: \"none\" }}>\r\n                                    Forgotten password\r\n                                </Link>\r\n                            </form>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Create Account Section */}\r\n                    <div className=\"col-md-6 create-section\" id='two'>\r\n                        <div\r\n                            className=\"Randomimages1\"\r\n                            style={{\r\n                                backgroundImage: `url(${videoSrc[currentTheme]}), ${gradients[currentTheme]}`,\r\n                                backgroundSize: \"cover\",\r\n                                backgroundRepeat: \"no-repeat\",\r\n                                backgroundPosition: \"center\",\r\n                                backgroundBlendMode: \"overlay\",\r\n                                position: \"absolute\",\r\n                                top: 0,\r\n                                left: 0,\r\n                                width: \"100%\",\r\n                                height: \"100%\",\r\n                                zIndex: 0,\r\n                                transition: \"background-image 0.5s ease\",\r\n                            }}\r\n                        ></div>\r\n\r\n                        <div className=\"rand\" onClick={() => console.log('Theme before change:', theme) || ChangeColor()} style={{ zIndex: 3, position: \"absolute\", top: \"20px\", right: \"20px\" }}>\r\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"36\" height=\"36\" fill=\"currentColor\" className=\"bi bi-dice-5-fill\" viewBox=\"0 0 16 16\">\r\n                                <path d=\"M3 0a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V3a3 3 0 0 0-3-3zm2.5 4a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m8 0a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0M12 13.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3M5.5 12a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0M8 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3\" />\r\n                            </svg>\r\n                        </div>\r\n                        <div className='create-section1 text-center d-flex align-items-center justify-content-center'>\r\n                            <div className=\"p-2\">\r\n                                <h3 className=\"text-white mb-3\">New Here?</h3>\r\n                                <p className=\"text-white mb-4\">\r\n                                    Don’t have an account yet? Create one now and join the conversation!\r\n                                </p>\r\n                                <Link\r\n                                    className=\"btn btn-outline-light btn-lg create-btn\"\r\n                                    href='/Auth/Signup' style={{ textDecoration: \"none\" }}\r\n                                >\r\n                                    Create New Account\r\n                                </Link>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </>\r\n    );\r\n}\r\n\r\nexport default Login;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,SAAS;IACL,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,UAAU;QAAI,UAAU;QAAI,OAAO;IAAM;IACpF,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,mGAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,gHAAA,CAAA,WAAQ,AAAD;IAE3C,MAAM,eAAe,CAAC;QAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY;YACR,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC5C;IACJ;IAEA,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QACX,YAAY;QAEZ,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,EAAE;YAC1C,SAAS;YACT,WAAW;YACX;QACJ;QAEA,MAAM,WAAW,YAAY;YACzB,YAAY,CAAC;gBACT,IAAI,QAAQ,IAAI;oBACZ,cAAc;oBACd,OAAO;gBACX;gBACA,OAAO,OAAO;YAClB;QACJ,GAAG;QAEH,IAAI;YACA,MAAM,WAAW,MAAM,6GAAA,CAAA,UAAK,CAAC,IAAI,CAAC,2CAA2C,UAAU;gBACnF,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,iBAAiB;YACrB;YAEA,eAAe,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,SAAS,IAAI;YAC3D,WAAW;YAEX,OAAO,OAAO,CAAC;QACnB,EAAE,OAAO,OAAO;YACZ,YAAY;YACZ,IAAI,MAAM,QAAQ,EAAE;gBAChB,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;oBAC/B,SAAS;gBACb,OAAO;oBACH,SAAS,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,IAAI;gBAC1C;YACJ,OAAO;gBACH,SAAS;YACb;QACJ,SAAU;YACN,WAAW;QACf;IACJ;IAEA,MAAM,WAAW;QACb,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;IACd;IACA,MAAM,YAAY;QACd,UAAU;QACV,YAAY;QACZ,WAAW;QACX,YAAY;QACZ,UAAU;IACd;IAEA,MAAM,YAAY,OAAO,IAAI,CAAC;IAE9B,oDAAoD;IACpD,MAAM,eAAe,UAAU,QAAQ,CAAC,SAAS,QAAQ;IAGzD,MAAM,cAAc;QAChB,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM;QAC/D,MAAM,cAAc,SAAS,CAAC,YAAY;QAC1C,iBAAiB;IACrB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,mGAAA,CAAA,YAAS,AAAD,EAAE;QACN,aAAa,OAAO,CAAC,SAAS;IAClC,GAAG;QAAC;KAAa;IAEjB,qBACI;;YAAG,WAAW,CAAC,uBACX,qKAAC;gBAAI,WAAU;0BACX,cAAA,qKAAC;oBAAI,SAAQ;8BACT,cAAA,qKAAC;wBAAE,MAAK;wBAAO,QAAO;wBAAO,eAAc;wBAAQ,gBAAe;wBAAQ,aAAY;;0CAElF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAqB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG/G,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAiB,eAAc;oCAAY,MAAK;oCAAS,QAAO;oCAAsB,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGhH,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAI,QAAO;oCAA6B,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAGxF,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAG3G,qKAAC;gCAAK,GAAE;;;;;;0CAER,qKAAC;gCAAK,GAAE;0CACJ,cAAA,qKAAC;oCAAQ,eAAc;oCAAS,QAAO;oCAA2C,KAAI;oCAAK,aAAY;;;;;;;;;;;0CAE3G,qKAAC;gCAAiB,eAAc;gCAAY,MAAK;gCAAY,QAAO;gCAAa,KAAI;gCAAK,aAAY;;;;;;;;;;;;;;;;;;;;;;0BAKlH,qKAAC;gBAAI,WAAU;;kCACX,qKAAC,qHAAA,CAAA,UAAI;kCACD,cAAA,qKAAC;sCAAM;;;;;;;;;;;kCAEX,qKAAC;wBAAI,WAAU;;0CAEX,qKAAC;gCAAI,WAAU;;oCACV,uBACG,qKAAC;wCAAI,WAAU;wCAAqB,MAAK;;0DACrC,qKAAC;0DAAO;;;;;;4CAAe;4CAAE;;;;;;;kDAIjC,qKAAC;wCAAI,WAAU;;0DACX,qKAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,qKAAC;gDAAK,UAAU;;kEACZ,qKAAC;wDAAI,WAAU;;0EACX,qKAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAAqB;;;;;;0EACzD,qKAAC;gEACG,MAAK;gEACL,WAAU;gEACV,IAAG;gEACH,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,QAAQ;gEACxB,UAAU;;;;;;;;;;;;kEAIlB,qKAAC;wDAAI,WAAU;;0EACX,qKAAC;gEAAM,SAAQ;gEAAW,WAAU;0EAAqB;;;;;;0EACzD,qKAAC;gEACG,MAAK;gEACL,WAAU;gEACV,IAAG;gEACH,MAAK;gEACL,aAAY;gEACZ,OAAO,SAAS,QAAQ;gEACxB,UAAU;;;;;;;;;;;;kEAGlB,qKAAC;;;;;kEAAK,qKAAC;;;;;kEACP,qKAAC;wDAAO,MAAK;wDAAS,WAAU;kEAAkC;;;;;;kEAGlE,qKAAC;;;;;kEAAK,qKAAC;;;;;kEACP,qKAAC,qHAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,WAAU;wDAAyC,OAAO;4DAAE,gBAAgB;wDAAO;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ5H,qKAAC;gCAAI,WAAU;gCAA0B,IAAG;;kDACxC,qKAAC;wCACG,WAAU;wCACV,OAAO;4CACH,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,aAAa,EAAE;4CAC7E,gBAAgB;4CAChB,kBAAkB;4CAClB,oBAAoB;4CACpB,qBAAqB;4CACrB,UAAU;4CACV,KAAK;4CACL,MAAM;4CACN,OAAO;4CACP,QAAQ;4CACR,QAAQ;4CACR,YAAY;wCAChB;;;;;;kDAGJ,qKAAC;wCAAI,WAAU;wCAAO,SAAS,IAAM,QAAQ,GAAG,CAAC,wBAAwB,UAAU;wCAAe,OAAO;4CAAE,QAAQ;4CAAG,UAAU;4CAAY,KAAK;4CAAQ,OAAO;wCAAO;kDACnK,cAAA,qKAAC;4CAAI,OAAM;4CAA6B,OAAM;4CAAK,QAAO;4CAAK,MAAK;4CAAe,WAAU;4CAAoB,SAAQ;sDACrH,cAAA,qKAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGhB,qKAAC;wCAAI,WAAU;kDACX,cAAA,qKAAC;4CAAI,WAAU;;8DACX,qKAAC;oDAAG,WAAU;8DAAkB;;;;;;8DAChC,qKAAC;oDAAE,WAAU;8DAAkB;;;;;;8DAG/B,qKAAC,qHAAA,CAAA,UAAI;oDACD,WAAU;oDACV,MAAK;oDAAe,OAAO;wDAAE,gBAAgB;oDAAO;8DACvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC;uCAEe", "debugId": null}}]}