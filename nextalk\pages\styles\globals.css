body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
      'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }

  /* Links */
a {
  text-decoration: none;
}


a:hover {
  text-decoration: underline;
}
  
::-webkit-scrollbar {
  display: none;
}
/* Typography */
h2,
h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
}


p {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
}
  code {
    font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
      monospace;
  }
  @media (max-width: 767.98px) {
  h2,
  h3 {
      font-size: 1.5rem;
      /* Smaller headings */
  }
  p {
    font-size: 0.9rem;
    /* Smaller text */
}
}


@media (min-width: 767.98px) {
body {
  height: 100vh;
}
}