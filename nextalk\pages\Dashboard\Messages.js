import { useState, useRef, useEffect } from "react";
import axios from '../../utils/axiosConfig';
import Image from "next/image";
import predefine from "../../public/Images/predefine.webp";
import DashboardLayout from '../Components/DashboardLayout';
import { useRouter } from 'next/router';
import { useTheme } from '../../context/ThemeContext';

export default function Messages() {
    const [users, setUsers] = useState([]);
    const [sessionUser, setSessionUser] = useState(null);
    const [following, setFollowing] = useState(new Set());
    const [accepted, setAccepted] = useState(new Set());
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedChat, setSelectedChat] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [displayCount, setDisplayCount] = useState(6);
    const [visibleUsers, setVisibleUsers] = useState([]);
    const [filteredFollowers, setFilteredFollowers] = useState([]);
    const [profile, setProfile] = useState({
        username: 'user123',
        name: 'John Doe',
        email: '<EMAIL>',
        bio: 'No bio yet.',
        avatar: predefine,
        posts: 15,
        followersCount: 250,
        followingCount: 180,
    });
    const { theme } = useTheme();
    const [showOnlineOnly, setShowOnlineOnly] = useState(false);
    const router = useRouter();

    // Chat functionality states
    const [chatMessages, setChatMessages] = useState({});
    const [newMessage, setNewMessage] = useState("");
    const messagesEndRef = useRef(null);
    const [sessionUserId, setSessionUserId] = useState(null);
    const [selectedUser, setSelectedUser] = useState(null);

    // Mock last message data and reactions
    const [lastMessages, setLastMessages] = useState({});
    const [reactions, setReactions] = useState({});

    // Local Storage utility functions
    const getChatHistory = (userId) => {
        try {
            if (!sessionUserId || !userId) return [];
            const chatKey = `chat_${sessionUserId}_${userId}`;
            const stored = localStorage.getItem(chatKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading chat history:', error);
            return [];
        }
    };

    const saveChatHistory = (userId, messages) => {
        try {
            if (!sessionUserId || !userId) return;
            const chatKey = `chat_${sessionUserId}_${userId}`;
            // Limit messages to last 1000 to prevent localStorage overflow
            const limitedMessages = messages.slice(-1000);
            localStorage.setItem(chatKey, JSON.stringify(limitedMessages));
        } catch (error) {
            console.error('Error saving chat history:', error);
            // If localStorage is full, try to clear old chats
            if (error.name === 'QuotaExceededError') {
                console.warn('localStorage quota exceeded, clearing old chats...');
                clearOldChats();
                // Try saving again
                try {
                    localStorage.setItem(chatKey, JSON.stringify(messages.slice(-500)));
                } catch (retryError) {
                    console.error('Failed to save even after cleanup:', retryError);
                }
            }
        }
    };

    const clearOldChats = () => {
        try {
            const keys = Object.keys(localStorage);
            const chatKeys = keys.filter(key => key.startsWith('chat_'));
            // Remove oldest chats (simple cleanup strategy)
            chatKeys.slice(0, Math.floor(chatKeys.length / 2)).forEach(key => {
                localStorage.removeItem(key);
            });
        } catch (error) {
            console.error('Error clearing old chats:', error);
        }
    };

    const getLastMessageForUser = (userId) => {
        const messages = getChatHistory(userId);
        if (messages.length === 0) return 'No messages yet';
        const lastMsg = messages[messages.length - 1];
        const preview = lastMsg.text.length > 30 ? lastMsg.text.substring(0, 30) + '...' : lastMsg.text;
        return lastMsg.sender === 'You' ? `You: ${preview}` : preview;
    };

    const formatMessageTime = (timestamp) => {
        const now = new Date();
        const msgTime = new Date(timestamp);
        const diffInHours = (now - msgTime) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return msgTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // Less than a week
            return msgTime.toLocaleDateString([], { weekday: 'short' });
        } else {
            return msgTime.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    };

    // Handle chat selection with URL update
    const handleChatSelect = (user) => {
        setSelectedChat(user._id);
        setSelectedUser(user);

        // Update URL with user ID
        router.push(`/Dashboard/Messages?userId=${user._id}`, undefined, { shallow: true });

        // Load chat history for this user
        const history = getChatHistory(user._id);
        setChatMessages(prev => ({
            ...prev,
            [user._id]: history
        }));
    };

    useEffect(() => {
        const fetchData = async () => {
            const storedUser = JSON.parse(sessionStorage.getItem('user'));
            const sessionId = storedUser?.user?.id;
            setSessionUserId(sessionId);

            if (!sessionId) {
                setError('No session found.');
                setLoading(false);
                return;
            }

            try {
                // Fetch all users
                const response = await axios.post(
                    'https://nextalk-u0y1.onrender.com/displayusersProfile',
                    {},
                    {
                        headers: { 'Content-Type': 'application/json' },
                        withCredentials: true,
                    }
                );

                const personally = response.data;
                const filteredUsers = personally.filter(user => user._id !== sessionId);
                const sessionUser = personally.find(user => user._id === sessionId);
                setSessionUser(sessionUser);

                // Fetch follow status
                const followRes = await fetch(`https://nextalk-u0y1.onrender.com/follow-status/${sessionId}`);
                const followData = await followRes.json();
                setFollowing(new Set(followData.following));
                setAccepted(new Set(followData.accepted));

                // Generate last messages from local storage
                const mockLastMessages = filteredUsers.reduce((acc, user) => ({
                    ...acc,
                    [user._id]: getLastMessageForUser(user._id),
                }), {});
                setLastMessages(mockLastMessages);

                setUsers(filteredUsers);
                setLoading(false);

                // Check if there's a userId in URL and auto-select that chat only if explicitly requested
                const { userId } = router.query;
                if (userId && router.asPath.includes('userId=')) {
                    const userToSelect = filteredUsers.find(u => u._id === userId);
                    if (userToSelect) {
                        handleChatSelect(userToSelect);
                    }
                }
            } catch (err) {
                console.error('❌ Error fetching data:', err);
                setError('Failed to load data.');
                setLoading(false);
            }
        };

        fetchData();
    }, [router.query]);

    // Auto-scroll to bottom when new messages are added
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [chatMessages, selectedChat]);

    // Filter users based on search query and online status
    const filteredUsers = users
        .filter(user => accepted.has(user._id))
        .filter(user =>
            user.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .filter(user =>
            showOnlineOnly ? user.isOnline : true
        );

    const getThemeStyles = () => {
        if (theme === 'dark') {
            return {
                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
                color: '#e2e8f0',
                cardBg: 'rgba(255, 255, 255, 0.1)',
                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',
                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',
                notificationBg: 'rgba(51, 65, 85, 0.9)',
            };
        }
        return {
            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',
            color: '#1e293b',
            cardBg: 'rgba(255, 255, 255, 0.8)',
            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)',
        };
    };

    const styles = getThemeStyles();

    const getThemeStyless = () => {
        if (theme === 'dark') {
            return {
                background: '#1e293b',
                color: '#e2e8f0',
                inputBg: '#334155',
                inputColor: '#e2e8f0',
                messageBgYou: '#3b82f6',
                messageBgFriend: '#4b5563'
            };
        }
        // Default to light styles for 'homeback' or any other theme
        return {
            background: '#ffffff',
            color: '#1e293b',
            inputBg: '#f1f5f9',
            inputColor: '#1e293b',
            messageBgYou: '#3b82f6',
            messageBgFriend: '#e5e7eb'
        };
    };

    const currentThemeStyles = getThemeStyless();

    const handleSendMessage = (e) => {
        e.preventDefault();
        if (newMessage.trim() && selectedChat) {
            const currentMessages = chatMessages[selectedChat] || [];
            const newMsg = {
                id: Date.now(), // Use timestamp as unique ID
                sender: "You",
                text: newMessage,
                timestamp: new Date().toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                })
            };

            const updatedMessages = [...currentMessages, newMsg];

            // Update chat messages state
            setChatMessages(prev => ({
                ...prev,
                [selectedChat]: updatedMessages
            }));

            // Save to local storage
            saveChatHistory(selectedChat, updatedMessages);

            // Update last messages for the chat list
            setLastMessages(prev => ({
                ...prev,
                [selectedChat]: newMsg.text.length > 30 ? newMsg.text.substring(0, 30) + '...' : newMsg.text
            }));

            setNewMessage("");
        }
    };

    useEffect(() => {
        if (!profile || !Array.isArray(profile.followers)) return;

        // Extract follower user IDs from the populated followers array
        const followersArray = profile.followers.map(f => f._id?.toString());

        // Filter only users who are followers
        const followedUsers = users.filter(user =>
            followersArray.includes(user._id?.toString())
        );

        // Filter for search
        const filtered = followedUsers.filter(user =>
            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.username.toLowerCase().includes(searchTerm.toLowerCase())
        );

        setFilteredFollowers(filtered); // useful for Load More check

        // Control visible users based on search or default count
        if (searchTerm.trim() === '') {
            setVisibleUsers(followedUsers.slice(0, displayCount));
        } else {
            setVisibleUsers(filtered.slice(0, displayCount));
        }

    }, [searchTerm, users, displayCount, profile]);


    const handleLoadMore = () => {
        const prevCount = displayCount;
        const newCount = prevCount + 6;
        setDisplayCount(newCount);

        // Scroll to the previous 6th user (after DOM update)
        setTimeout(() => {
            const userElems = document.querySelectorAll(".user-result");
            if (userElems[prevCount]) {
                userElems[prevCount].scrollIntoView({
                    behavior: "smooth",
                    block: "start"
                });
            }
        }, 100); // wait a moment for new DOM elements to render
    };
    return (
        <DashboardLayout>
            {loading ? (
                <div className="custom-loader-overlay">
                    <svg viewBox="0 0 100 100">
                        <g fill="none" stroke="#fff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="6">
                            {/* left line */}
                            <path d="M 21 40 V 59">
                                <animateTransform attributeName="transform" type="rotate" values="0 21 59; 180 21 59" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* right line */}
                            <path d="M 79 40 V 59">
                                <animateTransform attributeName="transform" type="rotate" values="0 79 59; -180 79 59" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* top line */}
                            <path d="M 50 21 V 40">
                                <animate attributeName="d" values="M 50 21 V 40; M 50 59 V 40" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* bottom line */}
                            <path d="M 50 60 V 79">
                                <animate attributeName="d" values="M 50 60 V 79; M 50 98 V 79" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* top box */}
                            <path d="M 50 21 L 79 40 L 50 60 L 21 40 Z">
                                <animate attributeName="stroke" values="rgba(255,255,255,1); rgba(100,100,100,0)" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* mid box */}
                            <path d="M 50 40 L 79 59 L 50 79 L 21 59 Z" />
                            {/* bottom box */}
                            <path d="M 50 59 L 79 78 L 50 98 L 21 78 Z">
                                <animate attributeName="stroke" values="rgba(100,100,100,0); rgba(255,255,255,1)" dur="2s" repeatCount="indefinite" />
                            </path>
                            <animateTransform attributeName="transform" type="translate" values="0 0; 0 -19" dur="2s" repeatCount="indefinite" />
                        </g>
                    </svg>
                </div>
            ) : error ? (
                <div className="error">{error}</div>
            ) : filteredUsers.length === 0 ? (
                <div className="no-friends">No friends to message.</div>
            ) : (
                <div className="chat-container-instagram" style={{
                    background: getThemeStyles().background,
                    color: getThemeStyles().color,
                    display: 'flex',
                    height: '85vh',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    boxShadow: theme === 'dark' ? '0 8px 32px rgba(0,0,0,0.3)' : '0 8px 32px rgba(0,0,0,0.1)',
                    border: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)'
                }}>
                    {/* Left Sidebar for Chat List */}
                    <div className="chat-list-instagram" style={{
                        width: selectedChat ? '350px' : '100%',
                        borderRight: selectedChat ? (theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)') : 'none',
                        background: theme === 'dark' ? 'rgba(255,255,255,0.02)' : 'rgba(0,0,0,0.02)',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'all 0.3s ease'
                    }}>
                        <div style={{
                            padding: '20px',
                            borderBottom: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
                            background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)'
                        }}>
                            <h2 style={{
                                margin: '0 0 15px 0',
                                fontSize: '1.5rem',
                                fontWeight: '600',
                                textAlign: 'center'
                            }}>Messages</h2>
                            <div style={{ position: 'relative' }}>
                                <input
                                    type="text"
                                    placeholder="Search messages..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    style={{
                                        width: '100%',
                                        padding: '12px 40px 12px 16px',
                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
                                        borderRadius: '25px',
                                        background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                                        color: getThemeStyles().color,
                                        outline: 'none',
                                        fontSize: '0.9rem',
                                        transition: 'all 0.3s ease'
                                    }}
                                    onFocus={(e) => {
                                        e.target.style.borderColor = '#3b82f6';
                                        e.target.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';
                                        e.target.style.boxShadow = 'none';
                                    }}
                                />
                                <i className="bi bi-search" style={{
                                    position: 'absolute',
                                    right: '15px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    opacity: '0.6',
                                    fontSize: '0.9rem'
                                }}></i>
                            </div>
                        </div>
                        <div style={{
                            flex: 1,
                            overflowY: 'auto',
                            padding: '10px 0'
                        }}>
                            {filteredUsers.map(user => (
                                <div
                                    key={user._id}
                                    onClick={() => handleChatSelect(user)}
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        padding: '12px 20px',
                                        cursor: 'pointer',
                                        transition: 'all 0.2s ease',
                                        background: selectedChat === user._id ?
                                            (theme === 'dark' ? 'rgba(59,130,246,0.2)' : 'rgba(59,130,246,0.1)') :
                                            'transparent',
                                        borderLeft: selectedChat === user._id ? '3px solid #3b82f6' : '3px solid transparent'
                                    }}
                                    onMouseEnter={(e) => {
                                        if (selectedChat !== user._id) {
                                            e.target.style.background = theme === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)';
                                        }
                                    }}
                                    onMouseLeave={(e) => {
                                        if (selectedChat !== user._id) {
                                            e.target.style.background = 'transparent';
                                        }
                                    }}
                                >
                                    <div style={{ position: 'relative', marginRight: '15px' }}>
                                        <Image
                                            src={user.image || predefine}
                                            alt={user.name}
                                            width={50}
                                            height={50}
                                            style={{
                                                borderRadius: '50%',
                                                objectFit: 'cover',
                                                border: selectedChat === user._id ? '2px solid #3b82f6' : '2px solid transparent'
                                            }}
                                        />
                                        <span style={{
                                            position: 'absolute',
                                            bottom: '2px',
                                            right: '2px',
                                            width: '12px',
                                            height: '12px',
                                            background: '#10b981',
                                            borderRadius: '50%',
                                            border: '2px solid white'
                                        }}></span>
                                    </div>
                                    <div style={{ flex: 1, minWidth: 0 }}>
                                        <div style={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                            marginBottom: '4px'
                                        }}>
                                            <span style={{
                                                fontWeight: selectedChat === user._id ? '600' : '500',
                                                fontSize: '1rem',
                                                color: selectedChat === user._id ? '#3b82f6' : getThemeStyles().color
                                            }}>{user.name}</span>
                                            <span style={{
                                                fontSize: '0.75rem',
                                                opacity: 0.7,
                                                color: getThemeStyles().color
                                            }}>
                                                {(() => {
                                                    const messages = getChatHistory(user._id);
                                                    if (messages.length > 0) {
                                                        return formatMessageTime(messages[messages.length - 1].timestamp);
                                                    }
                                                    return '';
                                                })()}
                                            </span>
                                        </div>
                                        <p style={{
                                            margin: 0,
                                            fontSize: '0.85rem',
                                            opacity: 0.8,
                                            whiteSpace: 'nowrap',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            color: getThemeStyles().color
                                        }}>{lastMessages[user._id] || 'Start a conversation'}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Right Panel for Chat or Instructions */}
                    {selectedChat && (
                        <div style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column',
                            background: theme === 'dark' ? 'rgba(255,255,255,0.01)' : 'rgba(0,0,0,0.01)'
                        }}>
                            {/* Chat Header */}
                            <div style={{
                                padding: '20px',
                                borderBottom: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
                                background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '15px'
                            }}>
                                <Image
                                    src={filteredUsers.find(u => u._id === selectedChat)?.image || predefine}
                                    alt={filteredUsers.find(u => u._id === selectedChat)?.name}
                                    width={45}
                                    height={45}
                                    style={{
                                        borderRadius: '50%',
                                        objectFit: 'cover',
                                        border: '2px solid #3b82f6'
                                    }}
                                />
                                <div>
                                    <h3 style={{
                                        margin: 0,
                                        fontSize: '1.2rem',
                                        fontWeight: '600',
                                        color: getThemeStyles().color
                                    }}>{filteredUsers.find(u => u._id === selectedChat)?.name}</h3>
                                    <p style={{
                                        margin: 0,
                                        fontSize: '0.85rem',
                                        opacity: 0.7,
                                        color: getThemeStyles().color
                                    }}>Active now</p>
                                </div>
                            </div>
                            {/* Chat Messages */}
                            <div style={{
                                flex: 1,
                                overflowY: 'auto',
                                padding: '20px',
                                background: theme === 'dark' ? 'rgba(255,255,255,0.01)' : 'rgba(0,0,0,0.01)'
                            }}>
                                {chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (
                                    chatMessages[selectedChat].map((msg) => (
                                        <div
                                            key={msg.id}
                                            style={{
                                                display: 'flex',
                                                justifyContent: msg.sender === "You" ? 'flex-end' : 'flex-start',
                                                marginBottom: '15px'
                                            }}
                                        >
                                            <div
                                                style={{
                                                    maxWidth: '70%',
                                                    padding: '12px 16px',
                                                    borderRadius: '18px',
                                                    background: msg.sender === "You"
                                                        ? 'linear-gradient(45deg, #3b82f6, #60a5fa)'
                                                        : (theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'),
                                                    color: msg.sender === "You" ? '#ffffff' : getThemeStyles().color,
                                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                                    animation: 'messageSlideIn 0.3s ease-out'
                                                }}
                                            >
                                                <div style={{ marginBottom: '4px', lineHeight: '1.4' }}>{msg.text}</div>
                                                <div style={{
                                                    fontSize: '0.75rem',
                                                    opacity: 0.7,
                                                    textAlign: 'right'
                                                }}>{msg.timestamp}</div>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <div style={{
                                        textAlign: 'center',
                                        padding: '60px 20px',
                                        color: getThemeStyles().color,
                                        opacity: 0.6
                                    }}>
                                        <i className="bi bi-chat-dots" style={{
                                            fontSize: '3rem',
                                            marginBottom: '15px',
                                            display: 'block'
                                        }}></i>
                                        <h4 style={{ marginBottom: '10px' }}>No messages yet</h4>
                                        <p>Start the conversation by sending a message!</p>
                                    </div>
                                )}
                                <div ref={messagesEndRef} />
                            </div>

                            {/* Chat Input */}
                            <div style={{
                                padding: '20px',
                                borderTop: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
                                background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.03)'
                            }}>
                                <form onSubmit={handleSendMessage} style={{ display: 'flex', gap: '10px' }}>
                                    <input
                                        type="text"
                                        placeholder="Type a message..."
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        style={{
                                            flex: 1,
                                            padding: '12px 16px',
                                            border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
                                            borderRadius: '25px',
                                            background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                                            color: getThemeStyles().color,
                                            outline: 'none',
                                            fontSize: '1rem',
                                            transition: 'all 0.3s ease'
                                        }}
                                        onFocus={(e) => {
                                            e.target.style.borderColor = '#3b82f6';
                                            e.target.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';
                                        }}
                                        onBlur={(e) => {
                                            e.target.style.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';
                                            e.target.style.boxShadow = 'none';
                                        }}
                                    />
                                    <button
                                        type="submit"
                                        style={{
                                            width: '50px',
                                            height: '50px',
                                            border: 'none',
                                            borderRadius: '50%',
                                            background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
                                            color: 'white',
                                            cursor: 'pointer',
                                            transition: 'all 0.3s ease',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center'
                                        }}
                                        onMouseEnter={(e) => {
                                            e.target.style.transform = 'scale(1.05)';
                                            e.target.style.boxShadow = '0 5px 15px rgba(59,130,246,0.4)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.target.style.transform = 'scale(1)';
                                            e.target.style.boxShadow = 'none';
                                        }}
                                    >
                                        <i className="bi bi-send-fill"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    )}

                    {/* Instructions when no chat is selected */}
                    {!selectedChat && (
                        <div style={{
                            flex: 1,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            flexDirection: 'column',
                            padding: '40px',
                            textAlign: 'center',
                            background: theme === 'dark' ? 'rgba(255,255,255,0.01)' : 'rgba(0,0,0,0.01)'
                        }}>
                            <div style={{
                                background: theme === 'dark' ? 'rgba(59,130,246,0.1)' : 'rgba(59,130,246,0.05)',
                                borderRadius: '50%',
                                width: '120px',
                                height: '120px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginBottom: '30px',
                                border: theme === 'dark' ? '2px solid rgba(59,130,246,0.3)' : '2px solid rgba(59,130,246,0.2)'
                            }}>
                                <i className="bi bi-chat-heart" style={{
                                    fontSize: '3.5rem',
                                    color: '#3b82f6'
                                }}></i>
                            </div>
                            <h2 style={{
                                margin: '0 0 15px 0',
                                fontSize: '2rem',
                                fontWeight: '600',
                                color: getThemeStyles().color,
                                background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
                                WebkitBackgroundClip: 'text',
                                WebkitTextFillColor: 'transparent',
                                backgroundClip: 'text'
                            }}>Start Chatting!</h2>
                            <p style={{
                                margin: '0 0 30px 0',
                                fontSize: '1.1rem',
                                opacity: 0.8,
                                color: getThemeStyles().color,
                                maxWidth: '400px',
                                lineHeight: '1.6'
                            }}>
                                Connect with your friends and start meaningful conversations.
                                Select a chat from the list or send a message to someone new.
                            </p>
                            <button
                                data-bs-toggle="modal"
                                data-bs-target="#usersModal"
                                style={{
                                    padding: '15px 30px',
                                    border: 'none',
                                    borderRadius: '25px',
                                    background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
                                    color: 'white',
                                    fontSize: '1.1rem',
                                    fontWeight: '600',
                                    cursor: 'pointer',
                                    transition: 'all 0.3s ease',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '10px',
                                    boxShadow: '0 4px 15px rgba(59,130,246,0.3)'
                                }}
                                onMouseEnter={(e) => {
                                    e.target.style.transform = 'translateY(-2px)';
                                    e.target.style.boxShadow = '0 8px 25px rgba(59,130,246,0.4)';
                                }}
                                onMouseLeave={(e) => {
                                    e.target.style.transform = 'translateY(0)';
                                    e.target.style.boxShadow = '0 4px 15px rgba(59,130,246,0.3)';
                                }}
                            >
                                <i className="bi bi-plus-circle"></i>
                                Start New Message
                            </button>
                        </div>
                    )}
                </div>
            )}
            {/* Users Modal */}
            <div className="modal fade" id="usersModal" tabIndex="-1" aria-labelledby="usersModalLabel" aria-hidden="true">
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content" style={{
                        background: theme === 'dark' ? '#1e293b' : '#ffffff',
                        color: getThemeStyles().color,
                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
                        borderRadius: '15px',
                        boxShadow: '0 20px 60px rgba(0,0,0,0.3)'
                    }}>
                        <div className="modal-header" style={{
                            borderBottom: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
                            padding: '20px 25px'
                        }}>
                            <h5 className="modal-title" id="usersModalLabel" style={{
                                fontSize: '1.5rem',
                                fontWeight: '600',
                                color: getThemeStyles().color,
                                display: 'flex',
                                alignItems: 'center',
                                gap: '10px'
                            }}>
                                <i className="bi bi-people-fill" style={{ color: '#3b82f6' }}></i>
                                Start New Conversation
                            </h5>
                            <button
                                type="button"
                                className="btn-close"
                                data-bs-dismiss="modal"
                                aria-label="Close"
                                style={{
                                    background: theme === 'dark' ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.8)',
                                    borderRadius: '50%',
                                    padding: '8px'
                                }}
                            ></button>
                        </div>
                        <div className="modal-body" style={{ padding: '25px' }}>
                            <div style={{ position: 'relative', marginBottom: '20px' }}>
                                <input
                                    type="search"
                                    placeholder="Search users..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    style={{
                                        width: '100%',
                                        padding: '12px 40px 12px 16px',
                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
                                        borderRadius: '25px',
                                        background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                                        color: getThemeStyles().color,
                                        outline: 'none',
                                        fontSize: '1rem',
                                        transition: 'all 0.3s ease'
                                    }}
                                    onFocus={(e) => {
                                        e.target.style.borderColor = '#3b82f6';
                                        e.target.style.boxShadow = '0 0 0 3px rgba(59,130,246,0.1)';
                                    }}
                                    onBlur={(e) => {
                                        e.target.style.borderColor = theme === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)';
                                        e.target.style.boxShadow = 'none';
                                    }}
                                />
                                <i className="bi bi-search" style={{
                                    position: 'absolute',
                                    right: '15px',
                                    top: '50%',
                                    transform: 'translateY(-50%)',
                                    opacity: '0.6',
                                    fontSize: '1rem'
                                }}></i>
                            </div>

                            <div style={{
                                maxHeight: '400px',
                                overflowY: 'auto',
                                padding: '10px 0'
                            }}>
                                {loading ? (
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '15px',
                                        padding: '15px 0'
                                    }}>
                                        <div style={{
                                            width: "50px",
                                            height: "50px",
                                            borderRadius: "50%",
                                            background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                                            animation: 'pulse 1.5s ease-in-out infinite'
                                        }}></div>
                                        <div>
                                            <div style={{
                                                width: "120px",
                                                height: "16px",
                                                borderRadius: "4px",
                                                marginBottom: "8px",
                                                background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                                                animation: 'pulse 1.5s ease-in-out infinite'
                                            }}></div>
                                            <div style={{
                                                width: "80px",
                                                height: "12px",
                                                borderRadius: "4px",
                                                background: theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                                                animation: 'pulse 1.5s ease-in-out infinite'
                                            }}></div>
                                        </div>
                                    </div>
                                ) : (
                                    <>
                                        {visibleUsers.length > 0 ? (
                                            visibleUsers.map(user => (
                                                <div
                                                    key={user._id}
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'space-between',
                                                        padding: '15px',
                                                        borderRadius: '12px',
                                                        marginBottom: '10px',
                                                        background: theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)',
                                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
                                                        transition: 'all 0.3s ease',
                                                        cursor: 'pointer'
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.currentTarget.style.background = theme === 'dark' ? 'rgba(255,255,255,0.08)' : 'rgba(0,0,0,0.05)';
                                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.currentTarget.style.background = theme === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)';
                                                        e.currentTarget.style.transform = 'translateY(0)';
                                                    }}
                                                >
                                                    <div style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        gap: '15px'
                                                    }}>
                                                        <Image
                                                            src={user.image || predefine}
                                                            alt={user.name}
                                                            width={50}
                                                            height={50}
                                                            style={{
                                                                borderRadius: '50%',
                                                                objectFit: 'cover',
                                                                border: '2px solid rgba(59,130,246,0.3)'
                                                            }}
                                                        />
                                                        <div>
                                                            <div style={{
                                                                fontWeight: '600',
                                                                fontSize: '1rem',
                                                                color: getThemeStyles().color,
                                                                marginBottom: '4px'
                                                            }}>{user.name}</div>
                                                            <div style={{
                                                                fontSize: '0.85rem',
                                                                opacity: 0.7,
                                                                color: getThemeStyles().color
                                                            }}>@{user.username}</div>
                                                        </div>
                                                    </div>
                                                    <button
                                                        onClick={() => {
                                                            handleChatSelect(user);
                                                            // Close modal
                                                            const modal = document.getElementById('usersModal');
                                                            const modalInstance = window.bootstrap?.Modal?.getInstance(modal);
                                                            if (modalInstance) {
                                                                modalInstance.hide();
                                                            }
                                                        }}
                                                        style={{
                                                            padding: '8px 16px',
                                                            border: 'none',
                                                            borderRadius: '20px',
                                                            background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
                                                            color: 'white',
                                                            fontSize: '0.9rem',
                                                            fontWeight: '500',
                                                            cursor: 'pointer',
                                                            transition: 'all 0.3s ease'
                                                        }}
                                                        onMouseEnter={(e) => {
                                                            e.target.style.transform = 'scale(1.05)';
                                                            e.target.style.boxShadow = '0 4px 12px rgba(59,130,246,0.4)';
                                                        }}
                                                        onMouseLeave={(e) => {
                                                            e.target.style.transform = 'scale(1)';
                                                            e.target.style.boxShadow = 'none';
                                                        }}
                                                    >
                                                        <i className="bi bi-chat-dots me-1"></i>
                                                        Message
                                                    </button>
                                                </div>
                                            ))
                                        ) : (
                                            <div style={{
                                                textAlign: 'center',
                                                padding: '60px 20px',
                                                color: getThemeStyles().color,
                                                opacity: 0.6
                                            }}>
                                                <i className="bi bi-person-x" style={{
                                                    fontSize: "3rem",
                                                    marginBottom: "15px",
                                                    display: "block"
                                                }}></i>
                                                <h5>No Users Found</h5>
                                                <p>Try adjusting your search terms</p>
                                            </div>
                                        )}

                                        {/* Load More Button */}
                                        {visibleUsers.length < filteredFollowers.length && (
                                            <div style={{ textAlign: 'center', marginTop: '20px' }}>
                                                <button
                                                    onClick={handleLoadMore}
                                                    style={{
                                                        padding: '10px 20px',
                                                        border: theme === 'dark' ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
                                                        borderRadius: '20px',
                                                        background: 'transparent',
                                                        color: getThemeStyles().color,
                                                        cursor: 'pointer',
                                                        transition: 'all 0.3s ease',
                                                        fontSize: '0.9rem'
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.background = theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.background = 'transparent';
                                                    }}
                                                >
                                                    Load More
                                                </button>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Add CSS animations */}
            <style jsx>{`
                @keyframes messageSlideIn {
                    from {
                        opacity: 0;
                        transform: translateY(10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                @keyframes pulse {
                    0%, 100% {
                        opacity: 1;
                    }
                    50% {
                        opacity: 0.5;
                    }
                }

                .chat-container-instagram {
                    transition: all 0.3s ease;
                }

                .chat-list-instagram {
                    transition: all 0.3s ease;
                }

                @media (max-width: 768px) {
                    .chat-container-instagram {
                        flex-direction: column;
                        height: 90vh;
                    }

                    .chat-list-instagram {
                        width: 100% !important;
                        max-height: 300px;
                    }
                }

                @media (max-width: 480px) {
                    .chat-container-instagram {
                        height: 95vh;
                        border-radius: 8px;
                    }

                    .chat-list-instagram {
                        max-height: 250px;
                    }
                }
            `}</style>
        </DashboardLayout>
    );
}