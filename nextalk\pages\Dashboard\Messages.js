import { useState, useRef, useEffect } from "react";
import axios from '../../utils/axiosConfig';
import Image from "next/image";
import predefine from "../../public/Images/predefine.webp";
import DashboardLayout from '../Components/DashboardLayout';
import { useRouter } from 'next/router';
import { useTheme } from '../../context/ThemeContext';
import styles from '../../styles/Chat.module.css';

export default function Messages() {
    const [users, setUsers] = useState([]);
    const [sessionUser, setSessionUser] = useState(null);
    const [following, setFollowing] = useState(new Set());
    const [accepted, setAccepted] = useState(new Set());
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedChat, setSelectedChat] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [displayCount, setDisplayCount] = useState(6);
    const [visibleUsers, setVisibleUsers] = useState([]);
    const [filteredFollowers, setFilteredFollowers] = useState([]);
    const [profile, setProfile] = useState({
        username: 'user123',
        name: 'John Doe',
        email: '<EMAIL>',
        bio: 'No bio yet.',
        avatar: predefine,
        posts: 15,
        followersCount: 250,
        followingCount: 180,
    });
    const { theme } = useTheme();
    const [showOnlineOnly, setShowOnlineOnly] = useState(false);
    const router = useRouter();

    // Chat functionality states
    const [chatMessages, setChatMessages] = useState({});
    const [newMessage, setNewMessage] = useState("");
    const messagesEndRef = useRef(null);
    const [sessionUserId, setSessionUserId] = useState(null);
    const [selectedUser, setSelectedUser] = useState(null);

    // Mock last message data and reactions
    const [lastMessages, setLastMessages] = useState({});
    const [reactions, setReactions] = useState({});

    // Local Storage utility functions
    const getChatHistory = (userId) => {
        try {
            if (!sessionUserId || !userId) return [];
            const chatKey = `chat_${sessionUserId}_${userId}`;
            const stored = localStorage.getItem(chatKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Error loading chat history:', error);
            return [];
        }
    };

    const saveChatHistory = (userId, messages) => {
        try {
            if (!sessionUserId || !userId) return;
            const chatKey = `chat_${sessionUserId}_${userId}`;
            // Limit messages to last 1000 to prevent localStorage overflow
            const limitedMessages = messages.slice(-1000);
            localStorage.setItem(chatKey, JSON.stringify(limitedMessages));
        } catch (error) {
            console.error('Error saving chat history:', error);
            // If localStorage is full, try to clear old chats
            if (error.name === 'QuotaExceededError') {
                console.warn('localStorage quota exceeded, clearing old chats...');
                clearOldChats();
                // Try saving again
                try {
                    localStorage.setItem(chatKey, JSON.stringify(messages.slice(-500)));
                } catch (retryError) {
                    console.error('Failed to save even after cleanup:', retryError);
                }
            }
        }
    };

    const clearOldChats = () => {
        try {
            const keys = Object.keys(localStorage);
            const chatKeys = keys.filter(key => key.startsWith('chat_'));
            // Remove oldest chats (simple cleanup strategy)
            chatKeys.slice(0, Math.floor(chatKeys.length / 2)).forEach(key => {
                localStorage.removeItem(key);
            });
        } catch (error) {
            console.error('Error clearing old chats:', error);
        }
    };

    const getLastMessageForUser = (userId) => {
        const messages = getChatHistory(userId);
        if (messages.length === 0) return 'No messages yet';
        const lastMsg = messages[messages.length - 1];
        const preview = lastMsg.text.length > 30 ? lastMsg.text.substring(0, 30) + '...' : lastMsg.text;
        return lastMsg.sender === 'You' ? `You: ${preview}` : preview;
    };

    const formatMessageTime = (timestamp) => {
        const now = new Date();
        const msgTime = new Date(timestamp);
        const diffInHours = (now - msgTime) / (1000 * 60 * 60);

        if (diffInHours < 24) {
            return msgTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        } else if (diffInHours < 168) { // Less than a week
            return msgTime.toLocaleDateString([], { weekday: 'short' });
        } else {
            return msgTime.toLocaleDateString([], { month: 'short', day: 'numeric' });
        }
    };

    // Handle chat selection with URL update
    const handleChatSelect = (user) => {
        setSelectedChat(user._id);
        setSelectedUser(user);

        // Update URL with user ID
        router.push(`/Dashboard/Messages?userId=${user._id}`, undefined, { shallow: true });

        // Load chat history for this user
        const history = getChatHistory(user._id);
        setChatMessages(prev => ({
            ...prev,
            [user._id]: history
        }));
    };

    useEffect(() => {
        const fetchData = async () => {
            const storedUser = JSON.parse(sessionStorage.getItem('user'));
            const sessionId = storedUser?.user?.id;
            setSessionUserId(sessionId);

            if (!sessionId) {
                setError('No session found.');
                setLoading(false);
                return;
            }

            try {
                // Fetch all users
                const response = await axios.post(
                    'https://nextalk-u0y1.onrender.com/displayusersProfile',
                    {},
                    {
                        headers: { 'Content-Type': 'application/json' },
                        withCredentials: true,
                    }
                );

                const personally = response.data;
                const filteredUsers = personally.filter(user => user._id !== sessionId);
                const sessionUser = personally.find(user => user._id === sessionId);
                setSessionUser(sessionUser);

                // Fetch follow status
                const followRes = await fetch(`https://nextalk-u0y1.onrender.com/follow-status/${sessionId}`);
                const followData = await followRes.json();
                setFollowing(new Set(followData.following));
                setAccepted(new Set(followData.accepted));

                // Generate last messages from local storage
                const mockLastMessages = filteredUsers.reduce((acc, user) => ({
                    ...acc,
                    [user._id]: getLastMessageForUser(user._id),
                }), {});
                setLastMessages(mockLastMessages);

                setUsers(filteredUsers);
                setLoading(false);

                // Check if there's a userId in URL and auto-select that chat
                const { userId } = router.query;
                if (userId) {
                    const userToSelect = filteredUsers.find(u => u._id === userId);
                    if (userToSelect) {
                        handleChatSelect(userToSelect);
                    }
                }
            } catch (err) {
                console.error('❌ Error fetching data:', err);
                setError('Failed to load data.');
                setLoading(false);
            }
        };

        fetchData();
    }, [router.query]);

    // Auto-scroll to bottom when new messages are added
    useEffect(() => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    }, [chatMessages, selectedChat]);

    // Filter users based on search query and online status
    const filteredUsers = users
        .filter(user => accepted.has(user._id))
        .filter(user =>
            user.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
        .filter(user =>
            showOnlineOnly ? user.isOnline : true
        );

    const getThemeStyles = () => {
        if (theme === 'dark') {
            return {
                background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)',
                color: '#e2e8f0',
                cardBg: 'rgba(255, 255, 255, 0.1)',
                buttonGradient: 'linear-gradient(45deg, #ff6f61, #ffcc00)',
                buttonHover: 'linear-gradient(45deg, #ff4b3a, #ffb800)',
                notificationBg: 'rgba(51, 65, 85, 0.9)',
            };
        }
        return {
            background: 'linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(244, 244, 244) 100%)',
            color: '#1e293b',
            cardBg: 'rgba(255, 255, 255, 0.8)',
            buttonGradient: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
            buttonHover: 'linear-gradient(45deg, #2563eb, #3b82f6)',
        };
    };

    const styles = getThemeStyles();

    const getThemeStyless = () => {
        if (theme === 'dark') {
            return {
                background: '#1e293b',
                color: '#e2e8f0',
                inputBg: '#334155',
                inputColor: '#e2e8f0',
                messageBgYou: '#3b82f6',
                messageBgFriend: '#4b5563'
            };
        }
        // Default to light styles for 'homeback' or any other theme
        return {
            background: '#ffffff',
            color: '#1e293b',
            inputBg: '#f1f5f9',
            inputColor: '#1e293b',
            messageBgYou: '#3b82f6',
            messageBgFriend: '#e5e7eb'
        };
    };

    const currentThemeStyles = getThemeStyless();

    const handleSendMessage = (e) => {
        e.preventDefault();
        if (newMessage.trim() && selectedChat) {
            const currentMessages = chatMessages[selectedChat] || [];
            const newMsg = {
                id: Date.now(), // Use timestamp as unique ID
                sender: "You",
                text: newMessage,
                timestamp: new Date().toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                })
            };

            const updatedMessages = [...currentMessages, newMsg];

            // Update chat messages state
            setChatMessages(prev => ({
                ...prev,
                [selectedChat]: updatedMessages
            }));

            // Save to local storage
            saveChatHistory(selectedChat, updatedMessages);

            // Update last messages for the chat list
            setLastMessages(prev => ({
                ...prev,
                [selectedChat]: newMsg.text.length > 30 ? newMsg.text.substring(0, 30) + '...' : newMsg.text
            }));

            setNewMessage("");
        }
    };

    useEffect(() => {
        if (!profile || !Array.isArray(profile.followers)) return;

        // Extract follower user IDs from the populated followers array
        const followersArray = profile.followers.map(f => f._id?.toString());

        // Filter only users who are followers
        const followedUsers = users.filter(user =>
            followersArray.includes(user._id?.toString())
        );

        // Filter for search
        const filtered = followedUsers.filter(user =>
            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.username.toLowerCase().includes(searchTerm.toLowerCase())
        );

        setFilteredFollowers(filtered); // useful for Load More check

        // Control visible users based on search or default count
        if (searchTerm.trim() === '') {
            setVisibleUsers(followedUsers.slice(0, displayCount));
        } else {
            setVisibleUsers(filtered.slice(0, displayCount));
        }

    }, [searchTerm, users, displayCount, profile]);


    const handleLoadMore = () => {
        const prevCount = displayCount;
        const newCount = prevCount + 6;
        setDisplayCount(newCount);

        // Scroll to the previous 6th user (after DOM update)
        setTimeout(() => {
            const userElems = document.querySelectorAll(".user-result");
            if (userElems[prevCount]) {
                userElems[prevCount].scrollIntoView({
                    behavior: "smooth",
                    block: "start"
                });
            }
        }, 100); // wait a moment for new DOM elements to render
    };
    return (
        <DashboardLayout>
            {loading ? (
                <div className="custom-loader-overlay">
                    <svg viewBox="0 0 100 100">
                        <g fill="none" stroke="#fff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="6">
                            {/* left line */}
                            <path d="M 21 40 V 59">
                                <animateTransform attributeName="transform" type="rotate" values="0 21 59; 180 21 59" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* right line */}
                            <path d="M 79 40 V 59">
                                <animateTransform attributeName="transform" type="rotate" values="0 79 59; -180 79 59" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* top line */}
                            <path d="M 50 21 V 40">
                                <animate attributeName="d" values="M 50 21 V 40; M 50 59 V 40" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* bottom line */}
                            <path d="M 50 60 V 79">
                                <animate attributeName="d" values="M 50 60 V 79; M 50 98 V 79" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* top box */}
                            <path d="M 50 21 L 79 40 L 50 60 L 21 40 Z">
                                <animate attributeName="stroke" values="rgba(255,255,255,1); rgba(100,100,100,0)" dur="2s" repeatCount="indefinite" />
                            </path>
                            {/* mid box */}
                            <path d="M 50 40 L 79 59 L 50 79 L 21 59 Z" />
                            {/* bottom box */}
                            <path d="M 50 59 L 79 78 L 50 98 L 21 78 Z">
                                <animate attributeName="stroke" values="rgba(100,100,100,0); rgba(255,255,255,1)" dur="2s" repeatCount="indefinite" />
                            </path>
                            <animateTransform attributeName="transform" type="translate" values="0 0; 0 -19" dur="2s" repeatCount="indefinite" />
                        </g>
                    </svg>
                </div>
            ) : error ? (
                <div className="error">{error}</div>
            ) : filteredUsers.length === 0 ? (
                <div className="no-friends">No friends to message.</div>
            ) : (
                <div className={styles.chatContainer} style={{ background: getThemeStyles().background, color: getThemeStyles().color }}>
                    {/* Left Sidebar for Chat List */}
                    <div className={styles.chatList}>
                        <h2 className={styles.chatTitle}>Chats</h2>
                        <div style={{ padding: '0 20px 15px 20px' }}>
                            <input
                                type="text"
                                placeholder="Search chats..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '10px 15px',
                                    border: '1px solid rgba(255, 255, 255, 0.2)',
                                    borderRadius: '20px',
                                    background: 'rgba(255, 255, 255, 0.1)',
                                    color: getThemeStyles().color,
                                    outline: 'none',
                                    fontSize: '0.9rem'
                                }}
                            />
                        </div>
                        {filteredUsers.map(user => (
                            <div
                                key={user._id}
                                className={`${styles.chatItem} ${selectedChat === user._id ? styles.active : ''}`}
                                onClick={() => handleChatSelect(user)}
                            >
                                <div className={styles.avatarContainer}>
                                    <Image
                                        src={user.image || predefine}
                                        alt={user.name}
                                        width={40}
                                        height={40}
                                        className={styles.avatar}
                                    />
                                    <span className={styles.statusIndicator}></span>
                                </div>
                                <div className={styles.chatDetails}>
                                    <div className={styles.chatHeader}>
                                        <span className={styles.userName}>{user.name}</span>
                                        <span className={styles.timestamp}>
                                            {(() => {
                                                const messages = getChatHistory(user._id);
                                                if (messages.length > 0) {
                                                    return formatMessageTime(messages[messages.length - 1].timestamp);
                                                }
                                                return 'Now';
                                            })()}
                                        </span>
                                    </div>
                                    <p className={styles.lastMessage}>{lastMessages[user._id] || 'No messages yet'}</p>

                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Right Panel for Chat or Instructions */}
                    <div className={styles.chatPanel}>
                        {selectedChat ? (
                            <div className={styles.chatWindow}>
                                <div className="d-flex gap-5" style={{ alignItems: "center" }}>
                                    <div>
                                        <Image
                                            src={filteredUsers.find(u => u._id === selectedChat)?.image || predefine}
                                            alt={filteredUsers.find(u => u._id === selectedChat)?.image}
                                            width={100}
                                            height={100}
                                            className="avatar"
                                        /></div>
                                    <div>
                                        <h3 className="chat-header">Chat with {filteredUsers.find(u => u._id === selectedChat)?.name}</h3>
                                        <p className="chat-content">This is the chat window. Start messaging here!</p>
                                    </div>
                                </div>
                                <div
                                    className=""
                                    style={{
                                        background: currentThemeStyles.background,
                                        color: currentThemeStyles.color
                                    }}
                                >
                                    <div className={styles.chatMessages}>
                                        {chatMessages[selectedChat] && chatMessages[selectedChat].length > 0 ? (
                                            chatMessages[selectedChat].map((msg) => (
                                                <div
                                                    key={msg.id}
                                                    className={`${styles.message} ${msg.sender === "You" ? styles.messageYou : styles.messageFriend}`}
                                                >
                                                    <div
                                                        className={styles.messageBubble}
                                                        style={{
                                                            background: msg.sender === "You"
                                                                ? currentThemeStyles.messageBgYou
                                                                : currentThemeStyles.messageBgFriend,
                                                            color: msg.sender === "You" ? '#ffffff' : currentThemeStyles.color
                                                        }}
                                                    >
                                                        <span className={styles.messageText}>{msg.text}</span>
                                                        <span className={styles.messageTimestamp}>{msg.timestamp}</span>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className={styles.noMessages} style={{ color: currentThemeStyles.color }}>
                                                <i className="bi bi-chat-dots"></i>
                                                <h4>No messages yet</h4>
                                                <p>Start the conversation by sending a message!</p>
                                            </div>
                                        )}
                                        <div ref={messagesEndRef} />
                                    </div>
                                    <form className={styles.chatInputForm} onSubmit={handleSendMessage}>
                                        <input
                                            type="text"
                                            className={styles.chatInput}
                                            placeholder="Type a message..."
                                            value={newMessage}
                                            onChange={(e) => setNewMessage(e.target.value)}
                                            style={{
                                                background: currentThemeStyles.inputBg,
                                                color: currentThemeStyles.inputColor
                                            }}
                                        />
                                        <button type="submit" className={styles.chatSendBtn}>
                                            <i className="bi bi-send-fill"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        ) : (
                            <div className={styles.instructions}>
                                <div className="text-center">
                                    <div className={styles.iconContainer}>
                                        <span className={styles.chatIcon}><i className="bi bi-chat-right-text"></i></span>
                                    </div>
                                    <h3 className={styles.instructionTitle}>Your messages</h3>
                                    <p className={styles.instructionText}>Send a message to start a chat.</p>
                                    <button className={styles.sendButton} data-bs-toggle="modal" data-bs-target="#followers">
                                        {profile.followersCount || "0"} Send message
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
            <div className="modal" id="followers">
                <div className="modal-dialog">
                    <div className="modal-content">
                        <div className='d-flex justify-content-between'>
                            <div>
                                <h5>Followers</h5>
                            </div>
                            <div>
                                <button type="button" className="btn-close bg-primary" data-bs-dismiss="modal"></button>
                            </div>
                        </div><hr />
                        <div className="">
                            <div>
                                <input
                                    type="search"
                                    name="search"
                                    id="search"
                                    className="form-control mb-3"
                                    placeholder="Search users..."
                                    value={searchTerm}
                                    style={{
                                        backgroundColor: "white",
                                        transition: "background 0.3s",
                                        gap: "10px",
                                        border: "1px solid #333"
                                    }}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    onMouseEnter={e => e.currentTarget.style.backgroundColor = "white"}
                                    onMouseLeave={e => e.currentTarget.style.backgroundColor = "whitesmock"}
                                />

                                {
                                    loading ? (
                                        <div className='d-flex gap-4'>
                                            <div
                                                className="skeleton"
                                                style={{
                                                    width: "45px",
                                                    height: "45px",
                                                    borderRadius: "50%",
                                                }}
                                            ></div>
                                            <div>
                                                <div
                                                    className="skeleton"
                                                    style={{
                                                        width: "120px",
                                                        height: "16px",
                                                        borderRadius: "4px",
                                                        marginBottom: "8px",
                                                    }}
                                                ></div>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            {visibleUsers.length > 0 ? (
                                                visibleUsers.map(user => (
                                                    <div key={user._id} className='d-flex align-items-center mb-2 p-2 rounded user-result' style={{ justifyContent: "space-between" }}>
                                                        <div
                                                            className="d-flex gap-4 align-items-center"
                                                            style={{
                                                                cursor: "pointer",
                                                            }}
                                                        >
                                                            <Image
                                                                src={user.image || predefine}
                                                                alt={user.name}
                                                                width={60}
                                                                height={60}
                                                                className="rounded-circle"
                                                                style={{ objectFit: "cover" }}
                                                            />
                                                            <div>
                                                                <strong>{user.username}</strong><br />
                                                                <span>{user.name}</span>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <button
                                                                className='btn btn-primary btn-sm'
                                                                onClick={() => {
                                                                    handleChatSelect(user);
                                                                    // Close modal
                                                                    const modal = document.getElementById('followers');
                                                                    const modalInstance = window.bootstrap?.Modal?.getInstance(modal);
                                                                    if (modalInstance) {
                                                                        modalInstance.hide();
                                                                    }
                                                                }}
                                                                data-bs-dismiss="modal"
                                                            >
                                                                Message
                                                            </button>
                                                        </div>
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="no-followers-container text-center mt-5">
                                                    <div className="icon-wrapper mb-3">
                                                        <i className="bi bi-person-x" style={{ fontSize: "3rem", color: "#6c757d" }}></i>
                                                    </div>
                                                    <h5 style={{ color: "#6c757d" }}>No Followers Found</h5>
                                                </div>

                                            )}
                                            {/* Show "Load More" button only if there are more followed users to show */}
                                            {visibleUsers.length < filteredFollowers.length && (
                                                searchTerm.trim() === ''
                                                    ? (profile.followers.length || 0)
                                                    : users.filter(user =>
                                                        profile.followers.includes(user._id) &&
                                                        (user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                            user.username.toLowerCase().includes(searchTerm.toLowerCase()))
                                                    ).length
                                            ) && (
                                                    <div className="text-center mt-3">
                                                        <button className="btn w-100 btn-outline-primary" onClick={handleLoadMore}>
                                                            Load More
                                                        </button>
                                                    </div>
                                                )}


                                        </>


                                    )
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    );
}